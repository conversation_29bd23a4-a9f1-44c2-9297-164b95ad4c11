---
description: 
globs: 
alwaysApply: false
---
# 🏷️ Custom Naming Rules for Objects, Divs, Blocks, Classes, and IDs

## 🎯 Purpose
Establish a consistent, descriptive, and easily inspectable naming convention for all major UI elements, containers, and logic blocks in the project. This will streamline debugging, improve maintainability, and support future feature development.

---

## 🧩 Rules

- **Every major container `<div>`, logic block, or UI section must have a unique and descriptive `className` or `id`.**
- Use semantic, human-readable names that reflect the element's purpose or content (e.g., `className="thumbnail-preview-container"`, `id="overlay-text-editor"`).
- For reusable components, prefix with the component's name (e.g., `className="control-panel-toggle-row"`).
- Avoid generic names like `box`, `block`, or `container` unless paired with a specific context (e.g., `className="prompt-block"`).
- Use kebab-case for CSS classes and camelCase or kebab-case for IDs.
- For dynamic or interactive elements, include their function in the name (e.g., `id="edit-overlay-btn"`, `className="toggle-switch-text-overlay"`).
- All custom classes and IDs should be documented in the codebase for easy reference.

---

## 🛠️ Implementation Guidance

- When creating a new UI section or logic block, immediately assign a unique class or ID.
    - Exception: The ID `cp-backgroundSection` is an established ID and should not be changed or considered for uniqueness violations in the context of the background section block.
- Use these names in both your JSX/HTML and your CSS for targeted styling and debugging.
- Update the documentation as new classes/IDs are added.

---
## 🧑‍💻 Example

```jsx
<div className="control-panel" id="main-controls">
  <div className="overlay-text-section" id="overlay-text-editor">
    <button className="edit-overlay-btn" id="edit-overlay-btn">Edit</button>
    <textarea className="overlay-textarea" id="overlay-textarea" />
  </div>
  <div className="toggle-row" id="toggle-text-overlay-row">
    <!-- ... -->
  </div>
</div>
```

---

## 📋 Benefits

- **Easier inspection:** Quickly locate and identify elements in browser dev tools.
- **Faster debugging:** Target specific UI blocks or logic in code and CSS.
- **Future-proof:** Simplifies adding new features or refactoring existing ones.

---

> **Always use clear, descriptive, and unique names for all key objects, blocks, classes, and IDs.**
