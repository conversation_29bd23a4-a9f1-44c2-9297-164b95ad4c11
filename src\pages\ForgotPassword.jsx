import React, { useState, useRef } from 'react'
import { authAPI } from '../utils/supabase.mjs'
import { AuthThemeToggle } from '../components/ui/AuthThemeToggle.jsx'

/**
 * Forgot Password Screen Component with Unified Glass Design
 * Matches WelcomeGlass design structure and theme system
 * 
 * @param {Object} props
 * @param {Function} props.onBackToLogin - Callback to navigate back to login
 * @param {Function} props.onNavigateToSignUp - Callback to navigate to sign up
 * @param {boolean} props.isLightTheme - Current theme state
 * @param {Function} props.onToggleTheme - Theme toggle handler
 */
const ForgotPassword = ({ onBackToLogin, onNavigateToSignUp, isLightTheme, onToggleTheme }) => {
    // State for form inputs
    const [formData, setFormData] = useState({
        email: ''
    });
    
    // State for validation errors
    const [errors, setErrors] = useState({
        email: '',
        auth: ''
    });

    // Touched fields tracking for validation
    const [touched, setTouched] = useState({
        email: false
    });
    
    // Refs for form fields
    const inputRefs = {
        email: useRef(null)
    };
    
    // State for loading and success
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [resetEmailSent, setResetEmailSent] = useState(false);

    // Validation functions
    const validateEmail = (emailInput) => {
        const email = emailInput ? emailInput.trim() : '';
        if (!email) return "Email is required";
        const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i;
        if (!emailRegex.test(email)) return "Invalid email address";
        return "";
    };
    
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        
        setFormData(prev => ({ ...prev, [name]: value }));
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage, auth: '' }));
    };
    
    const handleBlur = (e) => {
        const { name, value } = e.target;
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage }));
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        const fieldsToTouch = { email: true };
        setTouched(prev => ({ ...prev, ...fieldsToTouch }));

        const currentErrors = {
            email: validateEmail(formData.email)
        };
        setErrors(prev => ({ ...prev, ...currentErrors, auth: '' }));

        if (currentErrors.email) {
            if (inputRefs.email?.current) {
                inputRefs.email.current.focus();
            }
            return;
        }
        
        setIsSubmitting(true);
        
        try {
            // Send password reset email with Supabase
            const result = await authAPI.sendPasswordResetEmail(formData.email);
            
            if (result.success) {
                setResetEmailSent(true);
            } else {
                // Password reset failed
                setErrors(prev => ({ 
                    ...prev, 
                    auth: result.error || 'Failed to send password reset email. Please try again.' 
                }));
            }
        } catch (error) {
            console.error('Password reset error:', error);
            setErrors(prev => ({ 
                ...prev, 
                auth: 'An unexpected error occurred. Please try again.' 
            }));
        } finally {
            setIsSubmitting(false);
        }
    };

    // If reset email was sent successfully, show success message
    if (resetEmailSent) {
        return (
            <div className="auth-glass-container" id="forgot-password-success-container">
                {/* Theme Toggle Button */}
                <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
                
                {/* Centered Glass Card */}
                <div className="flex items-center justify-center min-h-screen p-4">
                    <div className="auth-glass-card w-full max-w-md p-8" id="forgot-password-success-card">
                        {/* Logo */}
                        <img 
                            src="/assets/main-logo.svg" 
                            alt="Thumbspark Logo" 
                            className="auth-glass-logo" 
                            draggable="false" 
                        />

                        {/* Success Icon */}
                        <div className="mb-6 flex justify-center">
                            <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center">
                                <span className="iconify text-green-400" data-icon="solar:letter-bold" style={{ fontSize: '40px' }}></span>
                            </div>
                        </div>

                        {/* Title and Message */}
                        <h1 className="auth-glass-title">Email Sent!</h1>
                        <p className="auth-glass-subtitle">Check your inbox for the reset link</p>

                        <div className="mb-8">
                            <p className="text-gray-400 mb-4 text-center">
                                We've sent a password reset link to:
                            </p>
                            <p className="text-white font-medium mb-6 text-center">
                                {formData.email}
                            </p>
                            <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4 mb-6">
                                <p className="text-blue-300 text-sm mb-2">
                                    <strong>Next steps:</strong>
                                </p>
                                <ol className="text-gray-400 text-sm text-left list-decimal list-inside space-y-1">
                                    <li>Check your email inbox (and spam folder)</li>
                                    <li>Click the "Reset Password" link in the email</li>
                                    <li>Follow the instructions to create a new password</li>
                                </ol>
                            </div>
                            <p className="text-gray-500 text-xs text-center">
                                Didn't receive the email? Check your spam folder or try again.
                            </p>
                        </div>

                        {/* Navigation Links */}
                        <div className="text-center space-y-4">
                            <button 
                                type="button"
                                onClick={onBackToLogin}
                                className="auth-glass-cta-btn w-full"
                            >
                                Back to Sign In
                            </button>
                            
                            <button 
                                type="button"
                                onClick={() => setResetEmailSent(false)}
                                className="auth-glass-link"
                            >
                                Try a different email
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="auth-glass-container" id="forgot-password-container">
            {/* Theme Toggle Button */}
            <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
            
            {/* Centered Glass Card */}
            <div className="flex items-center justify-center min-h-screen p-4">
                <div className="auth-glass-card w-full max-w-md p-8" id="forgot-password-card">
                    {/* Logo */}
                    <img 
                        src="/assets/main-logo.svg" 
                        alt="Thumbspark Logo" 
                        className="auth-glass-logo" 
                        draggable="false" 
                    />

                    {/* Title and Subtitle */}
                    <h1 className="auth-glass-title">Reset Password</h1>
                    <p className="auth-glass-subtitle">Enter your email to receive a reset link</p>

                    {/* Global Error Message */}
                    {errors.auth && (
                        <div className="auth-glass-global-error" id="forgot-password-global-error">
                            <div className="auth-glass-global-error-text">
                                <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                {errors.auth}
                            </div>
                        </div>
                    )}

                    {/* Reset Password Form */}
                    <form onSubmit={handleSubmit} className="space-y-4" id="forgot-password-form">
                        {/* Email Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="email" className="auth-glass-label">
                                Email Address
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:letter-linear"></span>
                                <input 
                                    type="email" 
                                    id="forgot-password-email-input" 
                                    name="email" 
                                    value={formData.email} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.email} 
                                    required
                                    className={`auth-glass-input ${errors.email && touched.email ? 'error' : ''}`}
                                    placeholder="Enter your email address"
                                />
                            </div>
                            {errors.email && touched.email && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.email}
                                </div>
                            )}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={isSubmitting || (errors.email && touched.email)}
                            className="auth-glass-cta-btn"
                            id="forgot-password-submit-btn"
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Sending Reset Link...</span>
                                </>
                            ) : (
                                'Send Reset Link'
                            )}
                        </button>

                        {/* Additional Links */}
                        <div className="text-center space-y-3">
                            <button 
                                type="button"
                                onClick={onBackToLogin}
                                className="auth-glass-link"
                            >
                                ← Back to Sign In
                            </button>
                            
                            <div>
                                <span className="text-gray-400 text-sm">
                                    Don't have an account?{' '}
                                    <button 
                                        type="button"
                                        onClick={onNavigateToSignUp}
                                        className="auth-glass-link"
                                    >
                                        Sign up here
                                    </button>
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword; 