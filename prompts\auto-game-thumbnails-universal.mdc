---
description:
globs:
alwaysApply: false
---
auto-game-thumbnails-universal
---
ruleType: always
---

# 🎮 Universal Auto-Game Thumbnail Enhancement

## Applies To
- /src/utils/promptFormatter.js
- /src/utils/promptBuilder.ts
- /src/hooks/usePromptEnhancer.ts
- /src/templates/gaming/

---

## Trigger Conditions
- Prompt mentions FPS/Battle Royale games (Fortnite, Warzone, Call of Duty, Valorant, PUBG, Apex, CS2, etc.)
- OR includes comparison/versus keywords (`vs`, `1v1`, `Noob vs Pro`, `Showdown`)

---

## Behavior
1. Always apply cinematic, game-authentic thumbnail logic (split-screen, in-game characters, dramatic lighting, brand logos, etc.) regardless of **text overlay** toggle.
2. If text overlay is **ON** → inject bold, uppercase, glowing text overlay that matches the topic.
3. If text overlay is **OFF** → explicitly state: *"Do NOT include any text overlay,"* but keep every other cinematic element.
4. Insert official game logos if available when `vs`/comparison is detected.
5. Works for all video topics that meet the trigger conditions.

---

## Example

**User Prompt:** `PUBG VS Warzone`

| Text Overlay | Result |
|--------------|--------|
| **ON** | Cinematic split-screen, PUBG & Warzone soldiers, dramatic battlefield, glowing "PUBG VS WARZONE" text, brand logos. |
| **OFF** | Cinematic split-screen, PUBG & Warzone soldiers, dramatic battlefield, brand logos, **no text overlay**. |

---

## Notes
- Aligned with GPT-image-1 latest visual guidelines (contrast, texture, style).
- Prevents generic/boring backgrounds when overlay is disabled.
- Reduces visual inconsistency across overlay states.
