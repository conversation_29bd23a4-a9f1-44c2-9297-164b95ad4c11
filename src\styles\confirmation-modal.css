/* Confirmation Modal Styles - Based on Figma Design */

/* Modal Overlay */
.confirmation-modal-overlay {
    animation: fadeIn 0.2s ease-out;
    backdrop-filter: blur(8px); /* Reduced from 12px by 20% (12 * 0.8 = 9.6 ≈ 8px) for subtler MacOS-style glass effect */
    -webkit-backdrop-filter: blur(8px);
    align-items: center !important; /* Center vertically */
    justify-content: center !important; /* Center horizontally */
}

.confirmation-modal-overlay.closing {
    animation: fadeOut 0.2s ease-in;
}

.confirmation-modal-overlay .confirmation-modal-container {
    animation: slideInScale 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.confirmation-modal-overlay.closing .confirmation-modal-container {
    animation: slideOutScale 0.2s cubic-bezier(0.4, 0, 1, 1);
}

/* Alert Composition - Main Modal */
.alert-composition {
    background: rgba(15, 15, 18, 0.75); /* Adjusted background opacity */
    border: 1px solid rgba(243, 244, 246, 0.2); /* Slightly more visible border for liquid glass look */
    border-radius: 20px; /* Slightly reduced for more pill-like shape */
    padding: 18px 12px 18px 12px; /* Updated padding as requested */
    backdrop-filter: blur(8px); /* Reduced from 12px by 20% for subtler glass effect */
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 
        0 6px 10px -2px rgba(0, 0, 0, 0.2), /* Slightly reduced shadow */
        0 3px 6px -2px rgba(0, 0, 0, 0.1),
        0 20px 30px -5px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.05); /* Inner glow for liquid glass effect */
    max-width: 460px; /* Reduced by 15% from 540px to 460px for more compact width */
    width: 100%;
    /* Liquid glass enhancements */
    position: relative;
}

/* Add subtle inner glow effect */
.alert-composition::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
    pointer-events: none;
}

/* Container Main */
.container-main {
    display: flex;
    align-items: flex-start; /* Changed back to flex-start as requested */
    gap: 14px; /* Slightly reduced gap */
    position: relative;
    z-index: 1;
}

/* Icon Container */
.icon-container {
    flex-shrink: 0;
    margin-top: 0; /* Removed margin-top for better centering */
}

.icon-container .iconify {
    color: #9CA3AF; /* text-gray-400 */
    font-size: 24px;
}

/* Content Container */
.content-container {
    flex: 1;
    min-width: 0;
}

/* Upper Section */
.upper-section {
    margin-bottom: 15px; /* Reduced by 15% from 18px to 15px */
}

/* Modal Title */
.modal-title {
    color: #FFFFFF; /* Pure white for maximum contrast */
    font-weight: 600; /* Slightly bolder for better visibility */
    font-size: 1.0rem; /* Set to 1.0rem as requested */
    line-height: 16px; /* Reduced proportionally from 18px to 16px */
    margin-bottom: 10px; /* Reduced by 15% from 12px to 10px */
}

/* Modal Message */
.modal-message {
    color: #D1D5DB; /* Lighter gray for better contrast */
    font-size: 0.930rem; /* Set to 0.930rem as requested */
    line-height: 1.2rem; /* Set to 1.2rem as requested */
}

/* Button Tabs Container */
.button-tabs {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-right: -8px; /* Adjusted for new compact size */
}

/* Cancel Button */
.cancel-btn {
    padding: 7px 17px; /* Reduced by 15% from 8px 20px to 7px 17px */
    border-radius: 8px;
    border: none; /* Removed border */
    background: transparent;
    color: #9CA3AF; /* text-gray-400 */
    font-size: 12px; /* Reduced by 15% from 14px to 12px */
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.cancel-btn:hover {
    color: #D1D5DB; /* text-gray-300 */
    background: rgba(255, 255, 255, 0.05); /* Subtle hover background */
}

/* Confirm Button */
.confirm-btn {
    padding: 7px 17px; /* Reduced by 15% from 8px 20px to 7px 17px */
    border-radius: 8px;
    border: 1px solid #EF4444; /* border-red-500 */
    background: rgba(239, 68, 68, 0.1); /* bg-red-500/10 */
    color: #F87171; /* text-red-400 */
    font-size: 12px; /* Reduced by 15% from 14px to 12px */
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.confirm-btn:hover {
    background: rgba(239, 68, 68, 0.2); /* bg-red-500/20 */
    border-color: #F87171; /* border-red-400 */
}

/* Close Icon Container */
.close-icon-container {
    flex-shrink: 0;
    margin-left: auto; /* Ensure it stays on the right */
}

.close-btn {
    color: #9CA3AF; /* text-gray-400 */
    padding: 4px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.close-btn:hover {
    color: #D1D5DB; /* text-gray-300 */
    background: rgba(255, 255, 255, 0.05); /* Subtle hover background */
}

.close-btn .iconify {
    font-size: 28px; /* Increased by 15% from 24px to 28px for better accessibility */
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

@keyframes slideOutScale {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0) scale(0.95);
    }
}

/* Responsive Design */
@media (max-width: 640px) {
    .alert-composition {
        margin: 16px;
        max-width: calc(100vw - 32px);
        padding: 12px 14px 12px 14px; /* Further reduced padding for mobile, maintaining 15% reduction */
    }
    
    .button-tabs {
        flex-direction: column-reverse;
        gap: 8px;
        margin-right: 0; /* Reset margin for mobile stacked layout */
    }
    
    .cancel-btn,
    .confirm-btn {
        width: 100%;
        justify-content: center;
        padding: 10px 17px; /* Slightly larger for mobile touch targets */
    }
    
    .upper-section {
        margin-bottom: 14px; /* Maintain proportional reduction on mobile */
    }
    
    .close-btn .iconify {
        font-size: 26px; /* Slightly smaller on mobile while maintaining 15% increase */
    }
}

/* Focus States for Accessibility */
.cancel-btn:focus,
.confirm-btn:focus,
.close-btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .modal-title {
        color: #F9FAFB;
    }
    
    .modal-message {
        color: #9CA3AF;
    }
} 