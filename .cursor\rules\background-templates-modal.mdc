---
description: 
globs: 
alwaysApply: true
---
ruleId: background-templates-modal
description: Defines the structure, behavior, and styling for the new interactive visual background templates feature, including a collapsible main section and a modal for selecting categorized background styles with .webp previews.
ruleType: always
appliesTo:
  - /src/components/ControlPanel.jsx # Main controls area, will host the toggle and the collapsible section
  - /src/components/background/BackgroundControls.jsx # New or refactored component for background category selection & modal trigger
  - /src/components/background/BackgroundTemplateModal.jsx # New component for the modal displaying .webp templates
  - /src/components/ui/Modal.jsx # If a generic modal wrapper is used or enhanced
  - /src/contexts/AppContext.jsx # For managing state related to background selection, modal visibility
  - /src/App.jsx # If App.jsx handles top-level state for controls
  - /src/styles/controls.css # For general control styling
  - /src/styles/background-controls.css # Specific styles for the background section and modal
  - /src/assets/background-templates/ # New directory to store .webp preview images

---

## 🧩 Background Templates Modal & Collapsible Section

### 1. Collapsible Section ("Background Styles") in `ControlPanel.jsx`
-   **Master Toggle:**
    -   Implement a `Switch` (Hero UI) labeled "Background Styles" or "Advanced Backgrounds".
    -   Default state: **OFF** (collapsed).
    -   Controls visibility of the `BackgroundControls.jsx` content.
-   **Animation:**
    -   Use a smooth slide-down/slide-up transition (e.g., `max-h-0` to `max-h-screen` with `transition-all duration-300 ease-in-out`) when toggled.
    -   Ensure consistent animation behavior with other collapsible sections (like "Include Person", "Include Icons").

### 2. Background Categories (`BackgroundControls.jsx`)
-   Displayed when the main toggle is ON.
-   **Layout:** Show a list or responsive grid of clickable "category tiles" (e.g., buttons or styled divs).
    -   Examples: "Cinematic", "Gaming", "Tech", "Business", "Abstract", "Solid Color".
    -   Each tile should have a clear label and possibly a representative icon (Solar icons).
-   **Interaction:** Clicking a category tile (except potentially "Solid Color") triggers the `BackgroundTemplateModal.jsx`.
-   "Solid Color" Category:
    -   May directly reveal a color picker inline.
    -   Alternatively, it can open a simpler modal displaying common color swatches (`.webp` previews of solid colors).

### 3. Background Template Modal (`BackgroundTemplateModal.jsx`)
-   **Trigger:** On category tile click.
-   **Structure (Hero UI Modal):**
    -   Header: Dynamic title (e.g., "Cinematic Background Styles").
    -   Body: Contains the grid of `.webp` template previews.
    -   Footer: "Close" button.
-   **Grid Layout:**
    -   CSS Grid or Flexbox for a **4-column layout**.
    -   Initially display **2 rows** (total 8 cells).
        -   Cells 1-7: `.webp` image previews of background styles.
            -   Images should be clear, aspect ratio maintained (e.g., 16:9 snippets).
            -   Hover effect: Slight zoom or border highlight.
            -   Clicking an image selects the style and updates the application state.
        -   Cell 8: "Show More" card.
            -   Styled differently (e.g., subtle background, border, centered text "Show More" + Solar icon like `add-circle-outline`).
            -   Clicking it loads more templates into the grid (modal body becomes scrollable if content exceeds viewport height).
-   **Image Assets:**
    -   All preview images must be `.webp`.
    -   Organize assets in `/public/assets/background-templates/{categoryName}/{styleName}.webp` or similar.
    -   A mapping (e.g., in `smartBackgroundMappings` or a new JSON) will link styles to their `.webp` paths.
-   **Selection Logic:**
    -   When a template is clicked, the modal should update the central state (e.g., via `AppContext`) with the selected background style/ID.
    -   The modal may close automatically on selection, or require explicit closing.

### 4. Styling and UX
-   **Hero UI & Tailwind:** Strictly use Hero UI components and Tailwind CSS utility classes.
-   **Dark Mode:** Ensure all new elements are styled for dark mode.
-   **Responsiveness:** The modal and its grid should be responsive. On smaller screens, the grid might change to 2 columns.
-   **Accessibility:**
    -   Proper ARIA attributes for modal, toggle, buttons, and grid items.
    -   Keyboard navigability for all interactive elements.

### 5. Data Source for Categories & Styles
-   Leverage or extend `smartBackgroundMappings` in `src/utils/promptFormatter.js` or create a dedicated `backgroundsConfig.js`.
-   This config will define categories, their available styles, and paths to corresponding `.webp` preview images.
    ```javascript
    // Example structure in a config file:
    export const backgroundCategories = [
      {
        id: 'cinematic',
        name: 'Cinematic',
        icon: 'solar-movie-tape-bold-duotone', // Example Solar icon
        styles: [
          { id: 'cinematic_bokeh', name: 'Bokeh Bliss', preview: '/assets/background-templates/cinematic/bokeh.webp' },
          { id: 'cinematic_flare', name: 'Lens Flare Drama', preview: '/assets/background-templates/cinematic/flare.webp' },
          // ... 7+ styles
        ]
      },
      // ... other categories
    ];
    ```

---

This should provide a solid foundation for building out this enhanced background selection feature! Let me know your thoughts.