# 🎨 GPT-Image-1 Thumbnail Prompt: Vibrant, Eye-Catching, Perfectly Aligned

## 🧠 Objective
Generate a cinematic, high-impact YouTube thumbnail (1280 × 720, 16 : 9) with:
- Vibrant, saturated colours
- Dramatic lighting and contrast
- Bold, glowing, shadowed text overlays
- Zero text clipping or edge crowding
- Professional, modern visual appeal

---

## ✅ Prompt Rules
**Always include:**
1. “Create a cinematic YouTube thumbnail at **1280×720** resolution, **16 : 9** aspect ratio.”
2. “Use **vibrant, saturated colours** and **dramatic lighting** for maximum visual impact.”
3. “Add a **bold, uppercase title** in a modern sans-serif font, with **strong glow** and **drop shadow** effects.”
4. “Ensure the text overlay is **fully visible, never clipped**, placed inside a safe zone with **≥ 64 px margin** from all edges.”
5. “Anchor the text overlay **top-right**—inside the 64 px safe-zone—never touching edges or bottom corners.”
6. “Break long titles into **two lines** for readability; prioritise clarity on mobile and desktop.”
7. “Frame the subject to leave space for the title; use **rule-of-thirds** (subject left, text right).”
8. “Do **not** overlap icons, arrows, or effects onto the text area.”
9. “Provide a background with **strong contrast** to the text (e.g. dark background, bright text).”
10. “Apply a **slight 3-D bevel** to the text for extra pop.”
11. “Avoid clutter; keep the composition **bold and simple**.”
12. “When **`textOverlay = false`**, strictly remove all words, letters, numbers, captions, or watermarks, but **retain icons, shapes, and other visual elements**.”

---

## 💬 Example Prompt Fragment
> Create a cinematic YouTube thumbnail at 1280×720. Use vibrant, saturated colours and dramatic lighting. Add a bold, uppercase title in a modern sans-serif font with strong glow and drop shadow. Place the text anchored in the top-right corner, with at least 64 px margin from all edges—never clipped or touching the edge. Break long titles into two lines if needed. Frame the subject on the left, text on the right, and ensure the background contrasts with the text. Add a slight 3-D bevel to the text for extra pop.

---

## 🧪 Testing Checklist
- [ ] No text is clipped or too close to the edge (≥ 64 px margin)
- [ ] When textOverlay is **false**, thumbnail contains **zero** stray letters/numbers
- [ ] Colours are vibrant and saturated
- [ ] Text has glow, shadow, and slight 3-D bevel
- [ ] Subject and text follow rule-of-thirds balance
- [ ] Thumbnail is readable and eye-catching on both mobile and desktop

---

> **File location:** `/prompts/vibrant-thumbnail-prompt.md` 