// Enhanced Authentication Utility with "Remember Me" Feature
// Implements secure persistent login using session tokens instead of plain credentials

const REMEMBER_ME_KEY = 'thumbspark_remember_me';
const SESSION_TOKEN_KEY = 'thumbspark_session_token';
const USER_PREFERENCES_KEY = 'thumbspark_user_prefs';

/**
 * Secure Remember Me Authentication Manager
 * Uses session tokens instead of storing plain credentials
 */
export const rememberMeAuth = {
  
  /**
   * Save user session for persistent login
   * @param {Object} session - Supabase session object
   * @param {boolean} rememberMe - Whether to persist the session
   */
  saveSession: (session, rememberMe = false) => {
    try {
      if (!session || !session.access_token) {
        console.warn('Invalid session provided to saveSession');
        return;
      }

      // Always save basic session data to sessionStorage for current session
      sessionStorage.setItem('current_session', JSON.stringify({
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at,
        user_id: session.user?.id,
        email: session.user?.email,
        timestamp: Date.now()
      }));

      // If "Remember Me" is enabled, also save to localStorage
      if (rememberMe) {
        localStorage.setItem(REMEMBER_ME_KEY, 'true');
        localStorage.setItem(SESSION_TOKEN_KEY, JSON.stringify({
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at,
          user_id: session.user?.id,
          email: session.user?.email,
          timestamp: Date.now()
        }));

        // Save user preferences for auto-fill
        localStorage.setItem(USER_PREFERENCES_KEY, JSON.stringify({
          email: session.user?.email,
          full_name: session.user?.user_metadata?.full_name,
          last_login: Date.now()
        }));

        console.log('✅ Session saved with Remember Me enabled');
      } else {
        // If not remembering, clear any existing persistent data
        localStorage.removeItem(REMEMBER_ME_KEY);
        localStorage.removeItem(SESSION_TOKEN_KEY);
        console.log('✅ Session saved for current session only');
      }
    } catch (error) {
      console.error('Error saving session:', error);
    }
  },

  /**
   * Get saved session data for auto-login
   * @returns {Object|null} Saved session data or null
   */
  getSavedSession: () => {
    try {
      // Check if user had "Remember Me" enabled
      const rememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
      
      if (!rememberMe) {
        return null;
      }

      const savedSessionData = localStorage.getItem(SESSION_TOKEN_KEY);
      if (!savedSessionData) {
        return null;
      }

      const sessionData = JSON.parse(savedSessionData);
      
      // Check if the saved session is still valid (not older than 30 days)
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
      const isExpired = Date.now() - sessionData.timestamp > maxAge;
      
      if (isExpired) {
        console.log('Saved session expired, clearing...');
        rememberMeAuth.clearSavedSession();
        return null;
      }

      // Check if the token itself is expired
      if (sessionData.expires_at && new Date(sessionData.expires_at * 1000) < new Date()) {
        console.log('Access token expired, will need refresh...');
        // Don't clear yet - Supabase might be able to refresh it
      }

      return sessionData;
    } catch (error) {
      console.error('Error getting saved session:', error);
      // Clear corrupted data
      rememberMeAuth.clearSavedSession();
      return null;
    }
  },

  /**
   * Get user preferences for auto-fill
   * @returns {Object|null} User preferences or null
   */
  getUserPreferences: () => {
    try {
      const rememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
      if (!rememberMe) {
        return null;
      }

      const prefsData = localStorage.getItem(USER_PREFERENCES_KEY);
      if (!prefsData) {
        return null;
      }

      return JSON.parse(prefsData);
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return null;
    }
  },

  /**
   * Check if Remember Me is currently enabled
   * @returns {boolean} True if Remember Me is enabled
   */
  isRememberMeEnabled: () => {
    return localStorage.getItem(REMEMBER_ME_KEY) === 'true';
  },

  /**
   * Clear all saved session data (used on sign out or "Forget Me")
   */
  clearSavedSession: () => {
    try {
      localStorage.removeItem(REMEMBER_ME_KEY);
      localStorage.removeItem(SESSION_TOKEN_KEY);
      localStorage.removeItem(USER_PREFERENCES_KEY);
      sessionStorage.removeItem('current_session');
      console.log('✅ All saved session data cleared');
    } catch (error) {
      console.error('Error clearing saved session:', error);
    }
  },

  /**
   * Update Remember Me preference for current session
   * @param {boolean} enabled - Whether to enable Remember Me
   * @param {Object} session - Current session object
   */
  updateRememberMePreference: (enabled, session) => {
    if (enabled && session) {
      // Enable Remember Me for current session
      rememberMeAuth.saveSession(session, true);
    } else {
      // Disable Remember Me but keep current session active
      localStorage.removeItem(REMEMBER_ME_KEY);
      localStorage.removeItem(SESSION_TOKEN_KEY);
      localStorage.removeItem(USER_PREFERENCES_KEY);
      console.log('✅ Remember Me disabled for future logins');
    }
  },

  /**
   * Validate and refresh a saved session with Supabase
   * @param {Object} supabase - Supabase client
   * @param {Object} sessionData - Saved session data
   * @returns {Promise<Object>} Result with success/error
   */
  validateAndRefreshSession: async (supabase, sessionData) => {
    try {
      if (!sessionData || !sessionData.refresh_token) {
        return { success: false, error: 'No valid session data' };
      }

      // Try to restore the session with Supabase
      const { data, error } = await supabase.auth.setSession({
        access_token: sessionData.access_token,
        refresh_token: sessionData.refresh_token
      });

      if (error) {
        console.error('Error restoring session:', error);
        // If session is invalid, clear saved data
        rememberMeAuth.clearSavedSession();
        return { success: false, error: error.message };
      }

      if (data.session && data.user) {
        // Session successfully restored, update saved data with new tokens
        rememberMeAuth.saveSession(data.session, true);
        console.log('✅ Session restored and refreshed for user:', data.user.email);
        return { success: true, session: data.session, user: data.user };
      }

      return { success: false, error: 'Failed to restore session' };
    } catch (error) {
      console.error('Error validating session:', error);
      rememberMeAuth.clearSavedSession();
      return { success: false, error: error.message };
    }
  }
};

/**
 * Enhanced authentication wrapper that integrates with Supabase
 */
export const enhancedAuth = {
  
  /**
   * Sign in with Remember Me support
   * @param {Object} supabase - Supabase client
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {boolean} rememberMe - Whether to remember the user
   * @returns {Promise<Object>} Authentication result
   */
  signIn: async (supabase, email, password, rememberMe = false) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        // Handle specific Supabase errors
        if (error.message.includes('Invalid login credentials')) {
          return { success: false, error: 'Invalid email or password. Please check your credentials and try again.' };
        }
        if (error.message.includes('Email not confirmed')) {
          return { success: false, error: 'Please check your email and click the confirmation link before signing in.' };
        }
        throw error;
      }
      
      if (data.session && data.user) {
        // Save session based on Remember Me preference
        rememberMeAuth.saveSession(data.session, rememberMe);
        
        return { 
          success: true, 
          data, 
          user: data.user,
          session: data.session,
          rememberMe: rememberMe
        };
      }
      
      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Enhanced sign in error:', error);
      return { success: false, error: error.message || 'Failed to sign in. Please try again.' };
    }
  },

  /**
   * Sign out and clear all saved data
   * @param {Object} supabase - Supabase client
   * @returns {Promise<Object>} Sign out result
   */
  signOut: async (supabase) => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear all saved session data
      rememberMeAuth.clearSavedSession();
      
      // Clear any additional Supabase session data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('sb-csibhnfqpwqkhpnvdakz-auth-token') || 
            key.startsWith('supabase.auth.token')) {
          localStorage.removeItem(key);
        }
      });
      
      return { success: true };
    } catch (error) {
      console.error('Enhanced sign out error:', error);
      // Even if Supabase sign out fails, clear local data
      rememberMeAuth.clearSavedSession();
      return { success: false, error: error.message || 'Failed to sign out completely.' };
    }
  },

  /**
   * Check for and restore saved session on app startup
   * @param {Object} supabase - Supabase client
   * @returns {Promise<Object>} Restoration result
   */
  checkSavedSession: async (supabase) => {
    try {
      const savedSession = rememberMeAuth.getSavedSession();
      
      if (!savedSession) {
        return { success: false, error: 'No saved session found' };
      }

      console.log('🔍 Found saved session, attempting to restore...');
      
      // Validate and refresh the saved session
      const result = await rememberMeAuth.validateAndRefreshSession(supabase, savedSession);
      
      if (result.success) {
        console.log('✅ Auto-login successful for user:', result.user.email);
        return {
          success: true,
          user: result.user,
          session: result.session,
          autoLogin: true
        };
      } else {
        console.log('❌ Saved session invalid:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error checking saved session:', error);
      rememberMeAuth.clearSavedSession();
      return { success: false, error: error.message };
    }
  }
}; 