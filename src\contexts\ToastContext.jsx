import React, { createContext, useContext, useState, useCallback } from 'react';

const ToastContext = createContext();

export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};

export const ToastProvider = ({ children }) => {
    const [toasts, setToasts] = useState([]);

    const addToast = useCallback((message, type = 'success', duration = 3000) => {
        const id = Date.now() + Math.random();
        const toast = { id, message, type, duration };
        
        setToasts(prev => [...prev, toast]);

        // Auto-remove toast after duration
        setTimeout(() => {
            removeToast(id);
        }, duration);

        return id;
    }, []);

    const removeToast = useCallback((id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    }, []);

    const showSuccess = useCallback((message, duration) => {
        return addToast(message, 'success', duration);
    }, [addToast]);

    const showError = useCallback((message, duration) => {
        return addToast(message, 'error', duration);
    }, [addToast]);

    const showInfo = useCallback((message, duration) => {
        return addToast(message, 'info', duration);
    }, [addToast]);

    const showWarning = useCallback((message, duration) => {
        return addToast(message, 'warning', duration);
    }, [addToast]);

    const value = {
        toasts,
        addToast,
        removeToast,
        showSuccess,
        showError,
        showInfo,
        showWarning
    };

    return React.createElement(ToastContext.Provider, { value }, children);
};

export const ToastContainer = () => {
    const { toasts, removeToast } = useToast();

    if (!toasts.length) return null;

    return React.createElement('div', {
        className: 'toast-container fixed top-6 right-6 z-[10002] space-y-3',
        style: {
            zIndex: 10002,
            pointerEvents: 'none'
        }
    },
        toasts.map((toast, index) =>
            React.createElement(Toast, {
                key: toast.id,
                message: toast.message,
                type: toast.type,
                onDismiss: () => removeToast(toast.id),
                delay: index * 100 // Stagger animations for multiple toasts
            })
        )
    );
};

const Toast = ({ message, type, onDismiss, delay = 0 }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isAnimatingOut, setIsAnimatingOut] = useState(false);

    React.useEffect(() => {
        // Delay appearance for staggered animation
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, delay);

        return () => clearTimeout(timer);
    }, [delay]);

    const getToastConfig = () => {
        switch (type) {
            case 'success':
                return {
                    icon: 'solar:check-circle-bold',
                    iconColor: '#10B981',
                    bgGradient: 'linear-gradient(135deg, #16A34A 0%, #15803D 100%)',
                    shadowColor: 'rgba(22, 163, 74, 0.4)',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                };
            case 'error':
                return {
                    icon: 'solar:close-circle-bold',
                    iconColor: '#EF4444',
                    bgGradient: 'linear-gradient(135deg, #DC2626 0%, #B91C1C 100%)',
                    shadowColor: 'rgba(220, 38, 38, 0.4)',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                };
            case 'warning':
                return {
                    icon: 'solar:danger-triangle-bold',
                    iconColor: '#F59E0B',
                    bgGradient: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
                    shadowColor: 'rgba(245, 158, 11, 0.4)',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                };
            case 'info':
                return {
                    icon: 'solar:info-circle-bold',
                    iconColor: '#3B82F6',
                    bgGradient: 'linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%)',
                    shadowColor: 'rgba(37, 99, 235, 0.4)',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                };
            default:
                return {
                    icon: 'solar:check-circle-bold',
                    iconColor: '#10B981',
                    bgGradient: 'linear-gradient(135deg, #16A34A 0%, #15803D 100%)',
                    shadowColor: 'rgba(22, 163, 74, 0.4)',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                };
        }
    };

    const config = getToastConfig();

    const handleClose = () => {
        setIsAnimatingOut(true);
        
        setTimeout(() => {
            onDismiss();
        }, 495); // Match animation duration
    };

    return React.createElement('div', {
        className: 'toast-item transform transition-all ease-out',
        style: {
            animation: isVisible && !isAnimatingOut 
                ? 'liquidSlideInRight 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)' 
                : isAnimatingOut 
                ? 'liquidSlideOutRight 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)'
                : 'none',
            opacity: isVisible && !isAnimatingOut ? 1 : 0,
            transition: 'opacity 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            perspective: '1000px',
            pointerEvents: 'auto'
        }
    },
        React.createElement('div', {
            className: 'liquid-glass-toast-inverted border backdrop-blur-md rounded-2xl p-4 flex items-center gap-3 min-w-[380px] max-w-[480px] transition-all duration-300 ease-out',
            style: {
                backdropFilter: 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)',
                WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)',
                background: config.bgGradient,
                border: `1px solid ${config.borderColor}`,
                boxShadow: `0 25px 50px -12px ${config.shadowColor}, 0 8px 16px -8px ${config.shadowColor}, 0 0 0 1px rgba(255, 255, 255, 0.1)`,
                borderRadius: '16px'
            }
        },
            // Icon container
            React.createElement('div', {
                className: 'flex-shrink-0',
                style: {
                    width: '40px',
                    height: '40px',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.15)',
                    border: '1px solid rgba(255, 255, 255, 0.25)',
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)'
                }
            },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': config.icon,
                    style: {
                        fontSize: '20px',
                        color: '#ffffff'
                    }
                })
            ),
            
            // Message content
            React.createElement('div', {
                className: 'flex-1'
            },
                React.createElement('p', {
                    className: 'text-white font-semibold leading-tight',
                    style: {
                        fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", system-ui, sans-serif',
                        fontSize: '14px',
                        fontWeight: '600',
                        lineHeight: '1.4',
                        letterSpacing: '-0.01em',
                        color: '#ffffff'
                    }
                }, message)
            ),
            
            // Close button
            React.createElement('button', {
                onClick: handleClose,
                className: 'flex-shrink-0 flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-transparent',
                'aria-label': 'Close notification',
                style: {
                    width: '28px',
                    height: '28px',
                    borderRadius: '8px',
                    background: 'rgba(255, 255, 255, 0.12)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)',
                    transition: 'all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    cursor: 'pointer'
                },
                onMouseEnter: (e) => {
                    e.target.style.transform = 'scale(1.05)';
                    e.target.style.background = 'rgba(255, 255, 255, 0.2)';
                },
                onMouseLeave: (e) => {
                    e.target.style.transform = 'scale(1)';
                    e.target.style.background = 'rgba(255, 255, 255, 0.12)';
                }
            },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:close-circle-bold',
                    style: {
                        fontSize: '16px',
                        color: '#ffffff'
                    }
                })
            )
        )
    );
}; 