// tooltipManager.js - Independent tooltip positioning system
// Prevents tooltip clipping by using fixed positioning and smart edge detection

/**
 * Calculate the optimal position for a tooltip relative to a trigger element
 * @param {HTMLElement} triggerElement - The element that triggers the tooltip
 * @param {HTMLElement} tooltipElement - The tooltip element to position
 * @param {string} preferredPosition - Preferred position: 'top', 'bottom', 'left', 'right'
 * @returns {Object} Position object with x, y coordinates and arrow position
 */
export const calculateTooltipPosition = (triggerElement, tooltipElement, preferredPosition = 'top') => {
    if (!triggerElement || !tooltipElement) return { x: 0, y: 0, position: 'top', arrowClass: 'tooltip-arrow-top' };

    const triggerRect = triggerElement.getBoundingClientRect();
    const tooltipRect = tooltipElement.getBoundingClientRect();
    const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
    };

    const spacing = 8; // Gap between trigger and tooltip
    const edgeBuffer = 16; // Minimum distance from viewport edges

    // Calculate positions for each direction
    const positions = {
        top: {
            x: triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2),
            y: triggerRect.top - tooltipRect.height - spacing,
            arrowClass: 'tooltip-arrow-top'
        },
        bottom: {
            x: triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2),
            y: triggerRect.bottom + spacing,
            arrowClass: 'tooltip-arrow-bottom'
        },
        left: {
            x: triggerRect.left - tooltipRect.width - spacing,
            y: triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2),
            arrowClass: 'tooltip-arrow-left'
        },
        right: {
            x: triggerRect.right + spacing,
            y: triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2),
            arrowClass: 'tooltip-arrow-right'
        }
    };

    // Check if position fits in viewport
    const fitsInViewport = (pos) => {
        return pos.x >= edgeBuffer && 
               pos.x + tooltipRect.width <= viewport.width - edgeBuffer &&
               pos.y >= edgeBuffer && 
               pos.y + tooltipRect.height <= viewport.height - edgeBuffer;
    };

    // Try preferred position first
    if (fitsInViewport(positions[preferredPosition])) {
        return { ...positions[preferredPosition], position: preferredPosition };
    }

    // Fallback order based on preferred position
    const fallbackOrder = {
        top: ['bottom', 'right', 'left'],
        bottom: ['top', 'right', 'left'],
        left: ['right', 'top', 'bottom'],
        right: ['left', 'top', 'bottom']
    };

    // Try fallback positions
    for (const fallbackPos of fallbackOrder[preferredPosition]) {
        if (fitsInViewport(positions[fallbackPos])) {
            return { ...positions[fallbackPos], position: fallbackPos };
        }
    }

    // If no position fits perfectly, adjust the best one to fit
    let bestPosition = positions[preferredPosition];
    
    // Clamp to viewport bounds
    bestPosition.x = Math.max(edgeBuffer, Math.min(bestPosition.x, viewport.width - tooltipRect.width - edgeBuffer));
    bestPosition.y = Math.max(edgeBuffer, Math.min(bestPosition.y, viewport.height - tooltipRect.height - edgeBuffer));

    return { ...bestPosition, position: preferredPosition };
};

/**
 * Apply fixed positioning to a tooltip element
 * @param {HTMLElement} tooltipElement - The tooltip element
 * @param {number} x - X coordinate
 * @param {number} y - Y coordinate
 * @param {string} arrowClass - CSS class for arrow positioning
 */
export const applyFixedPosition = (tooltipElement, x, y, arrowClass = 'tooltip-arrow-top') => {
    if (!tooltipElement) return;

    tooltipElement.style.position = 'fixed';
    tooltipElement.style.left = `${x}px`;
    tooltipElement.style.top = `${y}px`;
    tooltipElement.style.zIndex = '9999';
    tooltipElement.style.transform = 'none'; // Remove any transform-based centering

    // Update arrow position
    const arrow = tooltipElement.querySelector('.tooltip-arrow-fixed');
    if (arrow) {
        // Remove all arrow classes
        arrow.classList.remove('tooltip-arrow-top', 'tooltip-arrow-bottom', 'tooltip-arrow-left', 'tooltip-arrow-right');
        // Add the correct arrow class
        arrow.classList.add(arrowClass);
    }
};

/**
 * Create a smart tooltip that automatically positions itself to avoid clipping
 * @param {HTMLElement} triggerElement - Element that triggers the tooltip
 * @param {string} content - Tooltip content
 * @param {Object} options - Configuration options
 * @returns {HTMLElement} The created tooltip element
 */
export const createSmartTooltip = (triggerElement, content, options = {}) => {
    const {
        preferredPosition = 'top',
        className = 'tooltip-fixed bg-gray-900 text-white text-xs rounded-lg shadow-lg p-3',
        maxWidth = '280px',
        showDelay = 0,
        hideDelay = 150
    } = options;

    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = className;
    tooltip.style.cssText = `
        position: fixed;
        z-index: 9999;
        pointer-events: none;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
        max-width: ${maxWidth};
        line-height: 1.4;
    `;

    // Set content
    if (typeof content === 'string') {
        tooltip.innerHTML = content;
    } else {
        tooltip.appendChild(content);
    }

    // Create arrow element
    const arrow = document.createElement('div');
    arrow.className = 'tooltip-arrow-fixed';
    tooltip.appendChild(arrow);

    // Add to DOM (hidden initially)
    document.body.appendChild(tooltip);

    let showTimeout, hideTimeout;

    const showTooltip = () => {
        clearTimeout(hideTimeout);
        showTimeout = setTimeout(() => {
            // Calculate position after tooltip is in DOM
            const position = calculateTooltipPosition(triggerElement, tooltip, preferredPosition);
            applyFixedPosition(tooltip, position.x, position.y, position.arrowClass);
            
            tooltip.style.opacity = '1';
            tooltip.style.visibility = 'visible';
        }, showDelay);
    };

    const hideTooltip = () => {
        clearTimeout(showTimeout);
        hideTimeout = setTimeout(() => {
            tooltip.style.opacity = '0';
            tooltip.style.visibility = 'hidden';
        }, hideDelay);
    };

    // Event listeners
    triggerElement.addEventListener('mouseenter', showTooltip);
    triggerElement.addEventListener('mouseleave', hideTooltip);
    triggerElement.addEventListener('focus', showTooltip);
    triggerElement.addEventListener('blur', hideTooltip);

    // Cleanup function
    const cleanup = () => {
        clearTimeout(showTimeout);
        clearTimeout(hideTimeout);
        triggerElement.removeEventListener('mouseenter', showTooltip);
        triggerElement.removeEventListener('mouseleave', hideTooltip);
        triggerElement.removeEventListener('focus', showTooltip);
        triggerElement.removeEventListener('blur', hideTooltip);
        if (tooltip.parentNode) {
            tooltip.parentNode.removeChild(tooltip);
        }
    };

    // Return tooltip element and cleanup function
    return { tooltip, cleanup };
};

/**
 * Initialize smart tooltips for existing elements with data-tooltip attribute
 */
export const initializeSmartTooltips = () => {
    const elements = document.querySelectorAll('[data-tooltip]');
    const tooltips = [];

    elements.forEach(element => {
        const content = element.getAttribute('data-tooltip');
        const position = element.getAttribute('data-tooltip-position') || 'top';
        
        if (content) {
            const { tooltip, cleanup } = createSmartTooltip(element, content, {
                preferredPosition: position
            });
            tooltips.push({ element, tooltip, cleanup });
        }
    });

    // Return cleanup function for all tooltips
    return () => {
        tooltips.forEach(({ cleanup }) => cleanup());
    };
};

// Auto-initialize on DOM content loaded
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', initializeSmartTooltips);
} 