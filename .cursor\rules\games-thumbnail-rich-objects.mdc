---
description: 
globs: 
alwaysApply: false
---
@game-thumbnail-rich-objects
ruleId: game-thumbnail-rich-objects-no-logo
description: >
  When "Icons Mode" or "Clean Background with No Design Controls" is enabled, always generate a visually rich, game-authentic thumbnail for video games, but never include the game’s logo or brand name.

appliesTo:
  - /src/utils/promptFormatter.js

ruleType: always

promptInjection: |
  When Icons Mode or Clean Background is enabled, always:
  - Render a visually rich scene filled with multiple game-related objects, icons, gadgets, and UI elements that are instantly recognizable to fans of the game.
  - Include a diverse selection of characters, skins, and avatars from the game, showing them in dynamic, action-oriented, or expressive poses.
  - Display a variety of weapons, weapon skins, loadouts, and equipment, arranged in a way that highlights their uniqueness and in-game value.
  - Integrate authentic in-game UI elements such as scoreboards, health bars, ability icons, rank badges, or inventory panels, styled to match the game’s visual language.
  - Add collectible items, power-ups, or rare gadgets that are iconic within the game’s universe.
  - If applicable, show multiple player statuses (e.g., score, rank, health, shield, ammo) as part of the composition.
  - Use a clean, high-contrast background that makes all objects and UI elements stand out clearly, avoiding clutter but maximizing visual interest.
  - Ensure all elements are game-accurate, using official designs, colors, and proportions.
  - Arrange the composition to feel energetic and immersive, as if capturing a highlight moment or a showcase of the game’s best features.
  - Prioritize clarity and recognizability for each object, icon, and character, so viewers can instantly identify the game and its unique style.
  - **Do NOT include the game’s logo or brand name anywhere in the thumbnail image, text, watermark, or overlay.**

acceptanceCriteria:
  - Thumbnails are always rich in game-specific content (objects, icons, UI, characters, weapons, etc.).
  - The game’s logo or brand name is never present in the image, text, watermark, or overlay.
  - The rule is only triggered for video game templates when the specified modes are enabled.