import React, { useState, useEffect } from 'react'

const SubscriptionCheckoutModal = ({ 
    isOpen, 
    onClose, 
    selectedPlan = null,
    user = null 
}) => {
    // Form state
    const [formData, setFormData] = useState({
        firstName: '',
        country: 'Canada',
        stateRegion: 'Alberta',
        zipCode: '',
        paymentMethod: 'card'
    });

    // Plan configuration state
    const [planConfig, setPlanConfig] = useState({
        numberOfUsers: 1,
        billingFrequency: 'annual', // 'monthly' or 'annual'
        promoCode: '',
        showPromoInput: false
    });

    // Form validation state
    const [errors, setErrors] = useState({});
    const [isProcessing, setIsProcessing] = useState(false);
    
    // Promo code validation state
    const [promoValidation, setPromoValidation] = useState({
        isValidating: false,
        isValid: null,
        message: '',
        discount: 0
    });

    // Countries and states data
    const countries = [
        { value: 'Canada', label: 'Canada' },
        { value: 'United States', label: 'United States' },
        { value: 'United Kingdom', label: 'United Kingdom' },
        { value: 'Australia', label: 'Australia' },
        { value: 'Germany', label: 'Germany' },
        { value: 'France', label: 'France' }
    ];

    const statesRegions = {
        'Canada': [
            { value: 'Alberta', label: 'Alberta' },
            { value: 'British Columbia', label: 'British Columbia' },
            { value: 'Manitoba', label: 'Manitoba' },
            { value: 'Ontario', label: 'Ontario' },
            { value: 'Quebec', label: 'Quebec' }
        ],
        'United States': [
            { value: 'California', label: 'California' },
            { value: 'New York', label: 'New York' },
            { value: 'Texas', label: 'Texas' },
            { value: 'Florida', label: 'Florida' }
        ]
    };

    // Plan features data
    const planFeatures = {
        basic: [
            '20 HD Thumbnail Generations per month',
            'Access to all basic templates',
            'Standard support response time',
            'Basic editing tools',
            '720p max resolution'
        ],
        pro: [
            '100 HD Thumbnail Generations per month',
            'Access to premium templates',
            'Priority support (24/7)',
            'Advanced AI editing tools',
            '4K max resolution',
            'Custom branding options'
        ]
    };

    // Plan pricing
    const planPricing = {
        basic: { monthly: 19, annual: 190 },
        pro: { monthly: 49, annual: 486 }
    };

    // Calculate pricing
    const calculatePricing = () => {
        if (!selectedPlan) return { subtotal: 0, discount: 0, tax: 0, total: 0 };
        
        const basePrice = planPricing[selectedPlan.id]?.[planConfig.billingFrequency] || 0;
        const subtotal = basePrice; // Remove numberOfUsers multiplication since we removed that feature
        const discount = promoValidation.isValid ? (subtotal * promoValidation.discount / 100) : 0;
        const discountedSubtotal = subtotal - discount;
        const tax = discountedSubtotal * 0.05; // 5% tax
        const total = discountedSubtotal + tax;
        
        return { subtotal, discount, tax, total };
    };

    // Handle form input changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    // Handle plan config changes
    const handlePlanConfigChange = (field, value) => {
        setPlanConfig(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Handle promo code validation
    const handlePromoCodeValidation = async () => {
        if (!planConfig.promoCode.trim()) {
            setPromoValidation({
                isValidating: false,
                isValid: false,
                message: 'Please enter a promo code',
                discount: 0
            });
            return;
        }

        setPromoValidation(prev => ({ ...prev, isValidating: true }));

        try {
            // Simulate API call for promo code validation
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock validation logic - in real app, this would be an API call
            const validCodes = {
                'WELCOME20': { discount: 20, message: 'Welcome discount applied!' },
                'SAVE10': { discount: 10, message: 'Discount applied successfully!' },
                'NEWUSER': { discount: 15, message: 'New user discount applied!' }
            };

            const code = planConfig.promoCode.toUpperCase();
            if (validCodes[code]) {
                setPromoValidation({
                    isValidating: false,
                    isValid: true,
                    message: validCodes[code].message,
                    discount: validCodes[code].discount
                });
            } else {
                setPromoValidation({
                    isValidating: false,
                    isValid: false,
                    message: 'Invalid promo code. Please check and try again.',
                    discount: 0
                });
            }
        } catch (error) {
            setPromoValidation({
                isValidating: false,
                isValid: false,
                message: 'Error validating promo code. Please try again.',
                discount: 0
            });
        }
    };

    // Clear promo code validation
    const clearPromoCode = () => {
        setPlanConfig(prev => ({ ...prev, promoCode: '', showPromoInput: false }));
        setPromoValidation({
            isValidating: false,
            isValid: null,
            message: '',
            discount: 0
        });
    };

    // Validate form
    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.firstName.trim()) {
            newErrors.firstName = 'First name is required';
        }
        
        if (!formData.country) {
            newErrors.country = 'Country is required';
        }
        
        if (!formData.stateRegion) {
            newErrors.stateRegion = 'State/Region is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) return;
        
        setIsProcessing(true);
        
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // In a real app, this would process the payment
            console.log('Processing subscription:', {
                formData,
                planConfig,
                selectedPlan,
                pricing: calculatePricing()
            });
            
            // Close modal on success
            onClose();
        } catch (error) {
            console.error('Subscription processing failed:', error);
        } finally {
            setIsProcessing(false);
        }
    };

    // Reset form when modal opens
    useEffect(() => {
        if (isOpen && user) {
            setFormData(prev => ({
                ...prev,
                firstName: user.name?.split(' ')[0] || ''
            }));
        }
    }, [isOpen, user]);

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e) => {
            if (e.key === 'Escape' && isOpen) {
                onClose();
            }
        };
        
        document.addEventListener('keydown', handleEscape);
        return () => document.removeEventListener('keydown', handleEscape);
    }, [isOpen, onClose]);

    if (!isOpen || !selectedPlan) return null;

    const pricing = calculatePricing();
    const currentFeatures = planFeatures[selectedPlan.id] || [];

    return React.createElement('div', {
        className: 'subscription-checkout-modal-overlay fixed inset-0 z-[9999] overflow-y-auto',
        id: 'subscription-checkout-modal-overlay',
        style: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(12px)',
            WebkitBackdropFilter: 'blur(12px)'
        },
        onClick: onClose
    },
        React.createElement('div', {
            className: 'subscription-modal-container min-h-screen px-4 text-center flex items-center justify-center',
            id: 'subscription-modal-container'
        },
            React.createElement('div', {
                className: 'subscription-modal-content inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gray-900/95 backdrop-blur-xl shadow-xl rounded-3xl border border-gray-700/30',
                id: 'subscription-modal-content',
                onClick: (e) => e.stopPropagation()
            },
                // Header
                React.createElement('div', {
                    className: 'modal-header-container subscription-modal-header flex items-center justify-between mb-8',
                    id: 'subscription-modal-header'
                },
                    React.createElement('h2', {
                        className: 'subscription-modal-title text-2xl font-bold text-white',
                        id: 'subscription-modal-title'
                    }, `Get ${selectedPlan.name}`),
                    React.createElement('button', {
                        onClick: onClose,
                        className: 'modal-close-btn-fixed',
                        id: 'subscription-modal-close-btn',
                        'aria-label': 'Close modal'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:close-circle-bold',
                            style: { fontSize: '24px' }
                        })
                    )
                ),

                // Main content
                React.createElement('div', {
                    className: 'subscription-modal-main-content grid grid-cols-1 lg:grid-cols-2 gap-8',
                    id: 'subscription-modal-main-content'
                },
                    // Left column - Billing Information
                    React.createElement('div', {
                        className: 'billing-information-section space-y-6',
                        id: 'billing-information-section'
                    },
                        React.createElement('div', {
                            className: 'billing-form-container',
                            id: 'billing-form-container'
                        },
                            React.createElement('h3', {
                                className: 'billing-section-title text-lg font-semibold text-white mb-4',
                                id: 'billing-section-title'
                            }, 'Billing information'),
                            
                            // First Name
                            React.createElement('div', {
                                className: 'first-name-field-container mb-4',
                                id: 'first-name-field-container'
                            },
                                React.createElement('label', {
                                    className: 'first-name-label block text-sm font-medium text-gray-300 mb-2',
                                    id: 'first-name-label'
                                }, 'First name'),
                                React.createElement('input', {
                                    type: 'text',
                                    value: formData.firstName,
                                    onChange: (e) => handleInputChange('firstName', e.target.value),
                                    className: `first-name-input w-full px-4 py-3 bg-gray-800/50 border ${errors.firstName ? 'border-red-500' : 'border-gray-600'} rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all`,
                                    id: 'first-name-input',
                                    placeholder: 'Enter your first name'
                                }),
                                errors.firstName && React.createElement('p', {
                                    className: 'first-name-error text-red-400 text-sm mt-1',
                                    id: 'first-name-error'
                                }, errors.firstName)
                            ),

                            // Country and State/Region
                            React.createElement('div', {
                                className: 'location-fields-container grid grid-cols-1 md:grid-cols-2 gap-4 mb-4',
                                id: 'location-fields-container'
                            },
                                React.createElement('div', {
                                    className: 'country-field-container',
                                    id: 'country-field-container'
                                },
                                    React.createElement('label', {
                                        className: 'country-label block text-sm font-medium text-gray-300 mb-2',
                                        id: 'country-label'
                                    }, 'Country'),
                                    React.createElement('select', {
                                        value: formData.country,
                                        onChange: (e) => {
                                            handleInputChange('country', e.target.value);
                                            const newStates = statesRegions[e.target.value];
                                            if (newStates && newStates.length > 0) {
                                                handleInputChange('stateRegion', newStates[0].value);
                                            }
                                        },
                                        className: `country-select w-full px-4 py-3 bg-gray-800/50 border ${errors.country ? 'border-red-500' : 'border-gray-600'} rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all`,
                                        id: 'country-select'
                                    },
                                        countries.map(country =>
                                            React.createElement('option', {
                                                key: country.value,
                                                value: country.value
                                            }, country.label)
                                        )
                                    )
                                ),
                                
                                React.createElement('div', {
                                    className: 'state-region-field-container',
                                    id: 'state-region-field-container'
                                },
                                    React.createElement('label', {
                                        className: 'state-region-label block text-sm font-medium text-gray-300 mb-2',
                                        id: 'state-region-label'
                                    }, 'State/Region'),
                                    React.createElement('select', {
                                        value: formData.stateRegion,
                                        onChange: (e) => handleInputChange('stateRegion', e.target.value),
                                        className: `state-region-select w-full px-4 py-3 bg-gray-800/50 border ${errors.stateRegion ? 'border-red-500' : 'border-gray-600'} rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all`,
                                        id: 'state-region-select'
                                    },
                                        (statesRegions[formData.country] || []).map(state =>
                                            React.createElement('option', {
                                                key: state.value,
                                                value: state.value
                                            }, state.label)
                                        )
                                    )
                                )
                            ),

                            // Zip Code
                            React.createElement('div', {
                                className: 'zip-code-field-container mb-6',
                                id: 'zip-code-field-container'
                            },
                                React.createElement('label', {
                                    className: 'zip-code-label block text-sm font-medium text-gray-300 mb-2',
                                    id: 'zip-code-label'
                                }, 'Zip code (optional)'),
                                React.createElement('input', {
                                    type: 'text',
                                    value: formData.zipCode,
                                    onChange: (e) => handleInputChange('zipCode', e.target.value),
                                    className: 'zip-code-input w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all',
                                    id: 'zip-code-input',
                                    placeholder: 'Enter zip code'
                                })
                            ),


                        ),

                        // Payment Method Section
                        React.createElement('div', {
                            className: 'payment-method-section',
                            id: 'payment-method-section'
                        },
                            React.createElement('h3', {
                                className: 'payment-method-title text-lg font-semibold text-white mb-4',
                                id: 'payment-method-title'
                            }, 'Payment method'),
                            React.createElement('div', {
                                className: 'payment-methods-grid grid grid-cols-3 gap-4',
                                id: 'payment-methods-grid'
                            },
                                ['card', 'paypal', 'gpay'].map(method => {
                                    const icons = {
                                        card: 'solar:card-bold',
                                        paypal: 'logos:paypal',
                                        gpay: 'logos:google-pay'
                                    };
                                    const labels = {
                                        card: 'Card',
                                        paypal: 'PayPal',
                                        gpay: 'G Pay'
                                    };
                                    
                                    return React.createElement('label', {
                                        key: method,
                                        className: `payment-method-option payment-method-${method} cursor-pointer p-4 border-2 rounded-xl transition-all ${formData.paymentMethod === method ? 'border-purple-500 bg-purple-500/10' : 'border-gray-600 hover:border-gray-500'}`,
                                        id: `payment-method-${method}`
                                    },
                                        React.createElement('input', {
                                            type: 'radio',
                                            name: 'paymentMethod',
                                            value: method,
                                            checked: formData.paymentMethod === method,
                                            onChange: (e) => handleInputChange('paymentMethod', e.target.value),
                                            className: 'sr-only',
                                            id: `payment-method-${method}-input`
                                        }),
                                        React.createElement('div', {
                                            className: `payment-method-${method}-content text-center`,
                                            id: `payment-method-${method}-content`
                                        },
                                            React.createElement('span', {
                                                className: `iconify text-2xl ${method === 'paypal' ? 'text-blue-500' : 'text-white'} mb-2`,
                                                'data-icon': icons[method]
                                            }),
                                            React.createElement('p', {
                                                className: 'text-white text-sm font-medium'
                                            }, labels[method])
                                        )
                                    );
                                })
                            )
                        )
                    ),

                    // Right column - Plan Info
                    React.createElement('div', {
                        className: 'plan-info-section space-y-6',
                        id: 'plan-info-section'
                    },
                        React.createElement('div', {
                            className: 'plan-details-container',
                            id: 'plan-details-container'
                        },
                            React.createElement('h3', {
                                className: 'plan-info-title text-lg font-semibold text-white mb-4',
                                id: 'plan-info-title'
                            }, 'Plan info'),
                            
                            // Features List
                            React.createElement('div', {
                                className: 'plan-features-container mb-6',
                                id: 'plan-features-container'
                            },
                                React.createElement('h4', {
                                    className: 'plan-features-title text-base font-medium text-white mb-3',
                                    id: 'plan-features-title'
                                }, `With ${selectedPlan.name} you get`),
                                React.createElement('ul', {
                                    className: 'plan-features-list space-y-3',
                                    id: 'plan-features-list'
                                },
                                    currentFeatures.map((feature, index) =>
                                        React.createElement('li', {
                                            key: index,
                                            className: `plan-feature-item plan-feature-item-${index} flex items-start text-gray-200`,
                                            id: `plan-feature-item-${index}`
                                        },
                                            React.createElement('span', {
                                                className: 'iconify text-green-400 mr-3 mt-0.5 flex-shrink-0',
                                                'data-icon': 'solar:check-circle-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            React.createElement('span', {
                                                className: 'text-sm'
                                            }, feature)
                                        )
                                    )
                                )
                            ),



                            // Billing Frequency
                            React.createElement('div', {
                                className: 'billing-frequency-container mb-6',
                                id: 'billing-frequency-container'
                            },
                                React.createElement('label', {
                                    className: 'billing-frequency-label block text-sm font-medium text-gray-300 mb-3',
                                    id: 'billing-frequency-label'
                                }, 'Billing'),
                                React.createElement('div', {
                                    className: 'billing-frequency-toggle flex bg-gray-800/50 p-1 rounded-xl border border-gray-600',
                                    id: 'billing-frequency-toggle'
                                },
                                    React.createElement('button', {
                                        onClick: () => handlePlanConfigChange('billingFrequency', 'monthly'),
                                        className: `billing-frequency-monthly flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all ${planConfig.billingFrequency === 'monthly' ? 'bg-gray-700 text-white' : 'text-gray-400 hover:text-white'}`,
                                        id: 'billing-frequency-monthly'
                                    }, 'Monthly'),
                                    React.createElement('button', {
                                        onClick: () => handlePlanConfigChange('billingFrequency', 'annual'),
                                        className: `billing-frequency-annual flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all ${planConfig.billingFrequency === 'annual' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}`,
                                        id: 'billing-frequency-annual'
                                    }, 'Annual')
                                ),
                                React.createElement('p', {
                                    className: 'billing-frequency-price text-xs text-gray-400 mt-2 text-right',
                                    id: 'billing-frequency-price'
                                }, `${pricing.subtotal.toFixed(2)} CAD/${planConfig.billingFrequency === 'annual' ? 'year' : 'month'}`)
                            ),

                            // Promo Code
                            React.createElement('div', {
                                className: 'promo-code-section mb-6',
                                id: 'promo-code-section'
                            },
                                planConfig.showPromoInput ? (
                                    React.createElement('div', {
                                        className: 'promo-code-input-container',
                                        id: 'promo-code-input-container'
                                    },
                                        React.createElement('label', {
                                            className: 'promo-code-label block text-sm font-medium text-gray-300 mb-2',
                                            id: 'promo-code-label'
                                        }, 'Promo code'),
                                        React.createElement('div', {
                                            className: 'promo-code-controls flex gap-2 mb-2',
                                            id: 'promo-code-controls'
                                        },
                                            React.createElement('input', {
                                                type: 'text',
                                                value: planConfig.promoCode,
                                                onChange: (e) => {
                                                    handlePlanConfigChange('promoCode', e.target.value);
                                                    // Reset validation when user types
                                                    if (promoValidation.isValid !== null) {
                                                        setPromoValidation(prev => ({ ...prev, isValid: null, message: '' }));
                                                    }
                                                },
                                                className: `promo-code-input flex-1 px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all ${
                                                    promoValidation.isValid === true ? 'border-green-500 focus:ring-green-500' :
                                                    promoValidation.isValid === false ? 'border-red-500 focus:ring-red-500' :
                                                    'border-gray-600 focus:ring-purple-500'
                                                }`,
                                                id: 'promo-code-input',
                                                placeholder: 'Enter promo code',
                                                disabled: promoValidation.isValidating
                                            }),
                                            React.createElement('button', {
                                                onClick: clearPromoCode,
                                                className: 'promo-code-clear-btn w-12 h-12 bg-gray-700 hover:bg-gray-600 text-gray-400 hover:text-white rounded-xl transition-colors flex items-center justify-center',
                                                id: 'promo-code-clear-btn',
                                                title: 'Close'
                                            },
                                                React.createElement('span', {
                                                    className: 'iconify',
                                                    'data-icon': 'solar:close-circle-outline',
                                                    style: { fontSize: '20px' }
                                                })
                                            ),
                                            React.createElement('button', {
                                                onClick: handlePromoCodeValidation,
                                                disabled: !planConfig.promoCode.trim() || promoValidation.isValidating || promoValidation.isValid === true,
                                                className: `promo-code-apply-btn px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2 ${
                                                    promoValidation.isValid === true 
                                                        ? 'bg-green-600 text-white cursor-default' 
                                                        : 'bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white'
                                                }`,
                                                id: 'promo-code-apply-btn'
                                            },
                                                promoValidation.isValidating && React.createElement('span', {
                                                    className: 'iconify animate-spin',
                                                    'data-icon': 'solar:refresh-bold'
                                                }),
                                                promoValidation.isValid === true ? 'Applied' : 
                                                promoValidation.isValidating ? 'Validating...' : 'Apply'
                                            )
                                        ),
                                        // Validation feedback
                                        promoValidation.message && React.createElement('div', {
                                            className: `promo-code-validation-message text-sm flex items-center gap-2 ${
                                                promoValidation.isValid === true ? 'text-green-400' : 'text-red-400'
                                            }`,
                                            id: 'promo-code-validation-message'
                                        },
                                            React.createElement('span', {
                                                className: 'iconify',
                                                'data-icon': promoValidation.isValid === true ? 'solar:check-circle-linear' : 'solar:close-circle-bold'
                                            }),
                                            promoValidation.message
                                        )
                                    )
                                ) : (
                                    React.createElement('button', {
                                        onClick: () => handlePlanConfigChange('showPromoInput', true),
                                        className: 'promo-code-add-btn text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors flex items-center gap-2',
                                        id: 'promo-code-add-btn'
                                    },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:add-circle-bold'
                                        }),
                                        'Add a promo code'
                                    )
                                )
                            ),

                            // Pricing Summary
                            React.createElement('div', {
                                className: 'pricing-summary-container bg-gray-800/30 p-4 rounded-xl border border-gray-700 mb-6',
                                id: 'pricing-summary-container'
                            },
                                React.createElement('div', {
                                    className: 'pricing-summary-items space-y-2 mb-3',
                                    id: 'pricing-summary-items'
                                },
                                    React.createElement('div', {
                                        className: 'pricing-subtotal-row flex justify-between items-center',
                                        id: 'pricing-subtotal-row'
                                    },
                                        React.createElement('span', {
                                            className: 'text-gray-400'
                                        }, 'Subtotal'),
                                        React.createElement('span', {
                                            className: 'text-white',
                                            id: 'pricing-subtotal-amount'
                                        }, `${pricing.subtotal.toFixed(2)} CAD`)
                                    ),
                                    pricing.discount > 0 && React.createElement('div', {
                                        className: 'pricing-discount-row flex justify-between items-center',
                                        id: 'pricing-discount-row'
                                    },
                                        React.createElement('span', {
                                            className: 'text-green-400 flex items-center gap-1'
                                        },
                                            React.createElement('span', {
                                                className: 'iconify',
                                                'data-icon': 'solar:tag-bold'
                                            }),
                                            `Discount (${promoValidation.discount}%)`
                                        ),
                                        React.createElement('span', {
                                            className: 'text-green-400',
                                            id: 'pricing-discount-amount'
                                        }, `-${pricing.discount.toFixed(2)} CAD`)
                                    ),
                                    React.createElement('div', {
                                        className: 'pricing-tax-row flex justify-between items-center',
                                        id: 'pricing-tax-row'
                                    },
                                        React.createElement('span', {
                                            className: 'text-gray-400'
                                        }, 'Tax (5%)'),
                                        React.createElement('span', {
                                            className: 'text-white',
                                            id: 'pricing-tax-amount'
                                        }, `${pricing.tax.toFixed(2)} CAD`)
                                    )
                                ),
                                React.createElement('div', {
                                    className: 'pricing-total-section border-t border-gray-600 pt-3',
                                    id: 'pricing-total-section'
                                },
                                    React.createElement('div', {
                                        className: 'pricing-total-row flex justify-between items-center',
                                        id: 'pricing-total-row'
                                    },
                                        React.createElement('span', {
                                            className: 'text-gray-400 font-medium'
                                        }, 'Total charged today'),
                                        React.createElement('span', {
                                            className: 'pricing-total-amount text-white',
                                            id: 'pricing-total-amount',
                                            style: { fontWeight: '800', fontSize: '1.75rem' }
                                        }, `${pricing.total.toFixed(2)} CAD`)
                                    )
                                )
                            ),

                            // Confirm Button
                            React.createElement('button', {
                                onClick: handleSubmit,
                                disabled: isProcessing,
                                className: `subscription-confirm-btn upgrade-cta-btn w-full py-4 px-6 text-white font-bold text-lg rounded-xl disabled:cursor-not-allowed flex items-center justify-center gap-2`,
                                id: 'subscription-confirm-btn',
                                style: isProcessing ? {
                                    background: '#6B7280 !important',
                                    cursor: 'not-allowed'
                                } : {}
                            },
                                isProcessing && React.createElement('span', {
                                    className: 'iconify animate-spin',
                                    'data-icon': 'solar:refresh-bold'
                                }),
                                isProcessing ? 'Processing...' : 'Confirm and pay'
                            ),

                            // Legal Text
                            React.createElement('p', {
                                className: 'subscription-legal-text text-xs text-gray-500 mt-4 leading-relaxed',
                                id: 'subscription-legal-text'
                            }, 
                                'By clicking "Confirm and pay" you agree to our ',
                                React.createElement('a', {
                                    href: '#',
                                    className: 'subscription-terms-link text-purple-400 hover:text-purple-300 underline',
                                    id: 'subscription-terms-link'
                                }, 'Terms of use'),
                                '. ',
                                React.createElement('strong', null, 'Automatic annual renewal:'),
                                ` Your subscription will automatically renew every year. You'll be charged ${pricing.total.toFixed(2)} CAD on the renewal date. `,
                                React.createElement('strong', null, 'Cancel:'),
                                ' You can cancel your renewal anytime from Subscription > Plan.'
                            )
                        )
                    )
                )
            )
        )
    );
};

export default SubscriptionCheckoutModal; 