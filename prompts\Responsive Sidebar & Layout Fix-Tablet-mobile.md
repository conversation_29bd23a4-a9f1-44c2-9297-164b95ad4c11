** Objective

Fix the responsive issues in the current layout, especially for tablet and mobile breakpoints. The sidebar currently breaks the layout and disrupts the user experience.
Requirements

1. Sidebar Behavior (Tablet/Mobile)

On tablet and mobile breakpoints, the sidebar should not be permanently visible.
Implement a push on/off-canvas sidebar:
Sidebar slides in from the left when toggled (hamburger/menu button).
When open, it overlays the main content with a semi-transparent backdrop.
When closed, the main content (preview panel and prompt controls) remains fully visible and usable.
Sidebar should be accessible via a fixed button (top-left or bottom nav).

2. Main Content (Preview & Controls)

The preview panel and prompt controls should always remain fixed and visible (no scroll) at the bottom of the viewport on mobile/tablet.

Main content does not shift or resize when the sidebar is toggled—sidebar overlays, not pushes, the content.
All controls must be easily tappable and readable on small screens.

3. UX Best Practices
Use a smooth transition for sidebar open/close (e.g., <PERSON>lwind’s transition-transform).
Add a backdrop overlay when the sidebar is open; clicking the backdrop closes the sidebar.
Ensure keyboard accessibility (sidebar can be closed with Esc key).
Use a large, clear close button inside the sidebar.
Prevent background scrolling when the sidebar is open.
Sidebar width: 80vw max on mobile, 350px max on tablet.
Use Tailwind’s responsive classes (md:, lg:, etc.) for breakpoints.
4. General
All layout and style changes should be in /styles/layout.css and/or Tailwind classes.
Use only functional React components.
No inline styles; use Tailwind and external CSS only.
Summary
On tablet/mobile, the sidebar should become a push on/off-canvas overlay, leaving the main preview and controls always visible and fixed. This will greatly improve usability and prevent layout breakage on smaller screens.
