🎚️ Feature: Text Overlay Size Control (Medium vs Large)

📌 Purpose

Allow users to choose the size of the text overlay in their thumbnail, focusing only on impactful, legible options (Medium and Large).
Small text is excluded due to poor performance in mobile views and click-through rates.

✅ UX Control Specification

Control Type: Horizontal Radio Buttons

Label: Text Size

Options:

Medium — Clean, readable, balanced for structured thumbnails

Large — Bold, eye-catching, ideal for emotional or reaction thumbnails

Default Value: Large (optimized for mobile-first YouTube design)

🧠 Tooltip / Hint

"Choose how large your title text appears in the thumbnail. Large text grabs attention but may overlap content. Medium is cleaner and more balanced."

Placement:

Inline next to the label OR

Hover/click icon (ℹ️) next to radio group title

🧠 Prompt Logic (buildPrompt Integration)

Update prompt to reflect size selection:

const sizeText = {
  Medium: "Use moderately sized title text that fits comfortably without overpowering the thumbnail.",
  Large: "Use large, bold title text for dramatic visual impact and mobile-first visibility."
};

const textSizeBlock = includeTextOverlay ? sizeText[selectedTextSize] : "";

💡 Bonus UX Tip

🔄 Live Size Preview

Enhance UX with real-time preview of the text size applied to the thumbnail:

Show sample title text in the preview area or next to the toggle

Use scaled Aa / AA labels or font samples to indicate size before generation

Consider animating the transition between size selections to reinforce feedback

🧪 UX Justification

OptionReasonSmall❌ Removed — poor visibility on YouTube mobile thumbnailsMedium✅ Good for clean layouts and text + image balanceLarge✅ Strong attention-grabber, ideal for reactions or emotional content