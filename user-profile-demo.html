<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile Section Demo - Yhumbnail Spark</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Iconify -->
    <script src="https://code.iconify.design/3/3.1.1/iconify.min.js"></script>
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <style>
        /* User Profile Section Styles */
        .user-profile-section {
            background: linear-gradient(145deg, rgba(31, 41, 55, 0.8), rgba(17, 24, 39, 0.9));
            box-shadow: 
                0 10px 25px -5px rgba(0, 0, 0, 0.3),
                0 10px 10px -5px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(75, 85, 99, 0.3);
            transition: all 0.3s ease;
        }

        .user-profile-section:hover {
            box-shadow: 
                0 20px 40px -10px rgba(0, 0, 0, 0.4),
                0 10px 20px -5px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* User Avatar Enhancements */
        .user-profile-section .user-avatar {
            background: linear-gradient(135deg, #3B82F6, #8B5CF6, #EC4899);
            box-shadow: 
                0 4px 12px rgba(59, 130, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
        }

        /* Credits Progress Bar Enhancements */
        .user-profile-section .credits-progress-bar {
            background: linear-gradient(90deg, #8B5CF6, #3B82F6, #06B6D4);
            position: relative;
            overflow: hidden;
        }

        .user-profile-section .credits-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.4), 
                transparent
            );
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Upgrade Button Special Effects */
        .user-profile-section .upgrade-section button {
            background: linear-gradient(135deg, #2563EB, #1D4ED8);
            box-shadow: 
                0 4px 12px rgba(37, 99, 235, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .user-profile-section .upgrade-section button:hover {
            background: linear-gradient(135deg, #1D4ED8, #1E40AF);
            box-shadow: 
                0 6px 16px rgba(37, 99, 235, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* Crown icon special glow */
        .user-profile-section .upgrade-section .iconify[data-icon*="crown"] {
            filter: drop-shadow(0 0 4px rgba(251, 191, 36, 0.5));
        }

        /* User icon subtle glow */
        .user-profile-section .user-avatar .iconify {
            filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
        }
    </style>
</head>
<body class="bg-gray-900">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const UserProfileSection = ({ 
            user, 
            onSignOut, 
            onNavigateToProfile,
            onUpgradeClick 
        }) => {
            const [userInfo, setUserInfo] = useState({
                name: 'Alex Johnson',
                email: '<EMAIL>',
                credits: 750,
                maxCredits: 1000,
                plan: 'free'
            });

            // Update user info when user prop changes
            useEffect(() => {
                if (user) {
                    setUserInfo({
                        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
                        email: user.email || '<EMAIL>',
                        credits: user.app_metadata?.credits || 750,
                        maxCredits: user.app_metadata?.max_credits || 1000,
                        plan: user.app_metadata?.plan || 'free'
                    });
                }
            }, [user]);

            const creditsPercentage = (userInfo.credits / userInfo.maxCredits) * 100;

            return (
                <div 
                    className="user-profile-section bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6 max-w-sm mx-auto"
                    style={{ width: '320px' }}
                >
                    {/* User Info Section */}
                    <div className="user-info-section flex items-center gap-3 mb-6">
                        {/* User Avatar */}
                        <div className="user-avatar w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                            <span 
                                className="iconify"
                                data-icon="solar:user-linear"
                                style={{ fontSize: '20px', color: 'white' }}
                            />
                        </div>
                        {/* User Details */}
                        <div className="user-details flex-1 min-w-0">
                            <h3 className="user-name text-white font-semibold text-base truncate">
                                {userInfo.name}
                            </h3>
                            <p className="user-email text-gray-400 text-sm truncate">
                                {userInfo.email}
                            </p>
                        </div>
                    </div>

                    {/* Upgrade to Pro Section */}
                    <div className="upgrade-section bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/20 rounded-xl p-4 mb-6">
                        <div className="flex items-center gap-2 mb-2">
                            <span 
                                className="iconify text-yellow-400"
                                data-icon="solar:crown-linear"
                                style={{ fontSize: '20px' }}
                            />
                            <span className="text-blue-400 font-semibold text-base">
                                Upgrade to Pro
                            </span>
                        </div>
                        <p className="text-gray-300 text-sm mb-3 leading-relaxed">
                            Get unlimited AI generations and premium features
                        </p>
                        <button 
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2.5 px-4 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                            onClick={onUpgradeClick}
                        >
                            Upgrade Now
                        </button>
                    </div>

                    {/* Available Credits Section */}
                    <div className="credits-section mb-6">
                        <div className="credits-header flex items-center justify-between mb-3">
                            <span className="credits-label text-gray-400 text-sm font-medium">
                                Available Credits
                            </span>
                            <span className="credits-count text-white font-bold text-lg">
                                {userInfo.credits}/{userInfo.maxCredits}
                            </span>
                        </div>
                        {/* Credits Progress Bar */}
                        <div className="credits-progress-container w-full bg-gray-700 rounded-full h-2.5 overflow-hidden">
                            <div 
                                className="credits-progress-bar bg-gradient-to-r from-purple-500 to-blue-500 h-full rounded-full transition-all duration-500 ease-out"
                                style={{ width: `${creditsPercentage}%` }}
                            />
                        </div>
                    </div>

                    {/* Action Buttons Section */}
                    <div className="actions-section space-y-2">
                        {/* View Profile Button */}
                        <button 
                            className="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-700/50 transition-all duration-200 text-left group"
                            onClick={onNavigateToProfile}
                        >
                            <span 
                                className="iconify text-gray-400 group-hover:text-white transition-colors"
                                data-icon="solar:user-circle-linear"
                                style={{ fontSize: '18px' }}
                            />
                            <span className="text-gray-300 group-hover:text-white font-medium transition-colors">
                                View Profile
                            </span>
                        </button>
                        {/* Sign Out Button */}
                        <button 
                            className="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-red-900/20 transition-all duration-200 text-left group"
                            onClick={onSignOut}
                        >
                            <span 
                                className="iconify text-gray-400 group-hover:text-red-400 transition-colors"
                                data-icon="solar:logout-3-linear"
                                style={{ fontSize: '18px' }}
                            />
                            <span className="text-gray-300 group-hover:text-red-400 font-medium transition-colors">
                                Sign Out
                            </span>
                        </button>
                    </div>
                </div>
            );
        };

        const Demo = () => {
            const demoUser = {
                user_metadata: {
                    full_name: 'Alex Johnson'
                },
                email: '<EMAIL>',
                app_metadata: {
                    credits: 750,
                    max_credits: 1000,
                    plan: 'free'
                }
            };

            const handleSignOut = () => {
                console.log('Sign out clicked');
                alert('Sign out functionality would be implemented here');
            };

            const handleNavigateToProfile = () => {
                console.log('Navigate to profile clicked');
                alert('Navigate to profile functionality would be implemented here');
            };

            const handleUpgradeClick = () => {
                console.log('Upgrade clicked');
                alert('Upgrade modal would open here');
            };

            return (
                <div className="min-h-screen bg-gray-900 flex items-center justify-center p-8">
                    <div className="max-w-4xl w-full">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold text-white mb-4">
                                User Profile Section Demo
                            </h1>
                            <p className="text-gray-400 text-lg">
                                Compact user profile section similar to the reference design
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                            {/* Demo Component */}
                            <div className="demo-section">
                                <h2 className="text-xl font-semibold text-white mb-4">
                                    Live Component
                                </h2>
                                <div className="flex justify-center">
                                    <UserProfileSection 
                                        user={demoUser}
                                        onSignOut={handleSignOut}
                                        onNavigateToProfile={handleNavigateToProfile}
                                        onUpgradeClick={handleUpgradeClick}
                                    />
                                </div>
                            </div>
                            
                            {/* Features List */}
                            <div className="features-section">
                                <h2 className="text-xl font-semibold text-white mb-4">
                                    Features
                                </h2>
                                <div className="features-list space-y-3">
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Compact 320px width design
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Linear Solar icons throughout
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Gradient avatar with user icon
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Upgrade to Pro section with crown
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Animated credits progress bar
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        View Profile & Sign Out actions
                                    </div>
                                    <div className="feature-item flex items-center gap-3 text-gray-300">
                                        <span 
                                            className="iconify text-green-400"
                                            data-icon="solar:check-circle-linear"
                                            style={{ fontSize: '20px' }}
                                        />
                                        Hover effects and animations
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<Demo />, document.getElementById('root'));
    </script>
</body>
</html> 