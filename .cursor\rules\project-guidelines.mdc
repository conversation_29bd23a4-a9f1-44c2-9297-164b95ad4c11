---
description: 
globs: 
alwaysApply: true
---
# 📌 Description
Custom rules for GPT-4 Vision Thumbnail Generator MVP.
Defines folder structure, UI conventions, styling (Hero UI CDN + Tailwind), and prompt logic for image generation.

# 📌 Cursor Rules – GPT-4V Thumbnail Generator MVP

## Project Context

You are building a lightweight MVP app using **React**, styled with **Hero UI (CDN)** in **dark mode**, and enhanced with **Heroicons (CDN)**.

# Type: manual

The app allows users to:
- Input a **text prompt**
- Toggle design attributes (mood, icons, text overlay, person)
- Generate a **1280x720 YouTube-style thumbnail** using GPT-4 Vision
- Preview the image and export it if needed

This MVP is designed for a hackathon and must be fast, modular, and easy to debug.

---

## 🧱 Code Style and Structure

- Use **functional React components** only (`useState`, `useEffect`)
- Write clean, modular code with **named exports**
- Use **descriptive, camelCase** variable names (e.g., `includePerson`, `imageURL`)
- Keep all styles in **separate `.css` files** under `/styles`
- Maintain a single source of truth for state in `App.jsx`

### Folder Structure

/public /src /components - PromptInput.jsx - ControlPanel.jsx - ThumbnailPreview.jsx - GenerateButton.jsx /styles - layout.css - controls.css - preview.css App.jsx index.html cursor-rules.mdc prd.md


---

## 📁 Naming Conventions

- **Folders**: lowercase with dashes (`/components`, `/styles`)
- **Files**: PascalCase for components (`PromptInput.jsx`)
- **Exports**: Always use **named exports**
- **CSS**: Break into logical scopes (`layout.css`, `controls.css`)

---

## 🎨 UI & Styling Guidelines

- Use **Tailwind CSS via [Hero UI](mdc:https:/www.heroui.com)** (CDN only)
- Use **Heroicons via CDN**
- Enable and default to **dark mode**
- Use **mobile-first responsive design**
- Style only with:
  - Tailwind utility classes
  - External `.css` for layout or control overrides
- Do **not use inline `style={}` attributes**

### Hero UI & Icons Setup

Add this to `index.html`:

```html
<!-- Hero UI + Tailwind CDN -->
<link href="https://cdn.heroui.com/v1/tailwind.min.css" rel="stylesheet" />
<script src="https://cdn.heroui.com/v1/hero-ui.min.js"></script>

<!-- Heroicons CDN -->
<script src="https://unpkg.com/heroicons@2.0.16/dist/heroicons.min.js"></script>

⚙️ UI Logic Rules

Prompt input: <textarea /> with placeholder
Controls:
Toggle: Include Person
Toggle: Include Icons
Toggle: Enable Text Overlay
Dropdown: Mood (Surprised, Excited, Serious, Happy)
GenerateButton: Constructs final prompt + calls API
Prompt Generation Logic (Simplified)

const buildPrompt = () => {
  return `Create a cinematic YouTube thumbnail in 1280x720 showing ${
    includePerson ? "a " + mood.toLowerCase() + " person" : "a scene"
  } with ${
    includeIcons ? "glowing icons and " : ""
  }${textOverlay ? "bold text overlay" : ""}. Prompt: ${userPrompt}`;
};

📐 Thumbnail Preview Rules

Container: Fixed 1280x720 aspect ratio
Styles: object-cover, rounded-lg, shadow-lg, border
Placeholder shown if no image: dashed border with text

🛠 Technical Stack

React (JSX)
Tailwind CSS via Hero UI
Heroicons via CDN
No backend (call OpenAI Vision API directly or mock it)
Compatible with Bolt.new and Cursor
🧪 Dev Rules (Cursor)

Keep all API fetch logic inside App.jsx or /utils/api.js
Don't use .env — keep keys as constants for demo (or import from config.js)
Use fetch() for POST requests
Use mobile-friendly flexbox layouts (flex-col md:flex-row)
Store reusable UI elements in /components
Split styles into inspectable CSS files
❌ Do NOT

Use class-based React components
Use inline styles or embedded <style> tags
Use external component libraries (other than Hero UI CDN)
Add Firebase, Supabase, or server-side rendering
Use local file/image uploads
✅ Deliverables

 MVP UI with prompt + controls
 Generates prompt and calls GPT-4V API
 Shows image preview in 1280x720
 All CSS in separate files
 Bolt.new compatible demo
🏁 Ready to Build

Use App.jsx to:

Track all user state
Render subcomponents
Preview thumbnail
Submit data to backend (or mock)
This project is designed to impress at hackathon showcase.