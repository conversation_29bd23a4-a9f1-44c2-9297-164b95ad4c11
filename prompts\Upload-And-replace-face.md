## title: Upload and Replace AI Face with User Face

id: user-face-upload-replace.md

🎯 Feature: Upload Personal Face and Replace AI Face (with face-api.js)

Objective

Allow users to upload a personal face photo (headshot) that replaces the AI-generated face in YouTube thumbnails. This empowers creators to generate thumbnails that feature their real likeness with professional cinematic quality — no Photoshop needed.

The uploaded image is processed using face-api.js to detect, crop, and align the face in-browser before sending it to the thumbnail generation API.

🧩 Core UX Flow

Include Person Toggle

Must be ON to show face upload UI

Choose Face Source

📁 Upload (from local device)

🌐 Import by URL (optional field)

face-api.js In-Browser Processing

Detects face and key landmarks

Crops tightly around head

Aligns face vertically (auto rotation)

Shows preview with zoom/pan crop tool (optional)

Image ready to be uploaded or embedded

Live Preview

User sees how the uploaded face looks on canvas

Options: 🔁 Replace | ❌ Remove

Prompt Injection

Replaces AI face using prompt injection logic:

"subject": {
  "person": {
    "use_custom_face": true,
    "custom_face_url": "<https://yourcdn.com/uploads/user-face123.jpg>"
  }
}

🛠️ Technologies Used

ToolPurposeface-api.jsIn-browser face detection, crop, and landmark alignmentFirebase/S3Store uploaded face imageReact UIUpload field + previewCanvas/Fabric.js (optional)Cropping UI

📂 Folder Structure

/components/FaceUpload.jsx
/hooks/useFaceDetection.js
/utils/faceProcessing.js
/public/models/face-api/

✅ Prompt Policy

Do NOT generate a new AI face when "use_custom_face" is true

Person’s pose, lighting, and emotion remain the same

Only replace head area with uploaded image

Store image temporarily or persist per session

🧠 Developer Notes

Load face-api.js model files once on app init:

await faceapi.loadTinyFaceDetectorModel('/models');
await faceapi.loadFaceLandmarkModel('/models');

Validate uploaded images:

File size < 2MB

Format: .jpg or .png

Face must be clearly visible and centered

Optionally apply circular mask or soft fade to blend better

🔐 Privacy & Safety

“Your uploaded face is used only to generate a thumbnail preview and is not stored permanently unless saved.”

Auto-delete after session timeout or on export

Preview UI must show warning if face not detected

✅ Done When:

Users can upload a face only when “Include Person” is ON

Face is automatically cropped and aligned via face-api.js

Cropped face is injected into the final thumbnail

A toggle allows removing or replacing the face easily

Bonus UX Tip 💡

Allow the user to click a “Test It Now” button that instantly renders their uploaded face into a template thumbnail without changing other settings — great for creators testing brand presence.