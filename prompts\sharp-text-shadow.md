# 🎨 Sharp Text Shadow for Overlay Text  
*(with Smart Inversion for Dark Colors)*

## Objective
Enhance the visual depth and readability of all overlay text in thumbnails by applying a **sharp, non-blurred drop shadow**—regardless of the selected text color.  
For very dark overlay text (e.g., pure black), automatically **invert** the shadow to white for maximum contrast.  
The effect should mimic the crisp, high-contrast shadows found in premium YouTube thumbnails, ensuring text stands out on any background.

---

## Requirements

### 1. Shadow Style
- **Default:** `0 2px 0 #000` (plus a secondary offset with subtle transparency).
- **Dark-Text Inversion:** When the overlay text itself is black or extremely dark, the shadow switches to white (`#fff`) or near-white.
- **No blur radius**—the shadow must remain crisp and well-defined.

### 2. Visual Depth
- The shadow must create a tangible sense of elevation above the background.
- It must remain visible across *all* overlay text colors, including light, vibrant, and dark shades.

### 3. Smart Inversion Logic
- Detect if the overlay text color is “very dark” (e.g., luminance or HSL lightness below ~15 %).
- If dark, apply a **white** sharp shadow.
- Otherwise, apply the standard **black** sharp shadow.

### 4. Consistency
- Shadow is applied both in the **live preview** and **generated thumbnail**.
- **No changes** to existing UI controls or color picker layout.

---

## CSS Implementation Example

```css
/* Base sharp shadow */
.text-style-preview-gradient-line {
  text-shadow: 0 2px 0 #000, 0 4px 0 rgba(0,0,0,0.15);
  /* crisp, layered depth – no blur */
}

/* Inverted sharp shadow for very dark text */
.text-style-preview-gradient-line.invert-shadow {
  text-shadow: 0 2px 0 #fff, 0 4px 0 rgba(255,255,255,0.18);
}
```
*Adjust the 2 px / 4 px offsets as desired for optimal depth.*

---

## JavaScript / React Pseudocode

```js
import tinycolor from 'tinycolor2';   // or use your preferred color util

function getTextShadowClass(overlayColor) {
  const isDark = tinycolor(overlayColor).getBrightness() < 40; // 0–255 scale
  return isDark
    ? 'text-style-preview-gradient-line invert-shadow'
    : 'text-style-preview-gradient-line';
}

/* Example usage in JSX */
<span className={getTextShadowClass(primaryTextColor)}>
  {overlayText}
</span>
```

---

## Acceptance Criteria

1. **Always sharp:** Overlay text consistently shows a crisp, non-blurred shadow.
2. **Adaptive:**  
   • Dark (black) text → white shadow  
   • All other colors → black shadow
3. **No UI regressions:** Picker layout and other controls remain unchanged.
4. **Improved readability** across every background or theme.

---

**Note:**  
This enhancement targets **overlay text only** and must not influence any other UI elements or controls.