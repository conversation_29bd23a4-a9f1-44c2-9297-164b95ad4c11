---
description:
globs:
alwaysApply: false
---
# 📌 User Profile Dashboard - Cursor Rules & Implementation Guidelines

## Project Context
You are implementing a **User Profile Dashboard** for the GPT-4V Thumbnail Generator MVP. This is a **frontend-only implementation** with mock data and simulated functionality, designed to be enhanced with real backend integration later.

## 🎯 Core Requirements

### 1. UI/UX Guidelines & Best Practices
- **Information Hierarchy**: Use clear visual hierarchy with proper spacing, typography, and contrast
- **Accessibility**: Implement ARIA attributes, keyboard navigation, and screen reader support
- **Loading States**: Show skeleton loaders and loading indicators for better perceived performance
- **Empty States**: Design helpful empty states with clear CTAs (e.g., "No thumbnails generated yet")
- **Error Handling**: Graceful error states with actionable messages
- **Feedback**: Immediate visual feedback for all user interactions
- **Progressive Disclosure**: Show essential information first, details on demand

### 2. Design System Consistency
- **Hero UI Components**: Use only Hero UI components from CDN
- **Dark Mode**: Implement consistent dark mode theming throughout
- **Color Palette**: Follow existing app color scheme (#006FEE for primary, consistent grays)
- **Typography**: Maintain existing font hierarchy and sizing
- **Spacing**: Use consistent padding/margin patterns from existing components
- **Border Radius**: Follow existing 8px/12px/16px radius patterns

### 3. Solar Icons Implementation
```javascript
// Standard Solar Icon Structure
React.createElement('span', {
    className: 'iconify',
    'data-icon': 'solar:icon-name-icon-type',
    style: { 
        color: '#FFFFFF', // Customizable
        width: '20px',    // Customizable
        height: '20px'    // Customizable
    }
})

// Icon Types: linear, bold, bold-duotone, outline, broken
// Examples:
// 'solar:user-circle-bold'
// 'solar:settings-linear'
// 'solar:credit-card-bold-duotone'
```

### 4. Responsive & Mobile-First Design
- **Breakpoints**: Follow existing responsive patterns (640px, 768px, 1024px)
- **Mobile Priority**: Design mobile-first, enhance for desktop
- **Touch Targets**: Minimum 44px touch targets for mobile
- **Horizontal Scrolling**: Avoid horizontal scroll on mobile
- **Readable Text**: Ensure text remains readable on all screen sizes

### 5. Frontend-Only Implementation
- **Mock Data**: Use realistic mock data for all features
- **Local Storage**: Simulate persistence with localStorage
- **No API Calls**: All functionality should work without backend
- **Simulated Delays**: Add realistic loading delays for better UX testing
- **State Management**: Use React state and context for data management

### 6. Conflict Prevention
- **Isolated Components**: Create new components without modifying existing ones
- **CSS Scoping**: Use specific class names to avoid style conflicts
- **Route Isolation**: Add new routes without affecting existing navigation
- **State Isolation**: Keep profile state separate from existing app state

---

## 🏗️ Implementation Structure

### File Organization
```
/src
  /components
    /profile
      - UserProfilePage.jsx
      - ProfileHeader.jsx
      - GenerationHistory.jsx
      - UsageDashboard.jsx
      - SubscriptionPanel.jsx
      - BillingSection.jsx
  /styles
    - profile.css
  /utils
    - profileMockData.js
    - profileHelpers.js
  /contexts
    - ProfileContext.jsx (if needed)
```

### Component Architecture
- **UserProfilePage.jsx**: Main container component
- **ProfileHeader.jsx**: Avatar, user info, basic details
- **GenerationHistory.jsx**: Thumbnail history with search/filter
- **UsageDashboard.jsx**: Credits, usage metrics, visual indicators
- **SubscriptionPanel.jsx**: Plan details, upgrade options
- **BillingSection.jsx**: Payment methods, billing history

---

## 🎨 Design Specifications

### Layout Structure
```css
.user-profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .user-profile-container {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1.5rem;
    }
}
```

### Color Scheme (Dark Mode)
```css
:root {
    --profile-bg-primary: #1a1a1a;
    --profile-bg-secondary: #2a2a2a;
    --profile-border: #3a3a3a;
    --profile-text-primary: #ffffff;
    --profile-text-secondary: #a0a0a0;
    --profile-accent: #006FEE;
    --profile-success: #10b981;
    --profile-warning: #f59e0b;
    --profile-error: #ef4444;
}
```

### Component Styling Patterns
```css
.profile-card {
    background: var(--profile-bg-secondary);
    border: 1px solid var(--profile-border);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.profile-section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--profile-text-primary);
}
```

---

## 📱 Mobile-Responsive Guidelines

### Mobile Layout Adjustments
- **Single Column**: Stack all sections vertically on mobile
- **Collapsible Sections**: Use accordions for complex sections
- **Simplified Navigation**: Reduce navigation complexity on small screens
- **Touch-Friendly**: Larger buttons and touch targets
- **Readable Typography**: Adjust font sizes for mobile readability

### Responsive Breakpoints
```css
/* Mobile First */
.profile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

/* Tablet */
@media (min-width: 768px) {
    .profile-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .profile-grid {
        grid-template-columns: 300px 1fr;
        gap: 2rem;
    }
}
```

---

## 🔧 Mock Data Structure

### User Profile Mock Data
```javascript
export const mockUserProfile = {
    id: 'user_123',
    username: 'john_creator',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    plan: 'Basic',
    joinDate: '2024-01-15',
    credits: {
        total: 700,
        used: 245,
        remaining: 455
    },
    usage: {
        low: { used: 120, cost: 1 },
        medium: { used: 85, cost: 2 },
        high: { used: 40, cost: 4 }
    }
};

export const mockGenerationHistory = [
    {
        id: 'gen_001',
        thumbnail: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300',
        prompt: 'Epic mountain adventure vlog thumbnail',
        quality: 'High',
        date: '2024-03-15T10:30:00Z',
        credits: 4
    },
    // ... more history items
];
```

### Subscription Mock Data
```javascript
export const mockSubscription = {
    plan: 'Basic',
    price: '$9.99',
    billingCycle: 'monthly',
    nextBilling: '2024-04-15',
    paymentMethod: {
        type: 'card',
        last4: '4242',
        brand: 'Visa'
    },
    features: [
        '700 credits per month',
        'All quality levels',
        'Generation history',
        'Basic support'
    ]
};
```

---

## 🚀 Implementation Checklist

### Phase 1: Core Structure
- [ ] Create UserProfilePage.jsx with routing
- [ ] Implement ProfileHeader with avatar and basic info
- [ ] Add navigation dropdown to main app header
- [ ] Create profile.css with base styling

### Phase 2: History & Usage
- [ ] Build GenerationHistory component with mock data
- [ ] Implement UsageDashboard with visual indicators
- [ ] Add search/filter functionality to history
- [ ] Create responsive grid layouts

### Phase 3: Subscription & Billing
- [ ] Build SubscriptionPanel with plan details
- [ ] Add upgrade/downgrade simulation
- [ ] Implement BillingSection with payment methods
- [ ] Add cancel subscription flow

### Phase 4: Polish & Testing
- [ ] Add loading states and animations
- [ ] Implement error handling
- [ ] Test responsive behavior
- [ ] Verify accessibility compliance
- [ ] Test for conflicts with existing code

---

## ⚠️ Important Notes

### Conflict Prevention
- **CSS Namespacing**: Prefix all profile CSS classes with `.profile-`
- **Component Isolation**: Don't modify existing components
- **State Management**: Keep profile state separate from app state
- **Route Protection**: Ensure profile routes don't interfere with existing routes

### Performance Considerations
- **Lazy Loading**: Implement lazy loading for profile images
- **Virtual Scrolling**: For large history lists
- **Memoization**: Use React.memo for expensive components
- **Image Optimization**: Optimize avatar and thumbnail images

### Future Backend Integration Points
- **API Endpoints**: Document where real API calls will replace mock data
- **Authentication**: Mark where auth checks will be needed
- **Data Validation**: Prepare for server-side validation
- **Error Handling**: Design for real API error responses

---

## 🎯 Success Criteria

1. **Functionality**: All profile features work with mock data
2. **Design Consistency**: Matches existing app design language
3. **Responsiveness**: Works perfectly on mobile and desktop
4. **Performance**: Fast loading and smooth interactions
5. **Accessibility**: Meets WCAG 2.1 AA standards
6. **No Conflicts**: Doesn't break existing functionality
7. **Maintainability**: Clean, documented code ready for backend integration

---

This implementation should provide a complete, polished user profile experience that can be easily enhanced with real backend functionality later.
