# Context7MCP Smart Text Overlay Placement

Design a cinematic YouTube thumbnail at 1280x720 resolution with **context-aware, visually optimal text overlay placement**.

**Requirements:**
- **Text Overlay:** Add a bold, uppercase headline related to the video topic.
- **Font:** Modern, sans-serif, highly legible, with strong contrast.
- **Positioning:** 
  - *Primary*: Top-right or center-right, but always within a 40px safe zone from all edges.
  - *Context-Aware*: If the main subject (person, object, or icon) is on the right, place text on the left, and vice versa.
  - *Dynamic Adjustment*: If the background is busy or high-contrast, automatically move text to the clearest, least cluttered area.
  - *Mobile Safe*: Ensure text is never clipped or obscured by YouTube UI elements (profile, timestamp, play button).
- **Layout:**
  - Use pyramid or stacked layout for 3+ word headlines.
  - For short headlines, use a single line or two lines with balanced spacing.
- **Styling:**
  - Add a subtle glow or drop shadow for readability.
  - Use a gradient or outline for extra contrast if the background is complex.
- **Context7MCP Logic:**
  - Analyze the thumbnail’s main visual focus and avoid overlapping faces, key objects, or brand logos.
  - If a person is present, avoid covering the face or expressive features.
  - For gaming, tech, or business topics, align text with the main action or focal point for maximum impact.
- **Accessibility:** Ensure text is readable for all users, including those with color vision deficiencies.

**Output:**  
A thumbnail with perfectly placed, readable, and visually balanced text overlay, following all context7mcp rules.