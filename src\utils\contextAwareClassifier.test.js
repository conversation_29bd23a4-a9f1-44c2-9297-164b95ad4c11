/**
 * Test file for Context-Aware Keyword Disambiguation System
 * 
 * Run this in the browser console to test the functionality:
 * import('./contextAwareClassifier.test.js')
 */

import { 
    analyzePromptContext, 
    shouldApplyGamingLogic, 
    getCorrectLogoDescription 
} from './contextAwareClassifier.js';

// Test cases from the documentation
const testCases = [
    // Tech context (should NOT be gaming)
    {
        prompt: "code review best practices",
        expectedGaming: false,
        expectedContext: "tech",
        description: "Code in programming context"
    },
    {
        prompt: "write clean code in JavaScript",
        expectedGaming: false,
        expectedContext: "tech",
        description: "Code in programming context"
    },
    {
        prompt: "React vs Vue comparison",
        expectedGaming: false,
        expectedContext: "tech",
        description: "Tech framework comparison"
    },
    {
        prompt: "postal code validation",
        expectedGaming: false,
        expectedContext: "tech",
        description: "Code as identifier"
    },
    // NEW TEST CASE - The problematic prompt
    {
        prompt: "Cinematic Ultimate Battle: Crafting the Best Mobile App Design! showcasing moody lighting with a captivating scene, make some code scripting in the background",
        expectedGaming: false,
        expectedContext: "tech",
        description: "Mobile app design tutorial with battle metaphor"
    },
    
    // Gaming context (should be gaming)
    {
        prompt: "COD tournament highlights",
        expectedGaming: true,
        expectedContext: "gaming",
        description: "Call of Duty gaming context"
    },
    {
        prompt: "COD Warzone loadout guide",
        expectedGaming: true,
        expectedContext: "gaming",
        description: "Gaming with specific mode"
    },
    {
        prompt: "battle royale tips and tricks",
        expectedGaming: true,
        expectedContext: "gaming",
        description: "Gaming genre terminology"
    },
    {
        prompt: "1v1 arena tips",
        expectedGaming: true,
        expectedContext: "gaming",
        description: "Gaming terminology"
    },
    
    // Non-gaming context (should NOT be gaming)
    {
        prompt: "battle cancer with new treatments",
        expectedGaming: false,
        expectedContext: "health",
        description: "Battle in medical context"
    },
    {
        prompt: "cod fish recipes",
        expectedGaming: false,
        expectedContext: "food",
        description: "COD as fish"
    },
    {
        prompt: "rank employees by performance",
        expectedGaming: false,
        expectedContext: "business",
        description: "Rank in business context"
    },
    {
        prompt: "match colors for design",
        expectedGaming: false,
        expectedContext: "general",
        description: "Match in design context"
    }
];

// Logo correction test cases
const logoTestCases = [
    {
        tech: "react",
        expectedLogo: "atom symbol with orbiting electrons",
        description: "React logo should be atom, not infinity"
    },
    {
        tech: "vue",
        expectedLogo: "green V-shaped logo",
        description: "Vue.js distinctive logo"
    },
    {
        tech: "angular",
        expectedLogo: "red shield with A inside",
        description: "Angular shield logo"
    }
];

/**
 * Run all tests and display results
 */
export function runContextAwareTests() {
    console.log("🧪 Running Context-Aware Keyword Disambiguation Tests\n");
    
    let passed = 0;
    let failed = 0;
    
    // Test context analysis
    testCases.forEach((testCase, index) => {
        const analysis = analyzePromptContext(testCase.prompt);
        const shouldBeGaming = shouldApplyGamingLogic(testCase.prompt);
        
        const contextCorrect = analysis.primaryContext === testCase.expectedContext || 
                              (testCase.expectedContext === "general" && analysis.confidence < 0.3);
        const gamingCorrect = shouldBeGaming === testCase.expectedGaming;
        
        if (contextCorrect && gamingCorrect) {
            console.log(`✅ Test ${index + 1}: ${testCase.description}`);
            console.log(`   Prompt: "${testCase.prompt}"`);
            console.log(`   Context: ${analysis.primaryContext} (confidence: ${analysis.confidence.toFixed(2)})`);
            console.log(`   Gaming: ${shouldBeGaming} ✓\n`);
            passed++;
        } else {
            console.log(`❌ Test ${index + 1}: ${testCase.description}`);
            console.log(`   Prompt: "${testCase.prompt}"`);
            console.log(`   Expected: ${testCase.expectedContext} context, gaming: ${testCase.expectedGaming}`);
            console.log(`   Got: ${analysis.primaryContext} context, gaming: ${shouldBeGaming}`);
            console.log(`   Confidence: ${analysis.confidence.toFixed(2)}\n`);
            failed++;
        }
    });
    
    // Test logo corrections
    console.log("🎨 Testing Logo Corrections\n");
    
    logoTestCases.forEach((testCase, index) => {
        const logoDesc = getCorrectLogoDescription(testCase.tech);
        
        if (logoDesc === testCase.expectedLogo) {
            console.log(`✅ Logo Test ${index + 1}: ${testCase.description}`);
            console.log(`   ${testCase.tech} → ${logoDesc} ✓\n`);
            passed++;
        } else {
            console.log(`❌ Logo Test ${index + 1}: ${testCase.description}`);
            console.log(`   ${testCase.tech} → Expected: ${testCase.expectedLogo}`);
            console.log(`   Got: ${logoDesc || 'null'}\n`);
            failed++;
        }
    });
    
    // Summary
    console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
        console.log("🎉 All tests passed! Context-aware disambiguation is working correctly.");
    } else {
        console.log("⚠️ Some tests failed. Please review the implementation.");
    }
    
    return { passed, failed, total: passed + failed };
}

// Auto-run tests if this file is imported
if (typeof window !== 'undefined') {
    // Browser environment - run tests after a short delay
    setTimeout(() => {
        console.log("🚀 Auto-running Context-Aware Classification Tests...\n");
        runContextAwareTests();
    }, 100);
}

export default { runContextAwareTests }; 