---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
@text-overlay-safezone
ruleId: text-overlay-safezone
description: >
  Ensures all thumbnail text overlays are positioned within a safe area, with 20–40px margin from every edge. Prevents text clipping and guarantees readability on all sides and devices. All text overlays must be written in ALL CAPS (uppercase letters only).

appliesTo:
  - /src/utils/promptBuilder.ts
  - /src/utils/promptFormatter.js
  - /src/templates/
  - /src/components/ThumbnailPreview.jsx

ruleType: always

implementationNotes: |
  - Always add a prompt fragment instructing the model to keep text within a safe zone (20–40px margin from all edges).
  - Never allow text to be placed flush against any edge.
  - Apply this rule to all text positions, not just top-right.
  - All text overlays must be written in ALL CAPS (uppercase letters only, no small letters).
  - Use: "Add large, bold title text in the top-right, center, or any required position, with at least 40px padding from the edges to avoid clipping. Use safe-zone alignment to ensure the text is readable and never cut off on any side. ALWAYS WRITE TEXT IN ALL CAPS."
  - Test on all aspect ratios and device sizes to confirm no clipping occurs.

samplePromptFragment: |
  ADD LARGE, BOLD TITLE TEXT IN THE TOP-RIGHT, CENTER, OR ANY REQUIRED POSITION, WITH AT LEAST 40PX PADDING FROM THE EDGES TO AVOID CLIPPING. USE SAFE-ZONE ALIGNMENT TO ENSURE THE TEXT IS READABLE AND NEVER CUT OFF ON ANY SIDE. ALWAYS WRITE TEXT IN ALL CAPS.