---
description: 
globs: 
alwaysApply: false
---
# YOUTUBE THUMBNAIL PROCESSING PROTOCOL

## CORE MANDATES
1. ⚠️ **ALL** thumbnails must be processed through backend Sharp pipeline
2. ⚠️ Final output **MUST** be exactly 1280×720 pixels
3. ⚠️ **NO** unprocessed images exposed to users
4. ⚠️ Reject any image failing validation checks

## BACKEND PROCESSING PIPELINE
### Step 1: Image Acquisition
- Download AI-generated images directly to memory
- Required library: `axios`
- Timeout: 15s max

### Step 2: Sharp Processing Chain (NON-NEGOTIABLE)
```javascript
// REQUIRED STEPS (MUST INCLUDE ALL)
const processedBuffer = await sharp(inputBuffer)
  .rotate() // Auto-orient based on EXIF
  .resize({
    width: 1280,
    height: 720,
    fit: 'cover', // Maintain aspect ratio
    position: 'attention', // Focus on salient content
    kernel: 'lanczos3' // Highest quality scaling
  })
  .jpeg({ 
    quality: 90,
    progressive: true,
    mozjpeg: true // Critical for web optimization
  })
  .toBuffer();

  Step 3: Validation (MUST IMPLEMENT)

  const metadata = await sharp(processedBuffer).metadata();
if (metadata.width !== 1280 || metadata.height !== 720) {
  throw new Error('INVALID_DIMENSIONS'); // Hard failure
}

Step 4: Output Headers (EXACT SYNTAX)

res.setHeader('Content-Type', 'image/jpeg');
res.setHeader('Content-Disposition', 'attachment; filename="yt_thumbnail.jpg"');

FRONTEND RULES
Strict Prohibitions
❌ Never display raw image.url from AI provider

❌ Block downloads until backend validation passes

❌ Hide previews for unprocessed images

DEPENDENCIES (MINIMUM VERSIONS)

ERROR HANDLING
Rejection Criteria
Dimensions ≠ 1280×720 after processing

File size < 50KB (indicates corruption)

Processing time > 5s

Invalid EXIF data

Audit Logging (REQUIRED)

console.log(`[AUDIT] ${new Date().toISOString()} | 
Dims: ${metadata.width}x${metadata.height} | 
Size: ${processedBuffer.length} bytes`);


SECURITY PROTOCOLS
1- Input sanitization:
const cleanPrompt = prompt.replace(/[<>:"\/\\|?*]/g, '');

Memory hardening:

sharp.cache({ items: 0 }); // Disable disk caching
sharp.concurrency(1);      // Prevent resource exhaustion

COMPLIANCE CHECKS
Pre-Deployment Tests

# Verify Sharp installation
npx sharp --version

# Validate output dimensions (sample test)
curl -I your-api.com/generate-thumbnail | grep "1280x720"

# Quality check (minimum file size)
test $(wc -c < thumbnail.jpg) -ge 80000

Monitoring Requirements
Log all dimension violations

Alert on consecutive failures

Weekly audit reports


### Implementation Instructions:
1. Create new file `THUMBNAIL_PROCESSING_RULES.mdc` in project root
2. Paste entire content above
3. Add reference in critical files:
   ```javascript
   // Top of API routes
   const COMPLIANCE = require('../THUMBNAIL_PROCESSING_RULES.mdc');

 **   Add validation script to package.json:

   "scripts": {
  "validate-thumbnails": "node ./compliance-checks.js"
}

Key Enforcement Points:
Zero Tolerance Dimensions: Automatic rejection of non-1280×720 images

End-to-End Encryption: Images never touch disk during processing

MozJPEG Mandate: Guarantees web-optimized compression

Real-time Auditing: Dimension logs provide compliance proof

Hardened Security: Input sanitization + memory limits

This file ensures architectural enforcement of YouTube standards regardless of AI model changes or frontend updates. The rules automatically invalidate any implementation that doesn't produce exact 1280×720 outputs.