const React = window.React;
const { useState } = React;
import { UserProfileSection } from '../components/UserProfileSection.jsx';

export const UserProfileDemo = () => {
    const [demoUser] = useState({
        user_metadata: {
            full_name: '<PERSON>'
        },
        email: '<EMAIL>',
        app_metadata: {
            credits: 750,
            max_credits: 1000,
            plan: 'free'
        }
    });

    const handleSignOut = () => {
        console.log('Sign out clicked');
        alert('Sign out functionality would be implemented here');
    };

    const handleNavigateToProfile = () => {
        console.log('Navigate to profile clicked');
        alert('Navigate to profile functionality would be implemented here');
    };

    const handleUpgradeClick = () => {
        console.log('Upgrade clicked');
        alert('Upgrade modal would open here');
    };

    return React.createElement('div', {
        className: 'user-profile-demo min-h-screen bg-gray-900 flex items-center justify-center p-8'
    },
        React.createElement('div', {
            className: 'demo-container max-w-4xl w-full'
        },
            React.createElement('div', {
                className: 'text-center mb-8'
            },
                React.createElement('h1', {
                    className: 'text-3xl font-bold text-white mb-4'
                }, 'User Profile Section Demo'),
                React.createElement('p', {
                    className: 'text-gray-400 text-lg'
                }, 'Compact user profile section similar to the reference design')
            ),
            
            React.createElement('div', {
                className: 'demo-grid grid grid-cols-1 lg:grid-cols-2 gap-8 items-start'
            },
                // Demo Component
                React.createElement('div', {
                    className: 'demo-section'
                },
                    React.createElement('h2', {
                        className: 'text-xl font-semibold text-white mb-4'
                    }, 'Live Component'),
                    React.createElement('div', {
                        className: 'flex justify-center'
                    },
                        React.createElement(UserProfileSection, {
                            user: demoUser,
                            onSignOut: handleSignOut,
                            onNavigateToProfile: handleNavigateToProfile,
                            onUpgradeClick: handleUpgradeClick
                        })
                    )
                ),
                
                // Features List
                React.createElement('div', {
                    className: 'features-section'
                },
                    React.createElement('h2', {
                        className: 'text-xl font-semibold text-white mb-4'
                    }, 'Features'),
                    React.createElement('div', {
                        className: 'features-list space-y-3'
                    },
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Compact 320px width design'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Linear Solar icons throughout'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Gradient avatar with user icon'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Upgrade to Pro section with crown'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Animated credits progress bar'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'View Profile & Sign Out actions'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Hover effects and animations'
                        ),
                        React.createElement('div', {
                            className: 'feature-item flex items-center gap-3 text-gray-300'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '20px' }
                            }),
                            'Responsive design'
                        )
                    )
                )
            ),
            
            // Usage Instructions
            React.createElement('div', {
                className: 'usage-section mt-12 bg-gray-800/50 border border-gray-700 rounded-xl p-6'
            },
                React.createElement('h2', {
                    className: 'text-xl font-semibold text-white mb-4'
                }, 'Usage'),
                React.createElement('div', {
                    className: 'code-block bg-gray-900 border border-gray-600 rounded-lg p-4 overflow-x-auto'
                },
                    React.createElement('pre', {
                        className: 'text-sm text-gray-300'
                    },
                        React.createElement('code', null, `import { UserProfileSection } from './components/UserProfileSection.jsx';

React.createElement(UserProfileSection, {
    user: userObject,
    onSignOut: handleSignOut,
    onNavigateToProfile: handleNavigateToProfile,
    onUpgradeClick: handleUpgradeClick
});`)
                    )
                )
            )
        )
    );
}; 