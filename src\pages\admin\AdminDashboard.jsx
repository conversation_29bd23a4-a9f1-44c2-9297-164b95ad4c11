const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;

import { DashboardOverviewPage } from '../../components/admin/DashboardOverviewPage.jsx';
import { TemplateManagementPage } from '../../components/admin/TemplateManagementPage.jsx';
import { BackgroundManagementPage } from '../../components/admin/BackgroundManagementPage.jsx';
import { UserManagementPage } from '../../components/admin/UserManagementPage.jsx';
import { ApiUsageAnalyticsPage } from '../../components/admin/ApiUsageAnalyticsPage.jsx';
import '../../styles/admin.css';

export const AdminDashboard = ({ onExitDashboard = null }) => {
    const [activeSection, setActiveSection] = useState('dashboard');
    const [isNavigating, setIsNavigating] = useState(false);

    const navigationItems = [
        { id: 'dashboard', label: 'Dashboard Overview', icon: 'chart-bar' },
        { id: 'users', label: 'User Management', icon: 'users' },
        { id: 'templates', label: 'Template Management', icon: 'document-duplicate' },
        { id: 'backgrounds', label: 'Background Management', icon: 'photo' },
        { id: 'api-analytics', label: 'API Analytics', icon: 'chart-pie' },
        { id: 'settings', label: 'Settings', icon: 'cog-6-tooth' }
    ];

    const handleSkipToAuth = () => {
        // Set loading state
        setIsNavigating(true);
        
        // Clear authentication state to return to welcome/login page
        localStorage.removeItem('thumbnail-generator-auth');
        
        // Small delay for better UX, then reload
        setTimeout(() => {
            window.location.reload();
        }, 500);
    };

    const renderMainContent = () => {
        switch (activeSection) {
            case 'dashboard':
                return React.createElement(DashboardOverviewPage);
            case 'users':
                return React.createElement(UserManagementPage);
            case 'templates':
                return React.createElement(TemplateManagementPage);
            case 'backgrounds':
                return React.createElement(BackgroundManagementPage);
            case 'api-analytics':
                return React.createElement(ApiUsageAnalyticsPage);
            case 'settings':
                return React.createElement('div', { 
                    className: 'admin-settings-placeholder',
                    id: 'admin-settings-content'
                }, 
                    React.createElement('h2', { className: 'text-2xl font-bold text-white mb-4' }, 'Settings'),
                    React.createElement('p', { className: 'text-gray-400' }, 'Settings page coming soon...')
                );
            default:
                return React.createElement(DashboardOverviewPage);
        }
    };

    const createNavigationItem = (item) => {
        const isActive = activeSection === item.id;
        
        return React.createElement('button', {
            key: item.id,
            id: `admin-nav-${item.id}`,
            className: `admin-nav-item w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                isActive 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            }`,
            onClick: () => setActiveSection(item.id),
            'aria-current': isActive ? 'page' : undefined
        },
            React.createElement('svg', {
                className: 'admin-nav-icon w-5 h-5 mr-3',
                fill: 'none',
                stroke: 'currentColor',
                viewBox: '0 0 24 24'
            },
                React.createElement('path', {
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round',
                    strokeWidth: 2,
                    d: getIconPath(item.icon)
                })
            ),
            React.createElement('span', { className: 'admin-nav-label' }, item.label)
        );
    };

    const getIconPath = (iconName) => {
        const icons = {
            'chart-bar': 'M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z',
            'users': 'M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z',
            'document-duplicate': 'M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75',
            'photo': 'm2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z',
            'chart-pie': 'M10.5 6a7.5 7.5 0 107.5 7.5h-7.5V6z M13.5 10.5H21A7.5 7.5 0 0013.5 3v7.5z',
            'cog-6-tooth': 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
        };
        return icons[iconName] || icons['chart-bar'];
    };

    return React.createElement('div', { 
        className: 'admin-dashboard-container min-h-screen bg-gray-900 flex',
        id: 'admin-dashboard-main'
    },
        // Left Sidebar Navigation
        React.createElement('div', {
            className: 'admin-sidebar-navigation w-64 bg-gray-800 border-r border-gray-700 flex flex-col',
            id: 'admin-sidebar-nav'
        },
            // Sidebar Header
            React.createElement('div', {
                className: 'admin-sidebar-header p-6 border-b border-gray-700',
                id: 'admin-sidebar-header'
            },
                React.createElement('h1', {
                    className: 'admin-sidebar-title text-xl font-bold text-white'
                }, 'CRM Dashboard'),
                React.createElement('p', {
                    className: 'admin-sidebar-subtitle text-sm text-gray-400 mt-1'
                }, 'Thumbnail Generator')
            ),
            
            // Navigation Menu
            React.createElement('nav', {
                className: 'admin-sidebar-nav flex-1 p-4',
                id: 'admin-sidebar-navigation-menu'
            },
                React.createElement('ul', {
                    className: 'admin-nav-list space-y-2'
                }, navigationItems.map(item => 
                    React.createElement('li', { 
                        key: item.id,
                        className: 'admin-nav-list-item'
                    }, createNavigationItem(item))
                ))
            )
        ),

        // Main Content Area
        React.createElement('div', {
            className: 'admin-main-content-wrapper flex-1 flex flex-col',
            id: 'admin-main-content-wrapper'
        },
            // Header
            React.createElement('header', {
                className: 'admin-main-header bg-gray-800 border-b border-gray-700 px-6 py-4 flex justify-between items-center',
                id: 'admin-header-main'
            },
                React.createElement('div', {
                    className: 'admin-header-left flex items-center',
                    id: 'admin-header-left-section'
                },
                    React.createElement('h2', {
                        className: 'admin-page-title text-lg font-semibold text-white capitalize'
                    }, activeSection.replace('-', ' '))
                ),
                
                React.createElement('div', {
                    className: 'admin-header-right flex items-center gap-4',
                    id: 'admin-header-right-section'
                },
                    // Back to Main App Button (if onExitDashboard function is provided)
                    onExitDashboard && React.createElement('button', {
                        className: 'back-to-main-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2',
                        id: 'back-to-main-btn',
                        onClick: onExitDashboard
                    },
                        React.createElement('svg', {
                            className: 'w-4 h-4',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M10 19l-7-7m0 0l7-7m-7 7h18'
                            })
                        ),
                        'Back to Main App'
                    ),
                    
                    // Skip to Login/Signup Button
                    React.createElement('button', {
                        className: `skip-to-auth-btn ${isNavigating ? 'bg-purple-400 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'} text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors`,
                        id: 'skip-to-auth-btn',
                        onClick: handleSkipToAuth,
                        disabled: isNavigating
                    }, isNavigating ? 'Redirecting...' : 'Skip to Login/Signup'),
                    
                    // Admin Profile (placeholder)
                    React.createElement('div', {
                        className: 'admin-profile-section flex items-center',
                        id: 'admin-profile-section'
                    },
                        React.createElement('div', {
                            className: 'admin-profile-avatar w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center',
                            id: 'admin-profile-avatar'
                        },
                            React.createElement('span', {
                                className: 'admin-profile-initial text-white text-sm font-medium'
                            }, 'A')
                        )
                    )
                )
            ),

            // Main Content Area
            React.createElement('main', {
                className: 'admin-content-wrapper flex-1 p-6 overflow-y-auto',
                id: 'admin-main-content-area'
            }, renderMainContent())
        )
    );
}; 