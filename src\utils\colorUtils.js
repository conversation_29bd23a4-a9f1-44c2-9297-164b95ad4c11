/**
 * Color utility functions for gradient text overlay enhancement
 * Applies only to the 9 core colors: yellow, black, white, purple, blue, green, pink, orange, dark orange
 */

// The 9 core colors that this enhancement applies to
export const CORE_COLORS = {
  YELLOW: '#F0D000',
  BLACK: '#000000', 
  WHITE: '#FFFFFF',
  PURPLE: '#8B5CF6',
  BLUE: '#3B82F6',
  GREEN: '#22C55E',
  PINK: '#EC4899',
  ORANGE: '#FFA500',
  DARK_ORANGE: '#FF4B33'
};

// Core color palette with yellow first as default
export const CORE_COLOR_PALETTE = [
  { color: CORE_COLORS.YELLOW, name: 'Yellow' },
  { color: CORE_COLORS.BLACK, name: 'Black' },
  { color: CORE_COLORS.WHITE, name: 'White' },
  { color: CORE_COLORS.PURPLE, name: '<PERSON>' },
  { color: CORE_COLORS.BLUE, name: '<PERSON>' },
  { color: CORE_COLORS.GREEN, name: '<PERSON>' },
  { color: CORE_COLORS.PINK, name: '<PERSON>' },
  { color: CORE_COLORS.ORAN<PERSON>, name: 'Orange' },
  { color: CORE_COLORS.DARK_ORANGE, name: 'Dark Orange' }
];

/**
 * Convert hex color to HSL
 * @param {string} hex - Hex color string (e.g., '#FF0000')
 * @returns {Object} - {h, s, l} values
 */
export function hexToHsl(hex) {
  // Remove the hash if present
  hex = hex.replace('#', '');
  
  // Parse r, g, b values
  const r = parseInt(hex.substr(0, 2), 16) / 255;
  const g = parseInt(hex.substr(2, 2), 16) / 255;
  const b = parseInt(hex.substr(4, 2), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

/**
 * Convert HSL to hex color
 * @param {number} h - Hue (0-360)
 * @param {number} s - Saturation (0-100)
 * @param {number} l - Lightness (0-100)
 * @returns {string} - Hex color string
 */
export function hslToHex(h, s, l) {
  h = h / 360;
  s = s / 100;
  l = l / 100;

  const hue2rgb = (p, q, t) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  let r, g, b;

  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const toHex = (c) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * Generate a darker accent shade for gradient (20-30% darker in HSL)
 * For black/white, use subtle off-black/off-white to avoid harsh banding
 * @param {string} primaryColor - Primary hex color
 * @returns {string} - Darker accent hex color
 */
export function generateDarkerAccentShade(primaryColor) {
  const upperColor = primaryColor.toUpperCase();
  
  // Special handling for black and white to avoid harsh banding
  if (upperColor === CORE_COLORS.BLACK) {
    return '#1A1A1A'; // Subtle off-black
  }
  
  if (upperColor === CORE_COLORS.WHITE) {
    return '#E5E5E5'; // Subtle off-white (darker)
  }
  
  // Special handling for orange colors
  if (upperColor === CORE_COLORS.ORANGE) {
    return '#CC7A00'; // Darker orange
  }
  
  if (upperColor === CORE_COLORS.DARK_ORANGE) {
    return '#CC3300'; // Even darker orange-red
  }
  
  // For other colors, generate 20-30% darker shade using HSL
  const hsl = hexToHsl(primaryColor);
  
  // Reduce lightness by 25% (middle of 20-30% range)
  const darkerLightness = Math.max(0, hsl.l - 25);
  
  // Slightly increase saturation for more vibrant contrast (max 10% increase)
  const enhancedSaturation = Math.min(100, hsl.s + 5);
  
  return hslToHex(hsl.h, enhancedSaturation, darkerLightness);
}

/**
 * Check if a color is one of the 9 core colors
 * @param {string} color - Hex color to check
 * @returns {boolean} - True if it's a core color
 */
export function isCoreColor(color) {
  const upperColor = color.toUpperCase();
  return Object.values(CORE_COLORS).includes(upperColor);
}

/**
 * Get the appropriate stroke color (white or black) for text contrast
 * Based on the primary color to ensure readability
 * @param {string} primaryColor - Primary hex color
 * @returns {string} - 'white' or 'black' for optimal contrast
 */
export function getOptimalStrokeColor(primaryColor) {
  const upperColor = primaryColor.toUpperCase();
  
  // For light colors, use black stroke
  if (upperColor === CORE_COLORS.WHITE || upperColor === CORE_COLORS.YELLOW || upperColor === CORE_COLORS.ORANGE) {
    return 'black';
  }
  
  // For dark colors, use white stroke
  if (upperColor === CORE_COLORS.BLACK) {
    return 'white';
  }
  
  // For colored overlays (purple, blue, green, pink), determine based on lightness
  const hsl = hexToHsl(primaryColor);
  
  // If lightness is above 50%, use black stroke; otherwise use white
  return hsl.l > 50 ? 'black' : 'white';
}

/**
 * Generate CSS gradient string for text overlay
 * @param {string} primaryColor - Primary hex color
 * @param {string} secondaryColor - Secondary hex color (optional, will be generated if not provided)
 * @returns {string} - CSS linear-gradient string
 */
export function generateTextGradient(primaryColor, secondaryColor = null) {
  const accentColor = secondaryColor || generateDarkerAccentShade(primaryColor);
  return `linear-gradient(135deg, ${primaryColor} 0%, ${accentColor} 100%)`;
}

/**
 * Apply smooth transition styles for color picker interactions
 * @returns {Object} - CSS style object for transitions
 */
export function getColorTransitionStyles() {
  return {
    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
    willChange: 'transform, opacity, box-shadow'
  };
}

/**
 * Get enhanced styles for color circle active state
 * @param {boolean} isSelected - Whether the color is selected
 * @returns {Object} - CSS style object
 */
export function getColorCircleStyles(isSelected) {
  const baseStyles = {
    ...getColorTransitionStyles(),
    transform: 'scale(1)',
    boxShadow: 'none'
  };
  
  if (isSelected) {
    return {
      ...baseStyles,
      transform: 'scale(1.1)',
      boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.8), 0 4px 12px rgba(0, 0, 0, 0.3)'
    };
  }
  
  return baseStyles;
} 