// Debug utility for authentication issues
export const debugAuth = {
  // Log current URL and parameters
  logCurrentURL: () => {
    const url = new URL(window.location.href);
    console.log('Current URL:', url.href);
    console.log('URL Parameters:', Object.fromEntries(url.searchParams));
    console.log('Pathname:', url.pathname);
    return {
      href: url.href,
      params: Object.fromEntries(url.searchParams),
      pathname: url.pathname
    };
  },

  // Log localStorage contents
  logStorage: () => {
    const storage = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      storage[key] = value;
    }
    console.log('LocalStorage contents:', storage);
    return storage;
  },

  // Clear all auth-related localStorage
  clearAuthStorage: () => {
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth') || key.includes('supabase') || key.includes('sb-'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log('Removed localStorage key:', key);
    });
    
    console.log('Cleared auth storage. Removed keys:', keysToRemove);
    return keysToRemove;
  },

  // Check if URL contains recovery parameters
  isRecoveryURL: () => {
    const url = new URL(window.location.href);
    const hasToken = url.searchParams.has('token');
    const hasType = url.searchParams.has('type');
    const type = url.searchParams.get('type');
    const hasAccessToken = url.searchParams.has('access_token');
    const hasRefreshToken = url.searchParams.has('refresh_token');
    const hasErrorCode = url.searchParams.has('error_code');
    const hasErrorDescription = url.searchParams.has('error_description');
    
    const result = {
      hasToken,
      hasType,
      type,
      hasAccessToken,
      hasRefreshToken, 
      hasErrorCode,
      hasErrorDescription,
      isRecovery: (hasToken && type === 'recovery') || hasAccessToken || hasRefreshToken
    };
    
    console.log('Recovery URL check:', result);
    return result;
  },

  // Get session information from Supabase
  getSessionInfo: async () => {
    try {
      const { authAPI } = await import('../utils/supabase.mjs');
      const result = await authAPI.getCurrentSession();
      console.log('Current session info:', result);
      return result;
    } catch (error) {
      console.error('Error getting session info:', error);
      return { success: false, error: error.message };
    }
  }
};

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  window.debugAuth = debugAuth;
} 