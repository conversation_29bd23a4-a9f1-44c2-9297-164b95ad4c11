/* ================= GEIST FONT GLOBAL CONFIGURATIONS ================= */

/* Global font family overrides using Geist */
* {
    font-family: 'Geist', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

/* Monospace elements should use Geist Mono */
code, 
pre, 
kbd, 
samp,
.font-mono,
.monospace,
textarea.font-mono,
input[type="text"].font-mono,
.code-block,
.terminal,
.console {
    font-family: 'Geist Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;
    font-feature-settings: 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
}

/* Specific Geist font weight classes */
.font-geist-thin { font-weight: 100; }
.font-geist-extralight { font-weight: 200; }
.font-geist-light { font-weight: 300; }
.font-geist-normal { font-weight: 400; }
.font-geist-medium { font-weight: 500; }
.font-geist-semibold { font-weight: 600; }
.font-geist-bold { font-weight: 700; }
.font-geist-extrabold { font-weight: 800; }
.font-geist-black { font-weight: 900; }

/* Geist Mono specific weight classes */
.font-geist-mono-thin { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 100; 
}
.font-geist-mono-extralight { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 200; 
}
.font-geist-mono-light { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 300; 
}
.font-geist-mono-normal { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 400; 
}
.font-geist-mono-medium { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 500; 
}
.font-geist-mono-semibold { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 600; 
}
.font-geist-mono-bold { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 700; 
}
.font-geist-mono-extrabold { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 800; 
}
.font-geist-mono-black { 
    font-family: 'Geist Mono', monospace !important; 
    font-weight: 900; 
}

/* Enhanced readability for Geist fonts */
.geist-optimized {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Apply optimization to body by default */
body {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Debug mode indicators should use Geist Mono */
.debug-indicator,
.debug-toggle-btn,
.debug-message-banner {
    font-family: 'Geist Mono', monospace !important;
}

/* Code-like elements in the dashboard */
.generation-details-modal code,
.generation-details-modal .code-block,
.api-response,
.json-display {
    font-family: 'Geist Mono', monospace !important;
    font-feature-settings: 'liga' 1, 'calt' 1;
}

/* Ensure consistent spacing and readability */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Geist', ui-sans-serif, system-ui !important;
    font-feature-settings: 'kern' 1;
    letter-spacing: -0.025em;
}

/* Button text optimization */
button, .btn {
    font-family: 'Geist', ui-sans-serif, system-ui !important;
    font-feature-settings: 'kern' 1;
}

/* Input and form elements */
input, textarea, select {
    font-family: 'Geist', ui-sans-serif, system-ui !important;
}

/* Special case for prompt input when in typewriter mode */
.prompt-textarea.improving-typewriter,
.prompt-textarea.font-mono {
    font-family: 'Geist Mono', ui-monospace, SFMono-Regular !important;
    font-feature-settings: 'liga' 1, 'calt' 1, 'zero' 1;
    font-variant-numeric: slashed-zero;
}

/* Ensure Geist Mono for all suggestion chips */
.prompt-suggestion-chip,
.suggestion-chip,
.code-suggestion {
    font-family: 'Geist Mono', monospace !important;
    font-feature-settings: 'liga' 1, 'calt' 1;
} 