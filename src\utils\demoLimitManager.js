/**
 * Demo Limit Manager
 * Handles generation limits and tracking for demo users
 */

const DEMO_GENERATION_LIMIT = 20;

/**
 * Get localStorage keys for a specific demo account
 * @param {string} accountId - Demo account ID
 * @returns {object} Object with count and first generation keys
 */
const getDemoKeys = (accountId = null) => {
    // If no accountId, try to get current demo user
    if (!accountId) {
        try {
            const demoUser = JSON.parse(localStorage.getItem('thumbspark_demo_user') || '{}');
            accountId = demoUser.demoAccountId || demoUser.id || 'default';
        } catch (error) {
            accountId = 'default';
        }
    }
    
    return {
        countKey: `thumbspark_demo_generations_count_${accountId}`,
        firstGenerationKey: `thumbspark_demo_first_generation_${accountId}`
    };
};

/**
 * Get the current generation count for demo user
 * @param {string} accountId - Optional account ID
 * @returns {number} Current generation count
 */
export const getDemoGenerationCount = (accountId = null) => {
    try {
        const { countKey } = getDemoKeys(accountId);
        const count = localStorage.getItem(countKey);
        return count ? parseInt(count, 10) : 0;
    } catch (error) {
        return 0;
    }
};

/**
 * Get remaining generations for demo user
 * @param {string} accountId - Optional account ID
 * @returns {number} Remaining generations
 */
export const getRemainingGenerations = (accountId = null) => {
    const count = getDemoGenerationCount(accountId);
    return Math.max(0, DEMO_GENERATION_LIMIT - count);
};

/**
 * Check if demo user has reached the generation limit
 * @param {string} accountId - Optional account ID
 * @returns {boolean} True if limit reached
 */
export const hasReachedDemoLimit = (accountId = null) => {
    return getDemoGenerationCount(accountId) >= DEMO_GENERATION_LIMIT;
};

/**
 * Increment the demo generation count
 * @param {string} accountId - Optional account ID
 * @returns {number} New generation count
 */
export const incrementDemoCount = (accountId = null) => {
    try {
        const { countKey, firstGenerationKey } = getDemoKeys(accountId);
        const currentCount = getDemoGenerationCount(accountId);
        const newCount = currentCount + 1;
        
        localStorage.setItem(countKey, newCount.toString());
        
        // Track first generation timestamp for analytics
        if (newCount === 1) {
            localStorage.setItem(firstGenerationKey, new Date().toISOString());
        }
        
        return newCount;
    } catch (error) {
        return getDemoGenerationCount(accountId);
    }
};

/**
 * Reset demo generation count (for testing or admin purposes)
 * @param {string} accountId - Optional account ID
 */
export const resetDemoCount = (accountId = null) => {
    try {
        const { countKey, firstGenerationKey } = getDemoKeys(accountId);
        localStorage.removeItem(countKey);
        localStorage.removeItem(firstGenerationKey);
    } catch (error) {
        // Silently handle error
    }
};

/**
 * Get demo usage statistics
 * @param {string} accountId - Optional account ID
 * @returns {object} Usage statistics
 */
export const getDemoUsageStats = (accountId = null) => {
    try {
        const { firstGenerationKey } = getDemoKeys(accountId);
        const count = getDemoGenerationCount(accountId);
        const firstGeneration = localStorage.getItem(firstGenerationKey);
        const remaining = getRemainingGenerations(accountId);
        const isLimitReached = hasReachedDemoLimit(accountId);
        
        return {
            totalGenerations: count,
            remainingGenerations: remaining,
            limit: DEMO_GENERATION_LIMIT,
            isLimitReached,
            firstGenerationDate: firstGeneration ? new Date(firstGeneration) : null,
            progressPercentage: Math.round((count / DEMO_GENERATION_LIMIT) * 100)
        };
    } catch (error) {
        return {
            totalGenerations: 0,
            remainingGenerations: DEMO_GENERATION_LIMIT,
            limit: DEMO_GENERATION_LIMIT,
            isLimitReached: false,
            firstGenerationDate: null,
            progressPercentage: 0
        };
    }
};

/**
 * Get user-friendly message based on remaining generations
 * @param {string} accountId - Optional account ID
 * @returns {string} Status message
 */
export const getDemoStatusMessage = (accountId = null) => {
    const remaining = getRemainingGenerations(accountId);
    
    if (remaining === 0) {
        return "Demo limit reached! 🎯 You've generated 20 amazing thumbnails.";
    } else if (remaining <= 3) {
        return `⚡ Only ${remaining} generation${remaining === 1 ? '' : 's'} left in your demo!`;
    } else if (remaining <= 7) {
        return `🔥 ${remaining} generations remaining in your demo.`;
    } else {
        return `✨ ${remaining} generations available in your demo.`;
    }
};

/**
 * Check if user is a demo user
 * @returns {boolean} True if demo user
 */
export const isDemoUser = () => {
    try {
        const demoUser = localStorage.getItem('thumbspark_demo_user');
        return !!demoUser;
    } catch (error) {
        return false;
    }
}; 