/**
 * Demo Accounts Management System
 * Handles multiple demo accounts with individual generation limits
 */

// Demo account configurations
export const DEMO_ACCOUNTS = [
    {
        id: 'demo-user-1',
        email: '<EMAIL>',
        password: 'demo123',
        name: 'Demo User',
        displayName: 'Demo User',
        description: 'Primary demo account'
    },
    {
        id: 'demo-user-2', 
        email: '<EMAIL>',
        password: 'test123',
        name: 'Test User',
        displayName: 'Test User',
        description: 'Secondary demo account'
    },
    {
        id: 'demo-user-3',
        email: '<EMAIL>', 
        password: 'guest123',
        name: 'Guest User',
        displayName: 'Guest User',
        description: 'Guest demo account'
    }
];

/**
 * Validate demo account credentials
 * @param {string} email - Email address
 * @param {string} password - Password
 * @returns {Object|null} Demo user object if valid, null if invalid
 */
export const validateDemoCredentials = (email, password) => {
    const account = DEMO_ACCOUNTS.find(acc => 
        acc.email.toLowerCase() === email.toLowerCase() && 
        acc.password === password
    );
    
    if (!account) return null;
    
    // Create user object with required properties
    return {
        id: account.id,
        email: account.email,
        name: account.name,
        user_metadata: {
            full_name: account.displayName
        },
        created_at: new Date().toISOString(),
        isDemo: true,
        demoAccountId: account.id // Track which demo account this is
    };
};

/**
 * Get demo account info by email
 * @param {string} email - Email address
 * @returns {Object|null} Account info or null
 */
export const getDemoAccountByEmail = (email) => {
    return DEMO_ACCOUNTS.find(acc => acc.email.toLowerCase() === email.toLowerCase());
};

/**
 * Get all available demo accounts (for display purposes)
 * @returns {Array} Array of demo account info (without passwords)
 */
export const getAvailableDemoAccounts = () => {
    return DEMO_ACCOUNTS.map(acc => ({
        id: acc.id,
        email: acc.email,
        name: acc.name,
        displayName: acc.displayName,
        description: acc.description
        // Note: password is excluded for security
    }));
}; 