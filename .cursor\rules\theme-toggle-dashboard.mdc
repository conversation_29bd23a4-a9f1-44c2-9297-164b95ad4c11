---
description: 
globs: 
alwaysApply: true
---
Implement a new “Theme” setting in the Account Overview section of the dashboard.

- Provide a toggle or segmented control for Light/Dark mode selection.
- The Light theme must:
    • Use the same color palette as the reservation pages for primary, secondary, and background colors.
    • Adjust backgrounds, cards, and overlays for maximum compatibility with Hero UI and macOS glass/liquid design (e.g., soft gradients, subtle blurs, premium shadows).
    • Ensure all text, icons, and controls have sufficient contrast and follow accessibility guidelines (WCAG AA+).
    • Use glassy, semi-transparent cards with smooth transitions and premium border radii.
    • Animate theme transitions (fade/blur) for a polished experience.
- The Dark theme remains as currently implemented, but ensure consistency in spacing, border, and shadow logic.
- Theme selection must persist (localStorage or user profile).
- All theme logic should be managed via ThemeContext for global access.
- Test on all dashboard subpages for visual consistency.