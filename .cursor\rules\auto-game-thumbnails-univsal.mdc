---
description: 
globs: 
alwaysApply: false
---
auto-game-thumbnails-universal
---
ruleType: always
---

# 🎮 Universal Auto-Game Thumbnail Enhancement

## Applies To
- /src/utils/promptFormatter.js
- /src/utils/promptBuilder.ts
- /src/hooks/usePromptEnhancer.ts
- /src/templates/gaming/

---

## Trigger Conditions
- If the user prompt mentions FPS/Battle Royale games (e.g., Fortnite, Warzone, Call of Duty, Valorant, PUBG, Apex, CS2)
- OR includes comparison/versus keywords (`vs`, `1v1`, `Noob vs Pro`, `Showdown`)

---

## Behavior

- **Always** apply cinematic, game-authentic thumbnail logic (split-screen, in-game characters, dramatic lighting, brand logos, etc.) to the prompt, regardless of whether text overlay is enabled or not.
- If text overlay is ON, add bold, uppercase, glowing text overlay as per the video topic.
- If text overlay is OFF, explicitly instruct: “Do NOT include any text overlay,” but keep all other cinematic/game-authentic elements.
- Ensure brand logos are included for product/game/device comparisons if available.
- This logic must apply to all video topics/prompts matching the trigger, not just when text overlay is enabled.

---

## Example

**User Input:**  
`PUBG VS Warzone`

**With Text Overlay ON:**  
- Cinematic split-screen, PUBG and Warzone characters, dramatic background, glowing "PUBG VS WARZONE" text, brand logos.

**With Text Overlay OFF:**  
- Cinematic split-screen, PUBG and Warzone characters, dramatic background, brand logos, **no text overlay**.

---

## Notes

- Uses GPT-image-1 latest visual alignment for texture, contrast, and style.
- Ensures visual parity and quality regardless of text overlay setting.
- Reduces risk of generic/boring backgrounds for versus/game prompts.

---