---
description: 
globs: 
alwaysApply: false
---
---
title: icon-usage-limitation
id: icon-usage-limitation.mdc
ruleType: manual
---

## 🧠 Feature: Controlled Icon Usage in Thumbnail Generation

### Objective
Prevent excessive or distracting icon use in AI-generated thumbnails, especially for tech and design topics. Ensure brand authenticity and visual clarity by strictly limiting icon placement and style.

---

## Applies To
- /src/utils/promptBuilder.ts
- /src/hooks/usePromptEnhancer.ts
- /src/components/ControlPanel.jsx
- /src/templates/tech/
- /src/templates/design/

---

## 🎯 Problem
When icon mode is enabled, the current system may overuse icons—especially 3D or decorative ones—leading to cluttered, unprofessional thumbnails. Tech and design topics are particularly sensitive to this, and brand logos may be replaced with generic icons, reducing authenticity.

---

## ✅ Solution: Icon Usage Limitation Rules

- **Official Brand Logos:**  
  - Always use the official logo for the main brand, product, or platform.  
  - Never substitute the main logo with a generic or stylized icon.

- **Annotation Icons:**  
  - Limit additional icons to essential, high-impact annotations only (e.g., a single red arrow, highlight, or pointer).
  - Do not use more than one annotation icon per thumbnail unless contextually justified.

- **No Icon Overload:**  
  - Avoid using multiple 3D, decorative, or unrelated icons.
  - Never exaggerate with excessive iconography—icons should clarify, not distract.

- **Visual Focus:**  
  - The thumbnail must remain clean, focused, and professional.
  - Icons should serve only to emphasize or clarify key elements, not as decoration.

---

## 🛠️ Implementation Notes

- Update prompt construction logic to:
  - Insert only the official brand logo for the main subject.
  - Add at most one annotation icon (e.g., red arrow) if contextually relevant.
  - Exclude all other icons, especially 3D or decorative ones, unless explicitly required by the prompt.
- Add checks in the UI and prompt enhancer to enforce these limits.
- For tech and design templates, apply stricter icon controls by default.

---

## ✅ Done When

- Thumbnails for tech and design topics always use the official brand logo and at most one annotation icon.
- No thumbnail contains excessive, unrelated, or decorative icons.
- Visual clarity and brand authenticity are consistently maintained.

---

To activate this in Cursor: @icon-usage-limitation