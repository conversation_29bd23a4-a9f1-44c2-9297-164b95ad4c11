---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
@business-templates-expansion
ruleId: business-templates-expansion
description: >
  Add 10+ high-quality, visually diverse business templates to the Business category in App.jsx. Each template must use a cinematic, modern prompt, pyramid-shaped text overlays for 3+ words, and randomized design/graphics settings for variety.

appliesTo:
  - /src/App.jsx

ruleType: always

implementationNotes: |
  - Each template must specify a unique business topic (e.g., dropshipping, money secrets, crypto, success, comparison).
  - Use pyramid-shaped overlayText for 3+ word titles.
  - Randomize includePerson, includeIcons, selectedExpression, selectedTextSize, and selectedFontFamily.
  - Always enable textOverlay and set userPromptFocus to match the main concept.
  - Use clear, cinematic, and business-authentic language in promptBase.
  - Provide a short label and color for templateImagePlaceholder.

sampleTemplate: |
  {
    id: "crypto-explosion",
    name: "Crypto Explosion",
    description: "Bitcoin and Ethereum coins, glowing green arrow, and stock chart. Great for crypto growth videos.",
    promptBase: "YouTube thumbnail with Bitcoin and Ethereum coins glowing, upward green arrow, stock market candlestick chart background, bold text overlay in pyramid shape: 'CRYPTO', 'EXPLOSION!'.",
    settingsToApply: {
      includeIcons: true,
      textOverlay: true,
      overlayText: "CRYPTO\nEXPLOSION!",
      selectedTextSize: "Large",
      selectedFontFamily: "Impact",
      userPromptFocus: "Crypto Explosion"
    },
    templateImagePlaceholder: { text: "Crypto", bgColor: "bg-yellow-600" }
  }

acceptanceCriteria:
  - At least 10 new business templates are added.
  - Each template uses a unique,Vibrant , cinematic prompt and pyramid-shaped overlay text.
  - Design and graphics settings are varied for each template.
  - All templates are compatible with the app’s design and graphics controls.