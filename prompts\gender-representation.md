# Gender Representation in Thumbnails

## Overview
This document outlines best practices for gender representation in YouTube thumbnails using our generator. The goal is to create diverse, inclusive thumbnails that respect viewer preferences while maximizing engagement.

## Gender Options UI
The application provides four gender representation options:

- **🤖 Auto**: The AI decides the most appropriate gender based on the context of your prompt
- **👨 Male**: Explicitly generates a male-presenting figure 
- **👩 Female**: Explicitly generates a female-presenting figure
- **🧑 Non-binary**: Generates a figure with gender-neutral characteristics

## Prompt Engineering Best Practices

### When to Specify Gender
- **Topic-relevant**: Choose a gender that makes sense for your content's subject matter
- **Brand consistency**: Match your channel's established presenter or personality
- **Target demographic**: Consider your audience's preferences and expectations
- **A/B testing**: Try different gender representations to measure engagement

### Gender + Expression Combinations
Pair gender selection with appropriate expressions for maximum impact:

| Content Category | Recommended Gender | Expression Pairing |
|-----------------|-------------------|-------------------|
| Gaming | Any | Surprised or Excited |
| Tech Reviews | Any | Thinking or Surprised |
| Business/Finance | Any | Serious or Confident |
| Lifestyle/Beauty | Any | Happy or Excited |
| Educational | Any | Thinking or Confident |

### Prompt Examples

#### Auto Gender Examples:
```
Create a YouTube thumbnail with a person reacting to the latest iPhone announcement
```

#### Specific Gender Examples:
```
Create a YouTube thumbnail with a female tech reviewer examining the new MacBook
Create a YouTube thumbnail with a male chef preparing a gourmet meal
Create a YouTube thumbnail with a non-binary person discussing inclusive gaming
```

## Technical Implementation
The gender selection is implemented in the prompt formatter as follows:

```javascript
// Gender handling in promptFormatter.js
let genderDesc = "a human figure";
if (selectedGender === "Male") {
    genderDesc = "a male human figure";
} else if (selectedGender === "Female") {
    genderDesc = "a female human figure";
} else if (selectedGender === "Non-binary") {
    genderDesc = "a non-binary human figure";
}
```

## Future Improvements
- Add more diverse representation options
- Improve AI understanding of gender presentation nuance
- Develop customization for cultural context
- Support for uploaded reference images of diverse individuals 