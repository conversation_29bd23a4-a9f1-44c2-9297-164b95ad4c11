/**
 * Geist Font Configuration for Thumbspark
 * Based on Vercel's official Geist font implementation
 * https://github.com/vercel/geist-font
 */

// Since we're using CDN approach for this MVP, we'll define CSS variables
// that match Vercel's implementation pattern

export const initializeGeistFonts = () => {
    // Create CSS variables for Geist fonts to match Vercel's pattern
    const style = document.createElement('style');
    style.textContent = `
        :root {
            --font-geist-sans: 'Geist', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            --font-geist-mono: 'Geist Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
        }
        
        /* Apply <PERSON><PERSON><PERSON> as default font */
        html, body {
            font-family: var(--font-geist-sans);
            font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
            font-variant-ligatures: common-ligatures;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Override Tailwind's default font stack */
        .font-sans {
            font-family: var(--font-geist-sans) !important;
        }
        
        .font-mono {
            font-family: var(--font-geist-mono) !important;
        }
    `;
    
    document.head.appendChild(style);
    
    // Add font loading optimization
    const preloadGeistSans = document.createElement('link');
    preloadGeistSans.rel = 'preload';
    preloadGeistSans.as = 'font';
    preloadGeistSans.type = 'font/woff2';
    preloadGeistSans.href = 'https://fonts.gstatic.com/s/geist/v1/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7W0Q5n-wU.woff2';
    preloadGeistSans.crossOrigin = 'anonymous';
    
    const preloadGeistMono = document.createElement('link');
    preloadGeistMono.rel = 'preload';
    preloadGeistMono.as = 'font';
    preloadGeistMono.type = 'font/woff2';
    preloadGeistMono.href = 'https://fonts.gstatic.com/s/geistmono/v1/gyB4hws1JdgnKy56GB825bb3pXWUEP0L.woff2';
    preloadGeistMono.crossOrigin = 'anonymous';
    
    document.head.appendChild(preloadGeistSans);
    document.head.appendChild(preloadGeistMono);
    
    console.log('✅ Geist fonts initialized successfully');
};

// Font weight utilities that match Geist's available weights
export const GeistWeights = {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
};

// Helper function to apply Geist font to specific elements
export const applyGeistFont = (element, variant = 'sans', weight = 'normal') => {
    if (!element) return;
    
    const fontFamily = variant === 'mono' 
        ? 'var(--font-geist-mono)' 
        : 'var(--font-geist-sans)';
    
    element.style.fontFamily = fontFamily;
    element.style.fontWeight = GeistWeights[weight] || weight;
    element.style.fontFeatureSettings = variant === 'mono' 
        ? "'liga' 1, 'calt' 1, 'zero' 1"
        : "'kern' 1, 'liga' 1, 'calt' 1";
};

// Utility to check if Geist fonts are loaded
export const checkGeistFontsLoaded = () => {
    return new Promise((resolve) => {
        if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
                const geistSansLoaded = document.fonts.check('16px Geist');
                const geistMonoLoaded = document.fonts.check('16px "Geist Mono"');
                
                console.log('Geist Sans loaded:', geistSansLoaded);
                console.log('Geist Mono loaded:', geistMonoLoaded);
                
                resolve(geistSansLoaded && geistMonoLoaded);
            });
        } else {
            // Fallback for browsers without Font Loading API
            setTimeout(() => resolve(true), 3000);
        }
    });
};

// Enhanced font loading with fallback
export const loadGeistFontsWithFallback = async () => {
    try {
        // Initialize fonts
        initializeGeistFonts();
        
        // Wait for fonts to load
        const fontsLoaded = await checkGeistFontsLoaded();
        
        if (fontsLoaded) {
            console.log('✅ All Geist fonts loaded successfully');
            
            // Add loaded class to body for CSS transitions
            document.body.classList.add('geist-fonts-loaded');
            
            // Dispatch custom event
            window.dispatchEvent(new CustomEvent('geist-fonts-loaded'));
        } else {
            console.warn('⚠️ Some Geist fonts may not have loaded properly');
        }
        
        return fontsLoaded;
    } catch (error) {
        console.error('❌ Error loading Geist fonts:', error);
        return false;
    }
};

// Export font configuration for Tailwind
export const TailwindGeistConfig = {
    fontFamily: {
        'sans': ['var(--font-geist-sans)'],
        'mono': ['var(--font-geist-mono)'],
        'geist': ['var(--font-geist-sans)'],
        'geist-mono': ['var(--font-geist-mono)']
    }
}; 