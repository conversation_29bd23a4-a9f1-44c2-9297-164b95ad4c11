# Demo Thumbnails

This directory contains sample thumbnail images used when the application is running in demo mode on Netlify.

Since we can't include the OpenAI API key in client-side code for security reasons, these pre-generated thumbnails provide a fallback to demonstrate the application's capabilities.

## Usage

When the application detects it's running on Netlify, it will randomly select one of these thumbnails instead of making an API call to OpenAI.

## Adding More Thumbnails

To add more demo thumbnails:

1. Generate thumbnails using the local version of the application
2. Save them as JPG or PNG files in this directory
3. Name them descriptively (e.g., `tech-review.jpg`, `gaming-reaction.jpg`)
4. Make sure they are 1280x720 pixels (16:9 aspect ratio)

## Credits

These thumbnails were generated using OpenAI's GPT-4 Vision model.