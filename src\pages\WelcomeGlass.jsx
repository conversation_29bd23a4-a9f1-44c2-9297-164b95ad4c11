import React, { useState, useRef, useEffect } from 'react'
import { authAPI, supabase } from '../utils/supabase.mjs'
import { enhancedAuth, rememberMeAuth } from '../utils/auth.js'
import { AuthThemeToggle } from '../components/ui/AuthThemeToggle.jsx'

/**
 * Welcome Glass Screen Component - MacOS Liquid Glass Version
 * Default authentication page with unified glass design and enhanced UX
 * 
 * @param {Object} props
 * @param {Function} props.onAuthenticated - Callback for when login is completed
 * @param {Function} props.onNavigateToSignUp - Navigate to signup page
 * @param {Function} props.onNavigateToForgotPassword - Navigate to forgot password page
 * @param {boolean} props.isLightTheme - Current theme state
 * @param {Function} props.onToggleTheme - Theme toggle handler
 */
const WelcomeGlass = ({ onAuthenticated, onNavigateToSignUp, onNavigateToForgotPassword, isLightTheme, onToggleTheme }) => {
    // State for form inputs
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    
    // State for Remember Me checkbox
    const [rememberMe, setRememberMe] = useState(false);
    
    // State for validation errors
    const [errors, setErrors] = useState({
        email: '',
        password: '',
        auth: ''
    });

    // Touched fields tracking for validation
    const [touched, setTouched] = useState({
        email: false,
        password: false
    });
    
    // Refs for form fields
    const inputRefs = {
        email: useRef(null),
        password: useRef(null)
    };
    
    // State for password visibility and loading
    const [showPassword, setShowPassword] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    
    // Auto-fill email if user has saved preferences
    useEffect(() => {
        const userPrefs = rememberMeAuth.getUserPreferences();
        if (userPrefs && userPrefs.email) {
            setFormData(prev => ({ ...prev, email: userPrefs.email }));
            setRememberMe(true);
        }
    }, []);

    // Validation functions
    const validateEmail = (emailInput) => {
        const email = emailInput ? emailInput.trim() : '';
        if (!email) return "Email is required";
        const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i;
        if (!emailRegex.test(email)) return "Invalid email address";
        return "";
    };
    
    const validatePassword = (password) => {
        if (!password) return "Password is required";
        return "";
    };
    
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        
        setFormData(prev => ({ ...prev, [name]: value }));
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage, auth: '' }));
    };
    
    const handleBlur = (e) => {
        const { name, value } = e.target;
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        setErrors(prev => ({ ...prev, [name]: errorMessage }));
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        const fieldsToTouch = { email: true, password: true };
        setTouched(prev => ({ ...prev, ...fieldsToTouch }));

        const currentErrors = {
            email: validateEmail(formData.email),
            password: validatePassword(formData.password)
        };
        setErrors(prev => ({ ...prev, ...currentErrors, auth: '' }));

        if (currentErrors.email || currentErrors.password) {
            if (currentErrors.email) inputRefs.email.current?.focus();
            else if (currentErrors.password) inputRefs.password.current?.focus();
            return;
        }
        
        setIsSubmitting(true);
        
        try {
            const result = await enhancedAuth.signIn(supabase, formData.email, formData.password, rememberMe);
            
            if (result.success && result.user) {
                console.log(`✅ Login successful${rememberMe ? ' with Remember Me enabled' : ''}`);
                onAuthenticated(result.user);
            } else {
                setErrors(prev => ({
                    ...prev,
                    auth: result.error || 'Invalid email or password. Please check your credentials and try again.'
                }));
            }
        } catch (error) {
            console.error('Authentication error:', error);
            setErrors(prev => ({
                ...prev,
                auth: 'An unexpected error occurred. Please try again.'
            }));
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleGoogleSignIn = async () => {
        setIsSubmitting(true);
        setErrors(prev => ({ ...prev, auth: '' }));
        
        try {
            console.log('🔄 Starting Google sign-in...');
            const result = await authAPI.signInWithGoogle();
            
            if (result.success) {
                console.log('✅ Google sign-in initiated successfully');
            } else {
                console.error('❌ Google sign-in failed:', result.error);
                setErrors(prev => ({
                    ...prev,
                    auth: result.error || 'Failed to sign in with Google. Please try again.'
                }));
                setIsSubmitting(false);
            }
        } catch (error) {
            console.error('Google sign-in error:', error);
            setErrors(prev => ({
                ...prev,
                auth: 'An unexpected error occurred with Google sign-in. Please try again.'
            }));
            setIsSubmitting(false);
        }
    };

    return (
        <div className="auth-glass-container" id="welcome-glass-container">
            {/* Theme Toggle Button */}
            <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />

            {/* Centered Glass Card */}
            <div className="flex items-center justify-center min-h-screen p-4">
                <div className="auth-glass-card w-full max-w-md p-8" id="welcome-glass-card">
                    {/* Logo */}
                    <img 
                        src="/assets/main-logo.svg" 
                        alt="Thumbspark Logo" 
                        className="auth-glass-logo" 
                        draggable="false" 
                    />

                    {/* Title and Slogan - Updated per requirements */}
                    <h1 className="auth-glass-title">Log in</h1>
                    <p className="auth-glass-subtitle">Welcome back</p>

                    {/* Global Error Message */}
                    {errors.auth && (
                        <div className="auth-glass-global-error" id="welcome-glass-global-error">
                            <div className="auth-glass-global-error-text">
                                <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                {errors.auth}
                            </div>
                        </div>
                    )}

                    {/* Login Form */}
                    <form onSubmit={handleSubmit} className="space-y-4" id="welcome-glass-form">
                        {/* Google Sign In Button - Dark Theme */}
                        <button
                            type="button"
                            onClick={handleGoogleSignIn}
                            disabled={isSubmitting}
                            className="auth-glass-google-btn-dark"
                            id="welcome-glass-google-btn"
                        >
                            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="currentColor"/>
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="currentColor"/>
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="currentColor"/>
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="currentColor"/>
                            </svg>
                            Continue with Google
                        </button>
                        
                        {/* Divider */}
                        <div className="auth-glass-divider">
                            <span className="auth-glass-divider-text">or</span>
                        </div>

                        {/* Email Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="email" className="auth-glass-label">
                                Email Address
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:letter-linear"></span>
                                <input 
                                    type="email" 
                                    id="welcome-glass-email-input" 
                                    name="email" 
                                    value={formData.email} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.email} 
                                    required
                                    className={`auth-glass-input ${errors.email && touched.email ? 'error' : ''}`}
                                    placeholder="Enter your email address"
                                />
                            </div>
                            {errors.email && touched.email && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.email}
                                </div>
                            )}
                        </div>

                        {/* Password Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="password" className="auth-glass-label">
                                Password
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:lock-keyhole-linear"></span>
                                <input 
                                    type={showPassword ? "text" : "password"} 
                                    id="welcome-glass-password-input" 
                                    name="password" 
                                    value={formData.password} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.password} 
                                    required
                                    className={`auth-glass-input ${errors.password && touched.password ? 'error' : ''}`}
                                    placeholder="Enter your password"
                                />
                                <button 
                                    type="button" 
                                    onClick={() => setShowPassword(!showPassword)} 
                                    className="auth-glass-password-toggle"
                                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                                >
                                    <span className="iconify" data-icon={showPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                </button>
                            </div>
                            {errors.password && touched.password && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.password}
                                </div>
                            )}
                        </div>

                        {/* Remember Me and Forgot Password */}
                        <div className="flex items-center justify-between">
                            <div className="auth-glass-checkbox-container">
                                <input
                                    id="welcome-glass-remember-checkbox"
                                    name="remember-me"
                                    type="checkbox"
                                    checked={rememberMe}
                                    onChange={(e) => setRememberMe(e.target.checked)}
                                    className="auth-glass-checkbox"
                                />
                                <label htmlFor="welcome-glass-remember-checkbox" className="auth-glass-checkbox-label">
                                    Remember me
                                </label>
                            </div>
                            <button 
                                type="button"
                                onClick={onNavigateToForgotPassword}
                                className="auth-glass-link"
                            >
                                Forgot password?
                            </button>
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={isSubmitting || (errors.email && touched.email) || (errors.password && touched.password)}
                            className="auth-glass-cta-btn"
                            id="welcome-glass-signin-btn"
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Signing In...</span>
                                </>
                            ) : (
                                'Sign In'
                            )}
                        </button>

                        {/* Additional Links */}
                        <div className="text-center">
                            <span className="text-gray-400 text-sm">
                                Don't have an account?{' '}
                                <button 
                                    type="button"
                                    onClick={onNavigateToSignUp}
                                    className="auth-glass-link"
                                >
                                    Sign up here
                                </button>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export { WelcomeGlass }; 