---
description: 
globs: 
alwaysApply: true
---
@text-overlay-refresh-button
ruleId: text-overlay-refresh-button
description: >
  Adds a “Refresh Text Overlay” icon button (solar:refresh-square-linear) beside the edit button in the text overlay area. When clicked, it generates a new, contextually relevant text overlay suggestion based on the current video topic prompt. Only the text overlay value is updated; all other controls remain unchanged.

appliesTo:
  - /src/components/ControlPanel.jsx
  - /src/utils/promptVariations.js
  - /src/styles/controls.css

ruleType: always

implementationNotes: |
  - Place the icon-only button directly beside the edit button in the text overlay area.
  - Use the solar:refresh-square-linear icon, styled as a compact, accessible button.
  - On click, call the smart suggestion logic to generate a new overlay headline based on the current video topic prompt.
  - Update only the text overlay value; do not change any other state or controls.
  - The button is always visible when text overlay is enabled.
  - Add ARIA label: "Refresh text overlay suggestion".
  - Ensure full keyboard and screen reader accessibility.
  - UI transitions and button states must match the app’s design system.
  - Do not auto-refresh on toggle; refresh only on user click.

acceptanceCriteria:
  - <PERSON><PERSON> appears beside the edit button in the text overlay area.
  - Clicking the button generates a new, relevant text overlay suggestion.
  - Only the text overlay value is updated.
  - Button is accessible and visually consistent.
  - No other controls or state are affected.