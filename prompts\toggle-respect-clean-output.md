---
title: Respect Toggle State: Clean Background Output
id: toggle-respect-clean-output.md
---

## 🎯 Feature: Respect Toggles for Text Overlay and Person — Clean Output Rendering

### Objective
Ensure the AI thumbnail generation logic respects the user's intent when the **Text Overlay** and/or **Person** toggles are switched **OFF**. This is critical for creators who wish to export a clean background-only canvas for further editing in external tools (e.g., Photoshop, Figma, Canva).

---

## 🧩 Behavior Rules

### ✅ Text Overlay: OFF
- The generated image must **not include any text or headlines**
- The prompt injection **must not reference**:
  - `title`, `text_overlay`, `font`, `case`, or `position`
  - Avoid terms like “add text” or “text overlay”

### ✅ Include Person: OFF
- Do **not include** any human face, body, or character in the scene
- Remove mentions of:
  - `"subject": { "person": ... }`
  - facial expressions, emotion cues, or pose references

### ✅ Use Case: “Background-Only Thumbnail Mode”
- When both toggles are OFF:
  - Render a high-quality cinematic or stylized **background-only thumbnail**
  - Perfect for YouTubers who manually add branding in design tools

---

## 🧾 Example Prompt (When both OFF)
```text
Generate a cinematic YouTube thumbnail background in 1280x720 resolution with a vibrant gradient and dynamic light rays. No human figures or text overlays. Ensure it is compositionally balanced to allow later text placement. High contrast, with stylized cinematic lighting. Theme: Gaming Meltdown.

✅ Done When:

No visible text or titles appear in the final thumbnail when text toggle is OFF
No human characters appear when person toggle is OFF
Prompt builder respects toggle logic during generation
Developer Notes

Wrap logic in if (includeTextOverlay) / if (includePerson) checks inside buildPrompt()
Helpful for users using the app as a thumbnail background generator only
Bonus UX Tip 💡

Show a mini preview badge like “Clean Background Mode” or “Text-Free Export” near the Generate button to indicate that both toggles are OFF.