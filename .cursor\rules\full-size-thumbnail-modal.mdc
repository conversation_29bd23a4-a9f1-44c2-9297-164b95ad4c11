---
description: 
globs: 
alwaysApply: false
---
---
ruleId: full-size-thumbnail-modal
description: Adds a full-size preview modal for generated thumbnails, triggered by a maximize icon button, following Hero UI style and UX best practices.
ruleType: new_feature
appliesTo:
  - /src/components/ThumbnailPreview.jsx
  - /src/components/ui/Modal.jsx
  - /src/styles/preview.css
---

## 🎯 Feature: Full-Size Thumbnail Preview Modal

### 1. Trigger Button
- Add a button (with `solar:maximize-square-minimalistic-linear` icon) to the thumbnail preview area.
- But<PERSON> should be visible only when a thumbnail is present.
- Style: Hero UI button, dark mode, hover/focus states.

### 2. Modal Overlay
- On button click, open a modal overlay centered on the screen.
- <PERSON><PERSON> displays the thumbnail at full 1280x720 (or max viewport size, maintaining aspect).
- Modal background uses `backdrop-filter: blur(8px)` and a semi-transparent dark overlay.
- <PERSON><PERSON> has a close button (Hero UI, top-right, accessible).

### 3. Animation & UX
- Use a smooth fade-in/fade-out transition for modal open/close.
- <PERSON><PERSON> is keyboard accessible (ESC to close, focus trap).
- Add ARIA labels for accessibility.

### 4. Consistency
- All elements follow Hero UI style guide and match other UI components.
- Modal and button are responsive and mobile-friendly.

### 5. Benefits
- Users can inspect thumbnails at full size for quality and legibility before download or use.

---