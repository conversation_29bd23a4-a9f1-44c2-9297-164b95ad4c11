/*
 * brandLogos.js
 * Utility helpers for detecting popular tech / design / AI tools in a user prompt.
 * If a brand name is detected, we can instruct the image generator to embed its official logo.
 *
 * NOTE: This is a lightweight list – add more brands as needed. Keywords should be lowercase.
 */

// === CONTEXT ANALYSIS FOR BRAND DISAMBIGUATION ===

/**
 * Gaming context keywords that indicate the prompt is about actual gameplay/gaming content
 */
const GAMING_CONTEXT_KEYWORDS = [
  // Gameplay & Actions
  'gameplay', 'playing', 'play', 'game', 'gaming', 'gamer', 'build', 'battle', 'fight', 'combat',
  'match', 'round', 'level', 'stage', 'mission', 'quest', 'campaign', 'multiplayer', 'online',
  'pvp', 'pve', 'raid', 'dungeon', 'boss', 'enemy', 'opponent', 'team', 'squad', 'clan', 'guild',
  
  // Game Content Types
  'montage', 'highlights', 'compilation', 'best moments', 'epic', 'fail', 'wins', 'victory',
  'defeat', 'clutch', 'headshot', 'kill', 'elimination', 'score', 'points', 'rank', 'ranking',
  'leaderboard', 'tournament', 'esports', 'competitive', 'ranked', 'casual', 'stream', 'streaming',
  
  // Gaming Reviews & Guides
  'review', 'walkthrough', 'tutorial', 'guide', 'tips', 'tricks', 'strategy', 'tactics',
  'how to', 'best', 'worst', 'top', 'tier list', 'comparison', 'vs', 'versus', 'better',
  'meta', 'build guide', 'loadout', 'setup', 'config', 'settings', 'optimization',
  
  // Game Features & Elements
  'character', 'hero', 'champion', 'skin', 'outfit', 'weapon', 'item', 'loot', 'drop',
  'map', 'location', 'area', 'zone', 'world', 'server', 'update', 'patch', 'dlc',
  'expansion', 'mod', 'modding', 'custom', 'event', 'season', 'battle pass',
  
  // Performance & Skills
  'pro', 'professional', 'skill', 'skilled', 'noob', 'beginner', 'advanced', 'master',
  'godlike', 'insane', 'crazy', 'amazing', 'unbelievable', 'record', 'world record',
  'speedrun', 'any%', 'glitchless', 'challenge', 'hardcore', 'difficulty'
];

/**
 * Text overlay position indicators to help determine logo placement
 */
const TEXT_OVERLAY_POSITION_KEYWORDS = {
  top: ['top', 'upper', 'above', 'header'],
  bottom: ['bottom', 'lower', 'below', 'footer'],
  left: ['left', 'left side', 'left corner'],
  right: ['right', 'right side', 'right corner'],
  center: ['center', 'middle', 'central', 'centered']
};

/**
 * Analyzes if a prompt is genuinely about gaming content vs just mentioning a game
 * @param {string} prompt - The user prompt to analyze
 * @param {string} gameName - The detected game name
 * @returns {Object} Gaming context analysis with confidence scores
 */
function analyzeGamingContext(prompt, gameName) {
  const lower = prompt.toLowerCase();
  
  // Count gaming context keywords
  const gamingScore = GAMING_CONTEXT_KEYWORDS.reduce((score, keyword) => {
    return score + (lower.includes(keyword) ? 1 : 0);
  }, 0);
  
  // Check if the game is the main subject (appears early in prompt or with strong context)
  const gamePosition = lower.indexOf(gameName.toLowerCase());
  const promptLength = prompt.length;
  const isEarlyMention = gamePosition < (promptLength * 0.3); // First 30% of prompt
  
  // Gaming-specific phrases that strongly indicate game content
  const explicitGamingPhrases = [
    `${gameName} gameplay`, `${gameName} montage`, `${gameName} highlights`,
    `${gameName} tutorial`, `${gameName} guide`, `${gameName} review`,
    `${gameName} tips`, `${gameName} tricks`, `${gameName} build`,
    `${gameName} battle`, `${gameName} match`, `${gameName} win`,
    `playing ${gameName}`, `${gameName} stream`, `${gameName} tournament`,
    `${gameName} vs`, `${gameName} comparison`, `best ${gameName}`,
    `${gameName} moments`, `${gameName} fails`, `${gameName} compilation`
  ];
  
  const hasExplicitGamingPhrase = explicitGamingPhrases.some(phrase => 
    lower.includes(phrase.toLowerCase())
  );
  
  // Non-gaming contexts where games might be mentioned
  const nonGamingIndicators = [
    'news', 'announcement', 'release date', 'trailer', 'cinematic',
    'movie', 'film', 'book', 'novel', 'story', 'lore', 'history',
    'developer', 'company', 'studio', 'publisher', 'industry',
    'sales', 'revenue', 'stock', 'market', 'business', 'lawsuit'
  ];
  
  const nonGamingScore = nonGamingIndicators.reduce((score, keyword) => {
    return score + (lower.includes(keyword) ? 1 : 0);
  }, 0);
  
  // Calculate confidence score
  let confidence = 0;
  let context = 'ambiguous';
  let reasoning = '';
  
  if (hasExplicitGamingPhrase) {
    context = 'gaming_content';
    confidence = 0.95;
    reasoning = 'Explicit gaming content phrase detected';
  } else if (gamingScore >= 3 && nonGamingScore === 0) {
    context = 'gaming_content';
    confidence = Math.min(0.9, 0.6 + (gamingScore * 0.05));
    reasoning = `Strong gaming context (${gamingScore} keywords), no non-gaming indicators`;
  } else if (gamingScore >= 2 && isEarlyMention && nonGamingScore <= 1) {
    context = 'gaming_content';
    confidence = 0.75;
    reasoning = `Good gaming context (${gamingScore} keywords), early mention, minimal non-gaming context`;
  } else if (nonGamingScore > gamingScore) {
    context = 'non_gaming';
    confidence = 0.8;
    reasoning = `Non-gaming context stronger (${nonGamingScore} vs ${gamingScore})`;
  } else if (gamingScore === 0 && !isEarlyMention) {
    context = 'non_gaming';
    confidence = 0.7;
    reasoning = 'No gaming context, late mention in prompt';
  } else {
    context = 'ambiguous';
    confidence = 0.4;
    reasoning = `Mixed or unclear context (gaming: ${gamingScore}, non-gaming: ${nonGamingScore})`;
  }
  
  return {
    context,
    confidence: Math.min(confidence, 1.0),
    gamingScore,
    nonGamingScore,
    isEarlyMention,
    hasExplicitGamingPhrase,
    reasoning
  };
}

/**
 * Determines optimal logo placement based on text overlay position and content layout
 * @param {string} prompt - The user prompt
 * @param {string} overlayText - The text overlay content (if any)
 * @param {string} overlayPosition - The text overlay position (if specified)
 * @returns {Object} Logo placement recommendation
 */
function determineLogoPlacement(prompt, overlayText = '', overlayPosition = '') {
  const lower = prompt.toLowerCase();
  const overlayLower = overlayText.toLowerCase();
  const positionLower = overlayPosition.toLowerCase();
  
  // Analyze text overlay position from explicit position setting
  let textPosition = 'unknown';
  if (positionLower.includes('top')) textPosition = 'top';
  else if (positionLower.includes('bottom')) textPosition = 'bottom';
  else if (positionLower.includes('left')) textPosition = 'left';
  else if (positionLower.includes('right')) textPosition = 'right';
  else if (positionLower.includes('center')) textPosition = 'center';
  
  // If no explicit position, try to infer from prompt content
  if (textPosition === 'unknown') {
    for (const [position, keywords] of Object.entries(TEXT_OVERLAY_POSITION_KEYWORDS)) {
      if (keywords.some(keyword => lower.includes(keyword))) {
        textPosition = position;
        break;
      }
    }
  }
  
  // Analyze content layout hints
  const layoutHints = {
    splitScreen: lower.includes('vs') || lower.includes('versus') || lower.includes('split'),
    comparison: lower.includes('comparison') || lower.includes('compare') || lower.includes('better'),
    singleSubject: lower.includes('solo') || lower.includes('single') || lower.includes('one'),
    multipleElements: lower.includes('multiple') || lower.includes('many') || lower.includes('various')
  };
  
  // Determine optimal logo position
  let logoPosition = 'bottom-right'; // Default
  let reasoning = 'Default bottom-right placement';
  
  if (textPosition === 'top' || textPosition === 'center') {
    // Text is top/center, logo can go either bottom corner
    logoPosition = 'bottom-right';
    reasoning = 'Text overlay is top/center, placing logo in bottom-right';
  } else if (textPosition === 'bottom') {
    // Text is bottom, avoid overlap - consider content layout
    if (layoutHints.splitScreen) {
      logoPosition = 'bottom-center';
      reasoning = 'Split-screen layout with bottom text, centering logo';
    } else {
      logoPosition = 'top-right';
      reasoning = 'Bottom text overlay, placing logo in top-right to avoid overlap';
    }
  } else if (textPosition === 'right') {
    logoPosition = 'bottom-left';
    reasoning = 'Text overlay on right, placing logo in bottom-left';
  } else if (textPosition === 'left') {
    logoPosition = 'bottom-right';
    reasoning = 'Text overlay on left, placing logo in bottom-right';
  } else {
    // Unknown text position, use content hints
    if (layoutHints.splitScreen) {
      logoPosition = 'bottom-center';
      reasoning = 'Split-screen layout detected, centering logo at bottom';
    } else if (layoutHints.comparison) {
      logoPosition = 'bottom-left';
      reasoning = 'Comparison content, placing logo in bottom-left';
    } else {
      logoPosition = 'bottom-right';
      reasoning = 'No clear indicators, using default bottom-right';
    }
  }
  
  return {
    position: logoPosition,
    reasoning,
    textPosition,
    layoutHints,
    avoidOverlap: true
  };
}

/**
 * Determines if a game brand should be detected based on context analysis
 * @param {string} prompt - The user prompt
 * @param {string} gameName - The detected game name
 * @returns {boolean} True if should detect the game brand
 */
function shouldDetectGameBrand(prompt, gameName) {
  const analysis = analyzeGamingContext(prompt, gameName);
  
  // Only detect game brand if:
  // 1. Explicit gaming content phrase detected, OR
  // 2. Gaming context with high confidence (>= 0.75), OR
  // 3. Gaming context with medium confidence (>= 0.6) AND early mention
  const shouldDetect = analysis.hasExplicitGamingPhrase ||
                      (analysis.context === 'gaming_content' && analysis.confidence >= 0.75) ||
                      (analysis.context === 'gaming_content' && analysis.confidence >= 0.6 && analysis.isEarlyMention);
  
  return shouldDetect;
}

/**
 * Generates logo placement instructions for the image generator
 * @param {string} gameName - The name of the detected game
 * @param {Object} placement - Logo placement object from determineLogoPlacement
 * @returns {string} Detailed instructions for logo placement
 */
function generateLogoPlacementInstructions(gameName, placement) {
  const positionMap = {
    'bottom-right': 'bottom-right corner',
    'bottom-left': 'bottom-left corner', 
    'bottom-center': 'bottom center',
    'top-right': 'top-right corner',
    'top-left': 'top-left corner',
    'top-center': 'top center'
  };
  
  const position = positionMap[placement.position] || 'bottom-right corner';
  
  let instructions = `\n\nGAME LOGO PLACEMENT:\n`;
  instructions += `- Place the official ${gameName} logo in the ${position}\n`;
  instructions += `- Logo size: Medium (not too large, not too small) - approximately 8-12% of thumbnail width\n`;
  instructions += `- Add subtle drop shadow or glow effect for visibility against the background\n`;
  instructions += `- Ensure the logo does NOT overlap with text overlays, faces, or key visual elements\n`;
  instructions += `- Logo should be clearly visible but not dominate the composition\n`;
  instructions += `- Use official brand colors and maintain logo integrity\n`;
  instructions += `- Position logo with adequate margin from edges (minimum 20px)\n`;
  
  if (placement.avoidOverlap) {
    instructions += `- CRITICAL: Avoid any overlap with text overlays or main subject matter\n`;
  }
  
  instructions += `- Reasoning: ${placement.reasoning}\n`;
  
  return instructions;
}

/**
 * Tech/Dev/AI context keywords that indicate programming, coding, or AI-related topics
 */
const TECH_CONTEXT_KEYWORDS = [
  // Programming & Development
  'code', 'coding', 'programming', 'develop', 'development', 'developer', 'dev',
  'software', 'app', 'application', 'website', 'web development', 'frontend', 'backend',
  'fullstack', 'full stack', 'javascript', 'python', 'typescript', 'react', 'vue', 'angular',
  'node.js', 'html', 'css', 'git', 'github', 'repository', 'commit', 'pull request',
  
  // AI & Machine Learning
  'ai', 'artificial intelligence', 'machine learning', 'ml', 'deep learning', 'neural network',
  'chatgpt', 'openai', 'gpt', 'claude', 'gemini', 'copilot', 'autopilot', 'agent',
  'assistant', 'ai assistant', 'code assistant', 'pair programming',
  
  // Code Editors & IDEs
  'editor', 'ide', 'code editor', 'text editor', 'vscode', 'visual studio', 'sublime',
  'atom', 'jetbrains', 'intellij', 'pycharm', 'webstorm',
  
  // Development Tools & Concepts
  'tutorial', 'guide', 'how to code', 'learn to code', 'coding tutorial', 'programming tutorial',
  'debug', 'debugging', 'test', 'testing', 'automation', 'script', 'scripting',
  'api', 'database', 'server', 'deployment', 'cloud', 'devops', 'ci/cd',
  'framework', 'library', 'package', 'npm', 'yarn', 'pip', 'maven', 'gradle',
  
  // Specific Tech Terms
  'algorithm', 'data structure', 'function', 'method', 'class', 'object', 'variable',
  'array', 'loop', 'conditional', 'syntax', 'compile', 'runtime', 'terminal', 'command line'
];

/**
 * Mouse/UI cursor keywords that indicate reference to mouse pointer, not the AI tool
 */
const MOUSE_CURSOR_KEYWORDS = [
  // Direct mouse references
  'mouse', 'click', 'pointer', 'arrow', 'hover', 'drag', 'drop', 'select',
  'move the cursor', 'cursor position', 'cursor location', 'cursor movement',
  'cursor color', 'cursor style', 'cursor shape', 'cursor icon',
  
  // UI/UX contexts
  'ui', 'user interface', 'ux', 'design', 'graphic design', 'web design',
  'css cursor', 'html cursor', 'cursor property', 'cursor: pointer',
  'custom cursor', 'animated cursor', 'cursor effects',
  
  // Navigation contexts
  'navigate', 'navigation', 'browse', 'browsing', 'scroll', 'scrolling',
  'left click', 'right click', 'double click', 'single click',
  'cursor trail', 'cursor theme', 'cursor size',
  
  // Text cursor contexts (text editing, not AI tool)
  'cursor selection', 'text cursor', 'cursor blink', 'cursor blinking',
  'cursor caret', 'caret', 'insertion point', 'text selection',
  'cursor at end', 'cursor at beginning', 'place cursor', 'cursor focus'
];

/**
 * Analyzes the context of a prompt to determine if "cursor" refers to the AI tool or mouse pointer
 * @param {string} prompt - The user prompt to analyze
 * @returns {Object} Context analysis with confidence scores
 */
function analyzeCursorContext(prompt) {
  const lower = prompt.toLowerCase();
  
  // Count tech context keywords
  const techScore = TECH_CONTEXT_KEYWORDS.reduce((score, keyword) => {
    return score + (lower.includes(keyword) ? 1 : 0);
  }, 0);
  
  // Count mouse cursor keywords  
  const mouseScore = MOUSE_CURSOR_KEYWORDS.reduce((score, keyword) => {
    return score + (lower.includes(keyword) ? 1 : 0);
  }, 0);
  
  // Explicit mentions that strongly indicate Cursor AI
  const explicitCursorAI = [
    'cursor ai', 'cursor.so', 'cursor editor', 'cursor code', 'cursor assistant',
    'cursor agent', 'cursor copilot', 'ai pair programming', 'cursor autocomplete'
  ];
  
  const hasExplicitCursorAI = explicitCursorAI.some(phrase => lower.includes(phrase));
  
  // Explicit mentions that strongly indicate mouse cursor
  const explicitMouseCursor = [
    'mouse cursor', 'cursor pointer', 'css cursor', 'cursor: pointer',
    'move cursor', 'cursor position', 'cursor movement', 'cursor color',
    'cursor style', 'cursor icon', 'cursor theme', 'cursor shape'
  ];
  
  const hasExplicitMouseCursor = explicitMouseCursor.some(phrase => lower.includes(phrase));
  
  // Determine context with confidence scoring
  let context = 'ambiguous';
  let confidence = 0;
  let reasoning = '';
  
  if (hasExplicitCursorAI) {
    context = 'cursor_ai';
    confidence = 0.95;
    reasoning = 'Explicit Cursor AI reference detected';
  } else if (hasExplicitMouseCursor) {
    context = 'mouse_cursor';
    confidence = 0.95;
    reasoning = 'Explicit mouse cursor reference detected';
  } else if (techScore > 0 && mouseScore === 0) {
    context = 'cursor_ai';
    confidence = Math.min(0.8, 0.5 + (techScore * 0.1));
    reasoning = `Strong tech context (${techScore} keywords), no mouse context`;
  } else if (mouseScore > 0 && techScore === 0) {
    context = 'mouse_cursor';
    confidence = Math.min(0.8, 0.5 + (mouseScore * 0.1));
    reasoning = `Strong mouse/UI context (${mouseScore} keywords), no tech context`;
  } else if (techScore > mouseScore) {
    context = 'cursor_ai';
    confidence = 0.6 + ((techScore - mouseScore) * 0.1);
    reasoning = `Tech context stronger (${techScore} vs ${mouseScore})`;
  } else if (mouseScore > techScore) {
    context = 'mouse_cursor';
    confidence = 0.6 + ((mouseScore - techScore) * 0.1);
    reasoning = `Mouse context stronger (${mouseScore} vs ${techScore})`;
  } else {
    context = 'ambiguous';
    confidence = 0.3;
    reasoning = `Equal or no context (tech: ${techScore}, mouse: ${mouseScore})`;
  }
  
  return {
    context,
    confidence: Math.min(confidence, 1.0),
    techScore,
    mouseScore,
    hasExplicitCursorAI,
    hasExplicitMouseCursor,
    reasoning
  };
}

/**
 * Determines if "cursor" in the prompt should be detected as Cursor AI brand
 * @param {string} prompt - The user prompt to analyze
 * @returns {boolean} True if should detect Cursor AI, false otherwise
 */
function shouldDetectCursorAI(prompt) {
  const analysis = analyzeCursorContext(prompt);
  
  // Only detect Cursor AI if:
  // 1. Explicit Cursor AI reference, OR
  // 2. Tech context with high confidence (>= 0.7), OR  
  // 3. Tech context with medium confidence (>= 0.5) AND no explicit mouse cursor reference
  const shouldDetect = analysis.hasExplicitCursorAI || 
                      (analysis.context === 'cursor_ai' && analysis.confidence >= 0.7) ||
                      (analysis.context === 'cursor_ai' && analysis.confidence >= 0.5 && !analysis.hasExplicitMouseCursor);
  
  return shouldDetect;
}

// Categorized brand keywords. Prioritize more specific names.
const GAMING_BRANDS = [
  'fortnite',
  'warzone',
  'call of duty',
  'cod',
  'valorant',
  'pubg',
  'apex legends',
  'apex',
  'cs2',
  'counter strike',
  'cs:go',
  'minecraft', // Added Minecraft as a common game example
  'world of tanks',
  'clash of clans',
  'clash royale',
  'league of legends',
  'dota 2',
  'overwatch',
  'rainbow six siege',
  'fifa',
  'madden',
  'nba 2k',
  'rocket league',
  'fall guys',
  'among us',
  'genshin impact',
  'roblox',
  'unity',
  'unreal engine',
  'grand theft auto',
  'gta',
  'world of warcraft',
  'wow',
  'destiny 2',
  'destiny',
  'hearthstone',
  'diablo',
  'starcraft',
  'starcraft 2',
  'halo',
  'the elder scrolls',
  'skyrim',
  'fallout',
  'red dead redemption',
  'cyberpunk 2077',
  'witcher 3',
  'assassins creed',
  'far cry',
  'battlefield',
  'mortal kombat',
  'street fighter',
  'tekken',
  'super smash bros',
  'pokemon',
  'zelda',
  'mario',
  'super mario',
  'god of war',
  'spider-man',
  'the last of us',
  'ghost of tsushima',
  'bloodborne',
  'dark souls',
  'elden ring',
  'sekiro',
  'resident evil',
  'silent hill',
  'dead by daylight',
  'phasmophobia',
  'escape from tarkov',
  'rust',
  'ark survival evolved',
  'subnautica',
  'no mans sky',
  'terraria',
  'stardew valley',
  'animal crossing',
  'sims 4',
  'cities skylines',
  'civilization',
  'age of empires',
  'total war',
  'europa universalis',
  'crusader kings',
  'hearts of iron'
];

const TECH_DEV_AI_BRANDS = [
  // AI & Machine Learning (from Branded-logos.md)
  'openai', // (ChatGPT, DALL-E, GPT-4 etc.)
  'chatgpt',
  'dall-e',
  'gpt-4',
  'google ai', // (Gemini, Bard, Vertex AI)
  'gemini',
  'bard',
  'vertex ai',
  'midjourney',
  'stable diffusion',
  'hugging face',
  'tensorflow',
  'pytorch',
  'scikit-learn',
  'keras',
  'jupyter',
  'anaconda',
  'runwayml',
  'synthesia',
  'descript',
  'murf.ai',
  'elevenlabs',
  'notion ai',
  'jasper', // (formerly Jarvis)
  'copy.ai',
  'rytr',
  'writesonic',
  'claude', // (Anthropic)
  'perplexity ai',
  'firebase studio',
  // Existing TECH_DEV_AI_BRANDS (merged and de-duplicated)
  'bolt.new', // Prioritize this specific AI dev tool
  'lovable',  // AI dev tool
  'cursor',   // AI dev tool (context-aware detection implemented below)
  'figma',
  'webflow',
  'framer',
  'photoshop',
  'adobe photoshop',
  'illustrator',
  'adobe illustrator',
  'shopify',
  'canva',
  // 'midjourney', // already in AI
  // 'openai', // already in AI
  // 'chatgpt', // already in AI
  'notion', // also in Productivity
  'github',
  'gitlab',
  'firebase',
  'supabase',
  'vscode',
  'visual studio code',
  'flutter',
  'react',
  'next.js',
  'nextjs',
  'vue',
  'vue.js',
  'angular',
  'node.js',
  'python',
  'aws',
  'amazon web services',
  'google cloud',
  'gcp',
  'azure',
  'microsoft azure',
  'docker',
  'kubernetes',
  'jira',
  'slack',
  'discord',
  // 'zoom', // in Productivity
  'miro',
  'figjam',
  'adobe xd',
  'sketch',
  // 'trello', // in Productivity
  // 'asana', // in Productivity
  // 'clickup', // in Productivity
  'datadog',
  'sentry',
  'postman',
  'vercel',
  'netlify',
  // Design & UX/UI (from Branded-logos.md)
  'invision',
  'marvel',
  'principle',
  'protopie',
  'balsamiq',
  'axure rp',
  'affinity designer',
  'affinity photo',
  'procreate',
  'coreldraw',
  'zeplin',
  'abstract',
  'lottiefiles',
  'wordpress',
  'elementor',
  'squarespace',
  'wix',
  'dorik',
  'carrd',
  'bubble',
  'readymag',
  'tilda publishing',
  // Development & DevOps (from Branded-logos.md)
  'sublime text',
  'atom',
  'jetbrains', // (IntelliJ IDEA, PyCharm, WebStorm, etc.)
  'intellij idea',
  'pycharm',
  'webstorm',
  'bitbucket',
  'confluence',
  'microsoft teams',
  'jenkins',
  'circleci',
  'travis ci',
  'heroku',
  'digitalocean',
  'linode',
  'mongodb',
  'postgresql',
  'mysql',
  'redis',
  'elasticsearch',
  'terraform',
  'ansible',
  'puppet',
  'chef',
  'new relic',
  'insomnia',
  'swagger', // / OpenAPI
  'openapi',
  'graphql',
  'svelte',
  'nuxtjs',
  'gatsby',
  'django',
  'flask',
  'ruby on rails',
  'php',
  'laravel',
  'symfony',
  'java',
  'spring',
  'c#',
  '.net',
  'go',
  'golang',
  'rust',
  'swift',
  'kotlin',
  'react native',
  'electron',
  'zapier',
  'ifttt',
  'make', // (formerly Integromat)
  'integromat',
  'n8n',
  'bolt' // from Branded-logos.md (generic, ensure bolt.new is prioritized by logic)
];

const PRODUCTIVITY_OTHER_BRANDS = [
  'notion', // also in tech/dev
  'obsidian',
  'roam research',
  'airtable',
  'trello',
  'asana',
  'monday.com',
  'clickup',
  // 'miro', // in tech/dev
  // 'figjam', // in tech/dev
  'lucidchart',
  'grammarly',
  'zoom',
  'google meet',
  // 'discord', // in tech/dev
  'loom',
  'adobe premiere pro',
  'final cut pro',
  'davinci resolve',
  'audacity',
  'ableton live',
  'logic pro x'
];

// export const ALL_BRANDS = [...GAMING_BRANDS, ...TECH_DEV_AI_BRANDS]; // Old version
export const ALL_BRANDS = [
  ...new Set([ // Use Set to remove duplicates from combined list
    ...GAMING_BRANDS,
    ...TECH_DEV_AI_BRANDS,
    ...PRODUCTIVITY_OTHER_BRANDS
  ])
];

/**
 * Detect brand keywords in a prompt and categorize them with context awareness.
 * @param {string} prompt - Raw user prompt.
 * @param {number} [limit=3] - Max number of brands to return to avoid clutter.
 * @param {string} [overlayText=''] - Text overlay content for logo placement analysis.
 * @param {string} [overlayPosition=''] - Text overlay position for logo placement analysis.
 * @returns {{name: string, category: 'game' | 'tech' | 'productivity' | 'other', logoPlacement?: Object}[]} Array of matched brand objects.
 */
export function detectBrandLogos(prompt = '', limit = 3, overlayText = '', overlayPosition = '') {
  if (!prompt) return [];
  const lower = prompt.toLowerCase();
  const matches = [];

  const addMatch = (name, category = 'other', logoPlacement = null) => {
    const titleCasedName = name.replace(/(^|\s|-)(\w)/g, (_, p1, p2) => p1 + p2.toUpperCase());
    if (matches.length < limit && !matches.some(m => m.name.toLowerCase() === titleCasedName.toLowerCase())) {
      const match = { name: titleCasedName, category };
      if (logoPlacement) {
        match.logoPlacement = logoPlacement;
      }
      matches.push(match);
    }
  };

  // === ENHANCED CONTEXT-AWARE CURSOR DETECTION ===
  // Check for cursor using sophisticated context analysis
  if (lower.includes('cursor')) {
    if (shouldDetectCursorAI(prompt)) {
      addMatch('Cursor AI', 'tech');
    }
    // If shouldDetectCursorAI returns false, we don't add any cursor-related brand
  }

  // Specific checks first for priority brands (excluding cursor, now handled above)
  if (lower.includes('bolt.new')) addMatch('Bolt.new', 'tech');
  if (lower.includes('lovable')) addMatch('Lovable', 'tech');

  // === ENHANCED CONTEXT-AWARE GAMING BRAND DETECTION ===
  // Iterate through gaming brands with context analysis
  for (const keyword of GAMING_BRANDS) {
    if (matches.length >= limit) break;
    if (lower.includes(keyword) && !matches.some(m => m.name.toLowerCase() === keyword.toLowerCase())) {
      // Use context analysis to determine if this is actually gaming content
      if (shouldDetectGameBrand(prompt, keyword)) {
        // Determine optimal logo placement for this game
        const logoPlacement = determineLogoPlacement(prompt, overlayText, overlayPosition);
        addMatch(keyword, 'game', logoPlacement);
      }
    }
  }

  for (const keyword of TECH_DEV_AI_BRANDS) {
    if (matches.length >= limit) break;
    // Skip keywords already handled by special logic
    if (['bolt.new', 'lovable', 'cursor'].includes(keyword.toLowerCase())) continue;
    if (keyword.toLowerCase() === 'bolt' && matches.some(m => m.name === 'Bolt.new')) continue; // Prefer 'Bolt.new'

    if (lower.includes(keyword)) {
        addMatch(keyword, 'tech');
    }
  }

  for (const keyword of PRODUCTIVITY_OTHER_BRANDS) {
    if (matches.length >= limit) break;
    if (lower.includes(keyword) && !matches.some(m => m.name.toLowerCase() === keyword.toLowerCase())) {
        addMatch(keyword, 'productivity');
    }
  }
  
  // Special handling for 'bolt' if 'bolt.new' wasn't found and 'bolt' is not already added from TECH_DEV_AI_BRANDS
  if (!matches.some(m => m.name.toLowerCase() === 'bolt.new' || m.name.toLowerCase() === 'bolt') && lower.includes('bolt')) {
    addMatch('Bolt', 'tech');
  }

  // Deduplicate based on name (lowercase) and take the first category encountered if names clash after title-casing
  const uniqueMatchesMap = new Map();
  matches.forEach(match => {
    const lcName = match.name.toLowerCase();
    if (!uniqueMatchesMap.has(lcName)) {
        uniqueMatchesMap.set(lcName, match);
    } else {
        // Optional: Prioritize 'tech' or 'game' category if a duplicate is found with 'productivity' or 'other'
        const existingMatch = uniqueMatchesMap.get(lcName);
        if (match.category === 'tech' && existingMatch.category !== 'tech') {
            uniqueMatchesMap.set(lcName, match);
        } else if (match.category === 'game' && existingMatch.category !== 'game' && existingMatch.category !== 'tech') {
            uniqueMatchesMap.set(lcName, match);
        }
    }
  });

  return Array.from(uniqueMatchesMap.values()).slice(0, limit);
}

/**
 * Check if any of the detected brands are gaming brands.
 * @param {{name: string, category: string}[]} detectedBrands - Array of brand objects from detectBrandLogos.
 * @returns {boolean} True if at least one gaming brand is detected.
 */
export function hasGamingBrand(detectedBrands = []) {
    return detectedBrands.some(brand => brand.category === 'game');
}

/**
 * Check if any of the detected brands are tech/dev/ai brands.
 * @param {{name: string, category: string}[]} detectedBrands - Array of brand objects from detectBrandLogos.
 * @returns {boolean} True if at least one tech/dev/ai brand is detected.
 */
export function hasTechBrand(detectedBrands = []) {
    return detectedBrands.some(brand => brand.category === 'tech');
}

/**
 * Enhanced detectBrandLogosWithPlacement function that returns both brands and placement instructions
 * @param {string} prompt - Raw user prompt
 * @param {number} [limit=3] - Max number of brands to return
 * @param {string} [overlayText=''] - Text overlay content
 * @param {string} [overlayPosition=''] - Text overlay position
 * @returns {Object} - { brands: Array, logoInstructions: string }
 */
export function detectBrandLogosWithPlacement(prompt = '', limit = 3, overlayText = '', overlayPosition = '') {
  const brands = detectBrandLogos(prompt, limit, overlayText, overlayPosition);
  let logoInstructions = '';
  
  // Generate logo placement instructions for detected game brands
  const gameBrands = brands.filter(brand => brand.category === 'game' && brand.logoPlacement);
  
  if (gameBrands.length > 0) {
    logoInstructions = gameBrands.map(brand => 
      generateLogoPlacementInstructions(brand.name, brand.logoPlacement)
    ).join('\n');
  }
  
  return {
    brands,
    logoInstructions
  };
}

// === EXPORT ADDITIONAL FUNCTIONS FOR TESTING AND INTEGRATION ===
export { 
  analyzeCursorContext, 
  shouldDetectCursorAI,
  analyzeGamingContext,
  shouldDetectGameBrand,
  determineLogoPlacement,
  generateLogoPlacementInstructions
}; 