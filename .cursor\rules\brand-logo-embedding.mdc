---
description: 
globs: 
alwaysApply: false
---
---
title: brand-logo-embedding
id: brand-logo-embedding.mdc
ruleType: manual
---

## Feature: Brand Logo Embedding

### Objective
Allow users to search for and embed brand logos into their thumbnails, with options to select between 2D and 3D styles.

### Applies To
- /src/components/LogoSelector.jsx
- /src/components/ThumbnailEditor.jsx
- /src/state/logoPreferences.ts

### Feature Rules

#### 🎨 Logo Selection Modal
- Triggered by "Add Brand Logo" button
- Includes:
  - Search Field with autocomplete
  - Popular Brands Grid
  - Style Toggle (2D/3D)
  - Preview Area

#### 🖼️ Logo Placement and Customization
- Drag-and-Drop positioning
- Resize and Rotate controls
- Opacity and Effects adjustments

#### 🔧 Brandfetch API Integration
- Use Brand Search API
- Include `clientId` with every request
- Endpoint: `GET https://api.brandfetch.io/v2/search/{brand_name}?c={clientId}`

#### ⚙️ Caching and Performance
- Implement caching for fetched logos
- Use lazy loading to optimize performance

### Notes for Developers
- Ensure accessibility in modal design
- Handle API errors gracefully
- Validate user inputs in the search field

### Bonus UX Tip 💡
Provide tooltips or help icons to guide users on how to use the logo embedding feature effectively.

✅ Ready for rule activation.
To apply, drop this .mdc into your active Cursor workspace and toggle in Chat Cascade when needed.
