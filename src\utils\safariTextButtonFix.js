/**
 * Safari Text Button Group Fix Utility
 * 
 * This utility addresses Safari-specific issues with:
 * - aria-pressed button states not updating properly
 * - Text color rendering issues in button groups
 * - Active state persistence problems
 * - Focus and hover state management
 */

/**
 * Detect if the current browser is Safari (excluding Chrome-based browsers)
 * @returns {boolean} True if Safari, false otherwise
 */
export const isSafari = () => {
    if (typeof window === 'undefined') return false;
    
    const userAgent = window.navigator.userAgent;
    const isSafariUA = userAgent.includes('Safari');
    const isChrome = userAgent.includes('Chrome') || userAgent.includes('Chromium');
    const isEdge = userAgent.includes('Edge') || userAgent.includes('Edg');
    const isFirefox = userAgent.includes('Firefox');
    
    // Return true only for actual Safari (not Chrome, Edge, or Firefox)
    return isSafariUA && !isChrome && !isEdge && !isFirefox;
};

/**
 * Detect if the current browser is iOS Safari
 * @returns {boolean} True if iOS Safari, false otherwise
 */
export const isIOSSafari = () => {
    if (typeof window === 'undefined') return false;
    
    const userAgent = window.navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
    return isIOS && isSafari();
};

/**
 * Force Safari to properly update button group states
 * This addresses the known Safari bug where aria-pressed states don't trigger proper visual updates
 * 
 * @param {string} activeSize - The currently selected size ('Small', 'Medium', 'Large')
 * @param {string} buttonGroupSelector - CSS selector for the button group (default: '.text-size-button')
 */
export const forceSafariButtonStateUpdate = (activeSize, buttonGroupSelector = '.text-size-button') => {
    if (!isSafari()) return;
    
    // Use setTimeout to ensure this runs after React's state update
    setTimeout(() => {
        const buttons = document.querySelectorAll(buttonGroupSelector);
        
        buttons.forEach(button => {
            const buttonText = button.textContent?.trim();
            const isActive = buttonText === activeSize;
            
            // Force aria-pressed attribute update
            button.setAttribute('aria-pressed', isActive.toString());
            
            // Apply Safari-specific style fixes
            if (isActive) {
                // Active button styles
                button.style.color = '#FFFFFF';
                button.style.webkitTextFillColor = '#FFFFFF';
                button.style.fontWeight = '600';
                button.style.opacity = '1';
                button.style.visibility = 'visible';
                
                // Force Safari to recognize the change
                button.style.webkitTransform = 'translateZ(0)';
                button.style.transform = 'translateZ(0)';
            } else {
                // Inactive button styles
                button.style.color = '#9CA3AF';
                button.style.webkitTextFillColor = '#9CA3AF';
                button.style.fontWeight = '500';
                button.style.opacity = '1';
                button.style.visibility = 'visible';
            }
            
            // Force hardware acceleration and repaint
            button.style.webkitBackfaceVisibility = 'hidden';
            button.style.webkitPerspective = '1000px';
            
            // Clear the transform after a frame to trigger repaint
            requestAnimationFrame(() => {
                button.style.webkitTransform = '';
                button.style.transform = '';
            });
        });
    }, 0);
};

/**
 * Apply iOS Safari specific fixes for touch interactions
 * @param {string} buttonGroupSelector - CSS selector for the button group
 */
export const applyIOSSafariFixes = (buttonGroupSelector = '.text-size-button') => {
    if (!isIOSSafari()) return;
    
    const buttons = document.querySelectorAll(buttonGroupSelector);
    
    buttons.forEach(button => {
        // Prevent tap highlight
        button.style.webkitTapHighlightColor = 'transparent';
        
        // Improve text rendering on iOS
        button.style.textRendering = 'optimizeLegibility';
        button.style.webkitFontSmoothing = 'antialiased';
        
        // Add touch event listeners to force proper state updates
        button.addEventListener('touchstart', (e) => {
            // Force active state recognition
            if (button.getAttribute('aria-pressed') === 'true') {
                button.style.color = '#FFFFFF';
                button.style.webkitTextFillColor = '#FFFFFF';
            }
        }, { passive: true });
        
        button.addEventListener('touchend', (e) => {
            // Ensure state persists after touch
            setTimeout(() => {
                if (button.getAttribute('aria-pressed') === 'true') {
                    button.style.color = '#FFFFFF';
                    button.style.webkitTextFillColor = '#FFFFFF';
                    button.style.fontWeight = '600';
                }
            }, 10);
        }, { passive: true });
    });
};

/**
 * Initialize Safari fixes for text button groups
 * Call this after the button group is rendered to the DOM
 * 
 * @param {string} activeSize - Currently selected size
 * @param {string} buttonGroupSelector - CSS selector for buttons
 */
export const initSafariTextButtonFixes = (activeSize, buttonGroupSelector = '.text-size-button') => {
    if (!isSafari()) return;
    
    // Apply initial state fixes
    forceSafariButtonStateUpdate(activeSize, buttonGroupSelector);
    
    // Apply iOS-specific fixes if needed
    applyIOSSafariFixes(buttonGroupSelector);
    
    // Set up mutation observer to catch dynamic changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'aria-pressed') {
                const button = mutation.target;
                const isPressed = button.getAttribute('aria-pressed') === 'true';
                
                if (isPressed) {
                    button.style.color = '#FFFFFF';
                    button.style.webkitTextFillColor = '#FFFFFF';
                    button.style.fontWeight = '600';
                } else {
                    button.style.color = '#9CA3AF';
                    button.style.webkitTextFillColor = '#9CA3AF';
                    button.style.fontWeight = '500';
                }
            }
        });
    });
    
    // Observe all buttons in the group
    const buttons = document.querySelectorAll(buttonGroupSelector);
    buttons.forEach(button => {
        observer.observe(button, {
            attributes: true,
            attributeFilter: ['aria-pressed']
        });
    });
    
    // Return cleanup function
    return () => {
        observer.disconnect();
    };
};

/**
 * Safari-compatible button click handler
 * Use this as a wrapper for button click events in Safari
 * 
 * @param {string} size - The size being selected
 * @param {Function} setSelectedTextSize - State setter function
 * @param {Event} event - Click event
 */
export const handleSafariButtonClick = (size, setSelectedTextSize, event) => {
    // Update React state
    setSelectedTextSize(size);
    
    // Apply Safari-specific fixes
    if (isSafari()) {
        forceSafariButtonStateUpdate(size);
        
        // Additional immediate styling for the clicked button
        const button = event.target;
        if (button) {
            button.style.color = '#FFFFFF';
            button.style.webkitTextFillColor = '#FFFFFF';
            button.style.fontWeight = '600';
        }
    }
};

/**
 * Clean up Safari fixes
 * Call this when the component unmounts
 */
export const cleanupSafariTextButtonFixes = () => {
    // Remove any global event listeners or observers if needed
    // Currently handled by the observer returned from initSafariTextButtonFixes
}; 