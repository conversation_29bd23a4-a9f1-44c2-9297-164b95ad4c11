---
description: 
globs: 
alwaysApply: true
---
@person-headshot-enhancement
- ruleId: person-headshot-detail
description: >
  When the "Include Person" toggle is enabled and a facial headshot is requested, automatically enhance the prompt to ensure:
    - Cinematic portrait or video-style rendering
    - Exceptional facial detail and clarity
    - Lifelike, flattering skin tones (no over-smoothing or unnatural hues)
    - Professional-grade LUT color grading (cinematic or soft natural)
    - Enhanced contrast, depth, and mood without compromising skin fidelity
    - Studio-quality, well-balanced lighting with subtle, realistic shadows
    - Wide dynamic range for a polished, true-to-life appearance
    - Output suitable for high-end thumbnails or profile images

appliesTo:
  - /src/utils/promptBuilder.ts
  - /src/hooks/usePromptEnhancer.ts
  - /src/templates/people/
ruleType: always

sampleTransformedPrompt: |
  Render a cinematic portrait or video-style headshot that emphasizes facial features with exceptional clarity and detail. Ensure lifelike, flattering skin tones—avoid over-smoothing, plasticity, or unnatural color casts. Apply professional-grade LUT color grading (cinematic or soft natural preferred) to enhance contrast, depth, and mood, while preserving authentic skin fidelity. Use balanced, studio-quality lighting with gentle, realistic shadows and a wide dynamic range to achieve a polished yet natural look. The result should feel vibrant, engaging, and true-to-life, suitable for high-end thumbnails or profile imagery.