import React, { useState, useEffect } from 'react'

// Import the dedicated ErrorBoundary component
import { ErrorBoundary } from './ErrorBoundary.jsx';

// SVGs for eye and eye-closed
const EyeIcon = ({ className = '', ...props }) => (
    React.createElement('svg', {
        xmlns: 'http://www.w3.org/2000/svg',
        fill: 'none',
        viewBox: '0 0 24 24',
        strokeWidth: 1.5,
        stroke: 'currentColor',
        className: `w-5 h-5 ${className}`,
        ...props
    },
        React.createElement('path', {
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
            d: 'M2.25 12s3.75-7.5 9.75-7.5 9.75 7.5 9.75 7.5-3.75 7.5-9.75 7.5S2.25 12 2.25 12z'
        }),
        React.createElement('path', {
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
            d: 'M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z'
        })
    )
);

const EyeClosedIcon = ({ className = '', ...props }) => (
    React.createElement('svg', {
        xmlns: 'http://www.w3.org/2000/svg',
        fill: 'none',
        viewBox: '0 0 24 24',
        strokeWidth: 1.5,
        stroke: 'currentColor',
        className: `w-5 h-5 ${className}`,
        ...props
    },
        React.createElement('path', {
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
            d: 'M3.98 8.223A10.477 10.477 0 002.25 12s3.75 7.5 9.75 7.5c2.042 0 3.82-.393 5.282-1.028M6.228 6.228A10.45 10.45 0 0112 4.5c6 0 9.75 7.5 9.75 7.5a10.46 10.46 0 01-4.293 5.568M6.228 6.228l11.544 11.544M6.228 6.228L3 3m15 15l-3-3'
        })
    )
);

export const PasswordChangeModal = ({
    isOpen,
    onClose,
    onPasswordChange,
    passwordState,
    setPasswordState,
    rateLimitInfo
}) => {
    const [isClosing, setIsClosing] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '' });

    // Safe error accessor to prevent undefined errors
    const getError = (field) => {
        return passwordState?.errors?.[field] || '';
    };

    // Safe errors object accessor
    const getErrors = () => {
        return passwordState?.errors || {};
    };

    // Simple toggle function - no debounce needed since we're using inline SVGs
    // This prevents DOM mutation conflicts that occurred with Iconify

    useEffect(() => {
        if (!isOpen) {
            setIsClosing(false);
        }
    }, [isOpen]);

    // Note: Iconify re-scan removed since password visibility toggles now use inline SVGs
    // Other Solar icons in this component (error, requirements) still use Iconify and work fine

    // Calculate password strength
    const calculatePasswordStrength = (password) => {
        if (!password) {
            setPasswordStrength({ score: 0, label: '', color: '' });
            return;
        }

        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        // Calculate score
        if (checks.length) score += 20;
        if (password.length >= 12) score += 20;
        if (checks.lowercase) score += 20;
        if (checks.uppercase) score += 20;
        if (checks.numbers) score += 20;
        if (checks.special) score += 20;

        // Determine strength label and color
        let label = '';
        let color = '';
        if (score <= 20) {
            label = 'Very Weak';
            color = 'bg-red-500';
        } else if (score <= 40) {
            label = 'Weak';
            color = 'bg-orange-500';
        } else if (score <= 60) {
            label = 'Fair';
            color = 'bg-yellow-500';
        } else if (score <= 80) {
            label = 'Good';
            color = 'bg-blue-500';
        } else {
            label = 'Strong';
            color = 'bg-green-500';
        }

        setPasswordStrength({ score, label, color });
    };

    // Helper function to check password requirements
    const checkRequirement = (requirement) => {
        const password = passwordState.newPassword || '';
        switch (requirement) {
            case 'length':
                return password.length >= 8;
            case 'uppercase':
                return /[A-Z]/.test(password);
            case 'lowercase':
                return /[a-z]/.test(password);
            case 'number':
                return /\d/.test(password);
            case 'special':
                return /[!@#$%^&*(),.?":{}|<>]/.test(password);
            case 'mixed_case':
                return /[A-Z]/.test(password) && /[a-z]/.test(password);
            default:
                return false;
        }
    };

    // Update password strength when new password changes
    useEffect(() => {
        calculatePasswordStrength(passwordState.newPassword);

        // Force Iconify to re-scan icons when password requirements change
        if (typeof window !== 'undefined' && window.Iconify) {
            setTimeout(() => {
                window.Iconify.scan();
            }, 0);
        }
    }, [passwordState.newPassword]);

    // Validate passwords
    const validatePasswords = () => {
        const errors = {};

        if (!passwordState.currentPassword) {
            errors.currentPassword = 'Current password is required';
        }

        if (!passwordState.newPassword) {
            errors.newPassword = 'New password is required';
        } else if (passwordState.newPassword.length < 8) {
            errors.newPassword = 'Password must be at least 8 characters';
        } else if (passwordState.newPassword === passwordState.currentPassword) {
            errors.newPassword = 'New password must be different from current password';
        }

        if (!passwordState.confirmPassword) {
            errors.confirmPassword = 'Please confirm your new password';
        } else if (passwordState.newPassword !== passwordState.confirmPassword) {
            errors.confirmPassword = 'Passwords do not match';
        }

        setPasswordState(prev => ({ ...prev, errors }));
        return Object.keys(errors).length === 0;
    };

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
            // Reset form after closing
            setTimeout(() => {
                setPasswordState({
                    isOpen: false,
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: '',
                    showCurrentPassword: false,
                    showNewPassword: false,
                    showConfirmPassword: false,
                    errors: {},
                    isLoading: false,
                    successMessage: ''
                });
                setPasswordStrength({ score: 0, label: '', color: '' });
            }, 100);
        }, 200);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validatePasswords()) {
            return;
        }

        setPasswordState(prev => ({ ...prev, isLoading: true, errors: {} }));

        try {
            // Call the password change function
            await onPasswordChange({
                currentPassword: passwordState.currentPassword,
                newPassword: passwordState.newPassword
            });

            // Show success message
            setPasswordState(prev => ({
                ...prev,
                isLoading: false,
                successMessage: 'Password changed successfully!',
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
                showCurrentPassword: false,
                showNewPassword: false,
                showConfirmPassword: false
            }));

            // Auto close after 3 seconds
            setTimeout(() => {
                handleClose();
            }, 3000);
        } catch (error) {
            setPasswordState(prev => ({
                ...prev,
                isLoading: false,
                errors: {
                    submit: error.message || 'Failed to change password. Please try again.'
                }
            }));
        }
    };

    const togglePasswordVisibility = (field) => {
        setPasswordState(prev => ({
            ...prev,
            [field]: !prev[field]
        }));
    };

    if (!isOpen) return null;

    return React.createElement(ErrorBoundary, {},
        React.createElement('div', {
            className: `fixed inset-0 z-[10002] flex items-center justify-center${isClosing ? ' closing' : ''}`,
            onClick: (e) => e.target === e.currentTarget && handleClose(),
            style: { animation: isClosing ? 'fadeOut 0.2s ease-in' : 'fadeIn 0.2s ease-out' }
        },
            // Backdrop
            React.createElement('div', {
                className: 'absolute inset-0 bg-black/60 backdrop-blur-sm'
            }),

            // Modal Container
            React.createElement('div', {
                className: 'relative w-full max-w-md mx-4',
                style: { animation: isClosing ? 'slideOutScale 0.2s cubic-bezier(0.4, 0, 1, 1)' : 'slideInScale 0.3s cubic-bezier(0.16, 1, 0.3, 1)' }
            },
                // Modal Content
                React.createElement('div', {
                    className: 'bg-gray-800 border border-gray-700 rounded-2xl shadow-2xl overflow-hidden'
                },
                    passwordState.successMessage ? (
                        // Success State
                        React.createElement('div', {
                            className: 'p-8 text-center'
                        },
                            // Success Icon
                            React.createElement('div', {
                                className: 'mb-6 flex justify-center'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-green-400',
                                    'data-icon': 'solar:check-circle-linear',
                                    style: { fontSize: '52px' }
                                })
                            ),

                            // Success Title
                            React.createElement('h2', {
                                className: 'text-2xl font-bold text-white mb-3'
                            }, 'Password Changed!'),

                            // Success Message
                            React.createElement('p', {
                                className: 'text-gray-400 mb-8'
                            }, 'Your password has been updated successfully.'),

                            // Done Button
                            React.createElement('button', {
                                onClick: handleClose,
                                className: 'w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors',
                                autoFocus: true
                            }, 'Done')
                        )
                    ) : (
                        // Form State
                        React.createElement(React.Fragment, {},
                            // Header
                            React.createElement('div', {
                                className: 'px-6 py-5 border-b border-gray-700'
                            },
                                React.createElement('div', { className: 'flex items-center justify-between' },
                                    React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Change Password'),
                                    React.createElement('button', {
                                        onClick: handleClose,
                                        className: 'text-gray-400 hover:text-gray-300 transition-colors p-1',
                                        'aria-label': 'Close modal'
                                    },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:close-circle-bold',
                                            style: { fontSize: '24px' }
                                        })
                                    )
                                )
                            ),

                            // Rate Limiting Information (if available)
                            rateLimitInfo && rateLimitInfo.message && !rateLimitInfo.canChange && (
                                React.createElement('div', {
                                    className: 'px-6 py-4 border-b border-gray-700 bg-gray-800/50'
                                },
                                    React.createElement('div', {
                                        className: `flex items-start gap-3 text-sm ${rateLimitInfo.message.type === 'warning' ? 'text-yellow-400' :
                                            rateLimitInfo.message.type === 'info' ? 'text-blue-400' :
                                                'text-red-400'
                                            }`
                                    },
                                        React.createElement('span', {
                                            className: 'iconify mt-0.5',
                                            'data-icon':
                                                rateLimitInfo.message.type === 'warning' ? 'solar:clock-circle-linear' :
                                                    rateLimitInfo.message.type === 'info' ? 'solar:info-circle-linear' :
                                                        'solar:danger-triangle-linear',
                                            style: { fontSize: '18px' }
                                        }),
                                        React.createElement('div', {},
                                            React.createElement('p', { className: 'font-medium mb-1' }, rateLimitInfo.message.title),
                                            React.createElement('p', { className: 'text-xs opacity-90' }, rateLimitInfo.message.message),
                                            rateLimitInfo.message.nextAvailable && (
                                                React.createElement('p', { className: 'text-xs opacity-75 mt-2' },
                                                    `Next available: ${rateLimitInfo.message.nextAvailable.toLocaleString()}`
                                                )
                                            )
                                        )
                                    )
                                )
                            ),

                            // Rate Limiting Success Info (when changes remaining)
                            rateLimitInfo && rateLimitInfo.message && rateLimitInfo.canChange && rateLimitInfo.eligibility?.changesRemaining !== undefined && (
                                React.createElement('div', {
                                    className: 'px-6 py-3 border-b border-gray-700 bg-green-900/10'
                                },
                                    React.createElement('div', { className: 'flex items-center gap-2 text-green-400 text-sm' },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:check-circle-linear',
                                            style: { fontSize: '16px' }
                                        }),
                                        React.createElement('span', {},
                                            `${rateLimitInfo.eligibility.changesRemaining} password change${rateLimitInfo.eligibility.changesRemaining !== 1 ? 's' : ''} remaining this month`
                                        )
                                    )
                                )
                            ),

                            // Form
                            React.createElement('form', {
                                onSubmit: handleSubmit,
                                className: 'p-6'
                            },
                                // Current Password Field
                                React.createElement('div', { className: 'mb-5' },
                                    React.createElement('label', {
                                        htmlFor: 'current-password',
                                        className: 'block text-sm font-medium text-gray-300 mb-2'
                                    }, 'Current Password'),
                                    React.createElement('div', { className: 'password-input-container' },
                                        React.createElement('input', {
                                            type: passwordState.showCurrentPassword ? 'text' : 'password',
                                            id: 'current-password',
                                            value: passwordState.currentPassword,
                                            onChange: (e) => setPasswordState(prev => ({
                                                ...prev,
                                                currentPassword: e.target.value,
                                                errors: { ...getErrors(), currentPassword: '' }
                                            })),
                                            className: `w-full bg-gray-700 border ${getError('currentPassword') ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                            placeholder: 'Enter current password',
                                            disabled: passwordState.isLoading,
                                            autoComplete: 'current-password'
                                        }),
                                        React.createElement('button', {
                                            type: 'button',
                                            onClick: () => togglePasswordVisibility('showCurrentPassword'),
                                            className: 'password-toggle-btn',
                                            'aria-label': passwordState.showCurrentPassword ? 'Hide password' : 'Show password'
                                        },
                                            passwordState.showCurrentPassword
                                                ? React.createElement(EyeClosedIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                                : React.createElement(EyeIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                        )
                                    ),
                                    getError('currentPassword') && React.createElement('p', {
                                        className: 'mt-1 text-red-400 text-xs'
                                    }, getError('currentPassword'))
                                ),

                                // New Password Field
                                React.createElement('div', { className: 'mb-5' },
                                    React.createElement('label', {
                                        htmlFor: 'new-password',
                                        className: 'block text-sm font-medium text-gray-300 mb-2'
                                    }, 'New Password'),
                                    React.createElement('div', { className: 'password-input-container' },
                                        React.createElement('input', {
                                            type: passwordState.showNewPassword ? 'text' : 'password',
                                            id: 'new-password',
                                            value: passwordState.newPassword,
                                            onChange: (e) => setPasswordState(prev => ({
                                                ...prev,
                                                newPassword: e.target.value,
                                                errors: { ...getErrors(), newPassword: '' }
                                            })),
                                            className: `w-full bg-gray-700 border ${getError('newPassword') ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                            placeholder: 'Enter new password',
                                            disabled: passwordState.isLoading,
                                            autoComplete: 'new-password'
                                        }),
                                        React.createElement('button', {
                                            type: 'button',
                                            onClick: () => togglePasswordVisibility('showNewPassword'),
                                            className: 'password-toggle-btn',
                                            'aria-label': passwordState.showNewPassword ? 'Hide password' : 'Show password'
                                        },
                                            passwordState.showNewPassword
                                                ? React.createElement(EyeClosedIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                                : React.createElement(EyeIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                        )
                                    ),
                                    getError('newPassword') && React.createElement('p', {
                                        className: 'mt-1 text-red-400 text-xs'
                                    }, getError('newPassword')),

                                    // Password Strength Indicator
                                    passwordState.newPassword && React.createElement('div', { className: 'mt-3' },
                                        React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                                            React.createElement('span', { className: 'text-xs text-gray-400' }, 'Password Strength'),
                                            React.createElement('span', {
                                                className: `text-xs font-medium ${passwordStrength.score <= 40 ? 'text-red-400' :
                                                    passwordStrength.score <= 60 ? 'text-yellow-400' :
                                                        passwordStrength.score <= 80 ? 'text-blue-400' : 'text-green-400'
                                                    }`
                                            }, passwordStrength.label)
                                        ),
                                        React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-1.5' },
                                            React.createElement('div', {
                                                className: `${passwordStrength.color} h-1.5 rounded-full transition-all duration-300`,
                                                style: { width: `${passwordStrength.score}%` }
                                            })
                                        )
                                    )
                                ),

                                // Confirm Password Field
                                React.createElement('div', { className: 'mb-6' },
                                    React.createElement('label', {
                                        htmlFor: 'confirm-password',
                                        className: 'block text-sm font-medium text-gray-300 mb-2'
                                    }, 'Confirm New Password'),
                                    React.createElement('div', { className: 'password-input-container' },
                                        React.createElement('input', {
                                            type: passwordState.showConfirmPassword ? 'text' : 'password',
                                            id: 'confirm-password',
                                            value: passwordState.confirmPassword,
                                            onChange: (e) => setPasswordState(prev => ({
                                                ...prev,
                                                confirmPassword: e.target.value,
                                                errors: { ...getErrors(), confirmPassword: '' }
                                            })),
                                            className: `w-full bg-gray-700 border ${getError('confirmPassword') ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                            placeholder: 'Confirm new password',
                                            disabled: passwordState.isLoading,
                                            autoComplete: 'new-password'
                                        }),
                                        React.createElement('button', {
                                            type: 'button',
                                            onClick: () => togglePasswordVisibility('showConfirmPassword'),
                                            className: 'password-toggle-btn',
                                            'aria-label': passwordState.showConfirmPassword ? 'Hide password' : 'Show password'
                                        },
                                            passwordState.showConfirmPassword
                                                ? React.createElement(EyeClosedIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                                : React.createElement(EyeIcon, { className: 'text-gray-400', style: { fontSize: '20px' } })
                                        )
                                    ),
                                    getError('confirmPassword') && React.createElement('p', {
                                        className: 'mt-1 text-red-400 text-xs'
                                    }, getError('confirmPassword'))
                                ),

                                // Error Message
                                getError('submit') && React.createElement('div', {
                                    className: 'mb-6 p-3 bg-red-900/30 border border-red-700/50 rounded-lg flex items-center gap-2'
                                },
                                    React.createElement('span', {
                                        className: 'iconify text-red-400',
                                        'data-icon': 'solar:danger-triangle-linear',
                                        style: { fontSize: '20px' }
                                    }),
                                    React.createElement('p', { className: 'text-red-400 text-sm' }, getError('submit'))
                                ),
                                // Password Requirements with Enhanced UX
                                React.createElement("div", {
                                    className: "mb-6 p-3 bg-gray-700/50 rounded-lg",
                                    key: `requirements-${passwordState.newPassword || 'empty'}`
                                },
                                    React.createElement("p", { className: "text-xs text-gray-400 mb-2" }, "Password Requirements:"),
                                    React.createElement("ul", { className: "space-y-1" },
                                        React.createElement("li", {
                                            className: "flex items-center gap-2 text-xs",
                                            key: "req-length"
                                        },
                                            React.createElement("span", {
                                                className: `iconify ${checkRequirement('length') ? "text-green-400" : "text-red-400"}`,
                                                "data-icon": checkRequirement('length') ? "solar:check-circle-bold" : "solar:close-circle-bold",
                                                style: { fontSize: "14px" },
                                                key: `length-${checkRequirement('length')}`
                                            }),
                                            React.createElement("span", {
                                                className: checkRequirement('length') ? "text-white font-medium" : "text-gray-400"
                                            }, "At least 8 characters")
                                        ),
                                        React.createElement("li", {
                                            className: "flex items-center gap-2 text-xs",
                                            key: "req-mixed-case"
                                        },
                                            React.createElement("span", {
                                                className: `iconify ${checkRequirement('mixed_case') ? "text-green-400" : "text-red-400"}`,
                                                "data-icon": checkRequirement('mixed_case') ? "solar:check-circle-bold" : "solar:close-circle-bold",
                                                style: { fontSize: "14px" },
                                                key: `mixed-case-${checkRequirement('mixed_case')}`
                                            }),
                                            React.createElement("span", {
                                                className: checkRequirement('mixed_case') ? "text-white font-medium" : "text-gray-400"
                                            }, "Both uppercase and lowercase letters")
                                        ),
                                        React.createElement("li", {
                                            className: "flex items-center gap-2 text-xs",
                                            key: "req-number"
                                        },
                                            React.createElement("span", {
                                                className: `iconify ${checkRequirement('number') ? "text-green-400" : "text-red-400"}`,
                                                "data-icon": checkRequirement('number') ? "solar:check-circle-bold" : "solar:close-circle-bold",
                                                style: { fontSize: "14px" },
                                                key: `number-${checkRequirement('number')}`
                                            }),
                                            React.createElement("span", {
                                                className: checkRequirement('number') ? "text-white font-medium" : "text-gray-400"
                                            }, "At least one number")
                                        ),
                                        React.createElement("li", {
                                            className: "flex items-center gap-2 text-xs",
                                            key: "req-special"
                                        },
                                            React.createElement("span", {
                                                className: `iconify ${checkRequirement('special') ? "text-green-400" : "text-red-400"}`,
                                                "data-icon": checkRequirement('special') ? "solar:check-circle-bold" : "solar:close-circle-bold",
                                                style: { fontSize: "14px" },
                                                key: `special-${checkRequirement('special')}`
                                            }),
                                            React.createElement("span", {
                                                className: checkRequirement('special') ? "text-white font-medium" : "text-gray-400"
                                            }, "At least one special character")
                                        )
                                    )
                                ),
                                // Action Buttons
                                React.createElement('div', { className: 'flex gap-3' },
                                    React.createElement('button', {
                                        type: 'button',
                                        onClick: handleClose,
                                        className: 'flex-1 px-4 py-2.5 rounded-lg text-gray-400 hover:text-gray-300 transition-colors text-sm font-medium',
                                        disabled: passwordState.isLoading
                                    }, 'Cancel'),
                                    React.createElement('button', {
                                        type: 'submit',
                                        className: 'flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white px-4 py-2.5 rounded-lg font-medium text-sm transition-colors flex items-center justify-center gap-2',
                                        disabled: passwordState.isLoading
                                    },
                                        passwordState.isLoading && React.createElement('span', {
                                            className: 'iconify animate-spin',
                                            'data-icon': 'solar:refresh-linear',
                                            style: { fontSize: '16px' }
                                        }),
                                        passwordState.isLoading ? 'Changing...' : 'Change Password'
                                    )
                                )
                            )
                        ) // Close React.Fragment
                    ) // Close ternary operator
                )
            )
        ) // Close ErrorBoundary wrapper
    );
};
