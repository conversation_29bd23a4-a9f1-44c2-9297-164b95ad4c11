---
description: 
globs: 
alwaysApply: false
---
@remember-login-details

ruleId: remember-login-details
description: >
  Implements a persistent "Remember Me" login feature. When enabled by the user, login credentials (or, preferably, a session token) are securely saved in browser storage (localStorage or IndexedDB). The app should:
    - Auto-fill and auto-login if valid credentials/session exist, even after closing/reopening the browser or tab.
    - Provide a visible "Remember Me" checkbox on the login form.
    - Clear saved credentials/tokens on explicit sign out.
    - Never store plain-text passwords if a token/session approach is available.
    - Always respect user privacy and security best practices.
    - Optionally, provide a "Forget Me" option in account/settings.

appliesTo:
  - /src/pages/Welcome.jsx
  - /src/App.jsx
  - /src/utils/auth.js

acceptanceCriteria:
  - User who checks "Remember Me" stays logged in across tabs/app restarts.
  - Credentials/tokens are cleared on sign out.
  - Login form auto-fills or auto-logs in if valid data exists.
  - No plain-text passwords are stored if a token/session is available.
  - UI and UX are clear, secure, and user-friendly.

sampleUI: |
  [ ] Remember Me
  (checked = persistent login)
  (unchecked = session only)