const React = window.React;
const { useState, useEffect } = React;

import { authAPI } from '../utils/supabase.mjs';
import { debugAuth } from '../utils/debugAuth.js';
import { AuthThemeToggle } from '../components/ui/AuthThemeToggle.jsx';

export const ResetPassword = ({ isLightTheme, onToggleTheme }) => {
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [isLoading, setIsLoading] = useState(true); // Start true to wait for auth event
    const [readyToReset, setReadyToReset] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    useEffect(() => {
        // Debug information
        console.log('ResetPassword component mounted');
        debugAuth.logCurrentURL();
        debugAuth.logStorage();
        const urlCheck = debugAuth.isRecoveryURL();

        // Check if we came from a reset password email by looking at current session
        const checkIfRecoverySession = async () => {
            try {
                const sessionInfo = await debugAuth.getSessionInfo();
                console.log('Session check result:', sessionInfo);
                
                if (sessionInfo.success && sessionInfo.session) {
                    const user = sessionInfo.session.user;
                    // Check if user has recent recovery activity
                    if (user.recovery_sent_at) {
                        const recoverySentTime = new Date(user.recovery_sent_at);
                        const now = new Date();
                        const timeDiff = now - recoverySentTime;
                        const twentyMinutes = 20 * 60 * 1000; // 20 minutes in milliseconds
                        
                        console.log('Recovery sent at:', recoverySentTime);
                        console.log('Time difference:', timeDiff);
                        console.log('Within 20 minutes?', timeDiff < twentyMinutes);
                        
                        if (timeDiff < twentyMinutes) {
                            console.log('Recent recovery session detected, allowing password reset');
                            setReadyToReset(true);
                            setError('');
                            setIsLoading(false);
                            return true;
                        }
                    }
                }
            } catch (error) {
                console.error('Error checking recovery session:', error);
            }
            return false;
        };

        const { data: authListener } = authAPI.onAuthStateChange(async (event, session) => {
            console.log('ResetPassword auth state change:', event, session?.user?.email, session);
            
            if (event === 'PASSWORD_RECOVERY') {
                console.log('PASSWORD_RECOVERY event detected');
                setReadyToReset(true);
                setError(''); // Clear any previous errors
                setIsLoading(false);
            } else if (event === 'SIGNED_IN' && session) {
                console.log('SIGNED_IN event detected');
                // Check if this is a recovery session
                const isRecovery = await checkIfRecoverySession();
                if (isRecovery) {
                    return; // Already handled in checkIfRecoverySession
                }
                
                // Regular sign in - redirect to main app
                console.log('Regular sign in detected, redirecting to main app');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else if (event === 'SIGNED_OUT') {
                console.log('SIGNED_OUT event detected');
                setReadyToReset(false);
                setIsLoading(false);
            } else if (event === 'INITIAL_SESSION' && session) {
                console.log('INITIAL_SESSION event detected');
                // Check if this is a recovery session
                const isRecovery = await checkIfRecoverySession();
                if (isRecovery) {
                    return; // Already handled in checkIfRecoverySession
                }
                
                // Regular initial session - redirect to main app
                console.log('Regular initial session detected, redirecting to main app');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            }
        });

        // Check URL parameters and session immediately
        const checkRecoveryParams = async () => {
            if (urlCheck.isRecovery) {
                console.log('Recovery parameters found immediately');
                setReadyToReset(true);
                setError('');
                setIsLoading(false);
                return;
            }
            
            // Check if we have a recovery session
            const isRecoverySession = await checkIfRecoverySession();
            if (isRecoverySession) {
                return; // Already handled
            }
            
            // If no recovery detected, set a timeout to show error
            const timer = setTimeout(() => {
                if (isLoading && !readyToReset) {
                    console.log('Timeout reached, no recovery detected');
                    setIsLoading(false);
                    setError("Invalid or expired password reset link. Please request a new one or check the URL.");
                }
            }, 3000); // Back to 3 seconds since we do immediate checks
            
            return timer;
        };

        checkRecoveryParams().then(timer => {
            // Store timer for cleanup if needed
            if (timer) {
                // Timer will be cleaned up in return function
            }
        });

        return () => {
            authListener.subscription.unsubscribe();
        };
    }, []); // Remove dependencies to avoid infinite loops

    const handleUpdatePassword = async (e) => {
        e.preventDefault();
        if (password !== confirmPassword) {
            setError('Passwords do not match.');
            return;
        }
        if (password.length < 6) {
            setError('Password must be at least 6 characters long.');
            return;
        }

        setError('');
        setIsLoading(true);

        try {
            const result = await authAPI.updatePassword(password);
            if (result.success) {
                setSuccessMessage('Your password has been successfully updated! You can now log in.');
                setReadyToReset(false); // Prevent re-submission
                // Optional: Redirect to login after a delay
                // setTimeout(() => {
                //     window.location.href = '/'; 
                // }, 3000);
            } else {
                setError(result.error || 'Failed to update password. Please try again.');
            }
        } catch (err) {
            setError('An unexpected error occurred. Please try again.');
        }
        setIsLoading(false);
    };

    if (isLoading) {
        return (
            <div className="auth-glass-container" id="reset-password-loading-container">
                {/* Theme Toggle Button */}
                <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
                
                {/* Centered Glass Card */}
                <div className="flex items-center justify-center min-h-screen p-4">
                    <div className="auth-glass-card w-full max-w-md p-8" id="reset-password-loading-card">
                        {/* Logo */}
                        <img 
                            src="/assets/main-logo.svg" 
                            alt="Thumbspark Logo" 
                            className="auth-glass-logo" 
                            draggable="false" 
                        />

                        {/* Loading Icon */}
                        <div className="mb-6 flex justify-center">
                            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>

                        {/* Loading Message */}
                        <h1 className="auth-glass-title">Loading...</h1>
                        <p className="auth-glass-subtitle">Verifying your reset link</p>
                    </div>
                </div>
            </div>
        );
    }

    if (successMessage) {
        return (
            <div className="auth-glass-container" id="reset-password-success-container">
                {/* Theme Toggle Button */}
                <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
                
                {/* Centered Glass Card */}
                <div className="flex items-center justify-center min-h-screen p-4">
                    <div className="auth-glass-card w-full max-w-md p-8" id="reset-password-success-card">
                        {/* Logo */}
                        <img 
                            src="/assets/main-logo.svg" 
                            alt="Thumbspark Logo" 
                            className="auth-glass-logo" 
                            draggable="false" 
                        />

                        {/* Success Icon */}
                        <div className="mb-6 flex justify-center">
                            <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center">
                                <span className="iconify text-green-400" data-icon="solar:check-circle-bold" style={{ fontSize: '40px' }}></span>
                            </div>
                        </div>

                        {/* Success Message */}
                        <h1 className="auth-glass-title">Password Updated!</h1>
                        <p className="auth-glass-subtitle">Your password has been successfully changed</p>

                        <div className="mb-8">
                            <div className="bg-green-900/20 border border-green-700/50 rounded-lg p-4 mb-6">
                                <p className="text-green-300 text-sm text-center">
                                    Your password has been successfully updated. You can now sign in with your new password.
                                </p>
                            </div>
                        </div>

                        {/* Navigation Links */}
                        <div className="text-center space-y-4">
                            <button 
                                type="button"
                                onClick={() => window.location.href = '/'}
                                className="auth-glass-cta-btn w-full"
                            >
                                Continue to Sign In
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error && !readyToReset) {
        return (
            <div className="auth-glass-container" id="reset-password-error-container">
                {/* Theme Toggle Button */}
                <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
                
                {/* Centered Glass Card */}
                <div className="flex items-center justify-center min-h-screen p-4">
                    <div className="auth-glass-card w-full max-w-md p-8" id="reset-password-error-card">
                        {/* Logo */}
                        <img 
                            src="/assets/main-logo.svg" 
                            alt="Thumbspark Logo" 
                            className="auth-glass-logo" 
                            draggable="false" 
                        />

                        {/* Error Icon */}
                        <div className="mb-6 flex justify-center">
                            <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center">
                                <span className="iconify text-red-400" data-icon="solar:danger-circle-bold" style={{ fontSize: '40px' }}></span>
                            </div>
                        </div>

                        {/* Error Message */}
                        <h1 className="auth-glass-title">Invalid Link</h1>
                        <p className="auth-glass-subtitle">This reset link is invalid or has expired</p>

                        <div className="mb-8">
                            <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-4 mb-6">
                                <p className="text-red-300 text-sm text-center">
                                    {error}
                                </p>
                            </div>
                        </div>

                        {/* Navigation Links */}
                        <div className="text-center space-y-4">
                            <button 
                                type="button"
                                onClick={() => window.location.href = '/forgot-password'}
                                className="auth-glass-cta-btn w-full"
                            >
                                Request New Reset Link
                            </button>
                            
                            <button 
                                type="button"
                                onClick={() => window.location.href = '/'}
                                className="auth-glass-link"
                            >
                                Back to Sign In
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="auth-glass-container" id="reset-password-container">
            {/* Theme Toggle Button */}
            <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
            
            {/* Centered Glass Card */}
            <div className="flex items-center justify-center min-h-screen p-4">
                <div className="auth-glass-card w-full max-w-md p-8" id="reset-password-card">
                    {/* Logo */}
                    <img 
                        src="/assets/main-logo.svg" 
                        alt="Thumbspark Logo" 
                        className="auth-glass-logo" 
                        draggable="false" 
                    />

                    {/* Title and Subtitle */}
                    <h1 className="auth-glass-title">Set New Password</h1>
                    <p className="auth-glass-subtitle">Enter your new password below</p>

                    {/* Global Error Message */}
                    {error && (
                        <div className="auth-glass-global-error" id="reset-password-global-error">
                            <div className="auth-glass-global-error-text">
                                <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                {error}
                            </div>
                        </div>
                    )}

                    {/* Reset Password Form */}
                    <form onSubmit={handleUpdatePassword} className="space-y-4" id="reset-password-form">
                        {/* New Password Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="password" className="auth-glass-label">
                                New Password
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:lock-keyhole-linear"></span>
                                <input 
                                    type={showPassword ? "text" : "password"} 
                                    id="reset-password-input" 
                                    name="password" 
                                    value={password} 
                                    onChange={(e) => setPassword(e.target.value)} 
                                    required
                                    minLength="6"
                                    className="auth-glass-input"
                                    placeholder="Enter your new password"
                                />
                                <button 
                                    type="button" 
                                    onClick={() => setShowPassword(!showPassword)} 
                                    className="auth-glass-password-toggle"
                                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                                >
                                    <span className="iconify" data-icon={showPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                </button>
                            </div>
                        </div>

                        {/* Confirm Password Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="confirmPassword" className="auth-glass-label">
                                Confirm New Password
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:lock-keyhole-linear"></span>
                                <input 
                                    type={showConfirmPassword ? "text" : "password"} 
                                    id="reset-confirm-password-input" 
                                    name="confirmPassword" 
                                    value={confirmPassword} 
                                    onChange={(e) => setConfirmPassword(e.target.value)} 
                                    required
                                    minLength="6"
                                    className="auth-glass-input"
                                    placeholder="Confirm your new password"
                                />
                                <button 
                                    type="button" 
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)} 
                                    className="auth-glass-password-toggle"
                                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                                >
                                    <span className="iconify" data-icon={showConfirmPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                </button>
                            </div>
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={isLoading || !password || !confirmPassword}
                            className="auth-glass-cta-btn"
                            id="reset-password-submit-btn"
                        >
                            {isLoading ? (
                                <>
                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Updating Password...</span>
                                </>
                            ) : (
                                'Update Password'
                            )}
                        </button>

                        {/* Additional Links */}
                        <div className="text-center">
                            <button 
                                type="button"
                                onClick={() => window.location.href = '/'}
                                className="auth-glass-link"
                            >
                                ← Back to Sign In
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};
