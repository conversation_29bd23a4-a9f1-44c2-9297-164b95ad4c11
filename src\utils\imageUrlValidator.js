/**
 * Context7MCP Universal Face Upload by URL - Image URL Validator & Transformer
 * 
 * Supports all major image hosting services including Imgur, with comprehensive
 * format validation and URL transformation for maximum compatibility.
 */
import { uploadImageToImgbb } from './imgbbUploader.js';

/**
 * Supported image formats with their MIME types
 */
const SUPPORTED_FORMATS = {
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg', 
  'png': 'image/png',
  'webp': 'image/webp'
};

/**
 * Image hosting service patterns and their transformation rules
 */
const IMAGE_HOST_PATTERNS = {
  // Imgur patterns
  imgur: {
    patterns: [
      /^https?:\/\/(i\.)?imgur\.com\/([a-zA-Z0-9]+)(\.[a-zA-Z]+)?$/,
      /^https?:\/\/imgur\.com\/gallery\/([a-zA-Z0-9]+)$/,
      /^https?:\/\/imgur\.com\/a\/([a-zA-Z0-9]+)$/,
      /^https?:\/\/m\.imgur\.com\/([a-zA-Z0-9]+)$/
    ],
    transform: (url) => {
      // Extract image ID from various Imgur URL formats
      const match = url.match(/imgur\.com\/(?:gallery\/|a\/)?([a-zA-Z0-9]+)/);
      if (match) {
        const imageId = match[1];
        return `https://i.imgur.com/${imageId}.jpg`; // Default to jpg, will fallback to png if needed
      }
      return url;
    }
  },
  
  // Google Drive patterns
  googleDrive: {
    patterns: [
      /^https?:\/\/drive\.google\.com\/file\/d\/([a-zA-Z0-9_-]+)\/view/,
      /^https?:\/\/drive\.google\.com\/open\?id=([a-zA-Z0-9_-]+)/
    ],
    transform: (url) => {
      const match = url.match(/(?:file\/d\/|id=)([a-zA-Z0-9_-]+)/);
      if (match) {
        return `https://drive.google.com/uc?export=view&id=${match[1]}`;
      }
      return url;
    }
  },
  
  // Dropbox patterns
  dropbox: {
    patterns: [
      /^https?:\/\/www\.dropbox\.com\/s\/([a-zA-Z0-9_-]+)\/[^?]*\?dl=0$/,
      /^https?:\/\/dropbox\.com\/s\/([a-zA-Z0-9_-]+)/
    ],
    transform: (url) => {
      return url.replace(/\?dl=0$/, '?raw=1').replace(/\?dl=1$/, '?raw=1');
    }
  },
  
  // OneDrive patterns  
  oneDrive: {
    patterns: [
      /^https?:\/\/onedrive\.live\.com\/.*redir\?resid=/,
      /^https?:\/\/1drv\.ms\/[a-zA-Z]\/[a-zA-Z0-9_-]+/
    ],
    transform: (url) => {
      // OneDrive requires special handling - may need to be handled case by case
      return url.replace(/\?.*$/, ''); // Remove query parameters for now
    }
  },
  
  // Discord CDN patterns
  discord: {
    patterns: [
      /^https?:\/\/cdn\.discordapp\.com\/attachments\/\d+\/\d+\/[^?]+\.(jpg|jpeg|png|webp)$/,
      /^https?:\/\/media\.discordapp\.net\/attachments\/\d+\/\d+\/[^?]+\.(jpg|jpeg|png|webp)$/
    ],
    transform: (url) => url // Discord CDN URLs are already direct
  },
  
  // GitHub patterns
  github: {
    patterns: [
      /^https?:\/\/github\.com\/[^\/]+\/[^\/]+\/blob\/[^\/]+\/.*\.(jpg|jpeg|png|webp)$/,
      /^https?:\/\/raw\.githubusercontent\.com\/[^\/]+\/[^\/]+\/[^\/]+\/.*\.(jpg|jpeg|png|webp)$/
    ],
    transform: (url) => {
      if (url.includes('github.com') && url.includes('/blob/')) {
        return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/');
      }
      return url;
    }
  },
  
  // Generic direct image URLs
  direct: {
    patterns: [
      /^https?:\/\/.*\.(jpg|jpeg|png|webp)(\?.*)?$/i
    ],
    transform: (url) => url // Already direct
  }
};

/**
 * Validates if a URL is a supported image URL and transforms it if needed
 * @param {string} url - The URL to validate and transform
 * @returns {Object} - { isValid: boolean, transformedUrl: string, format: string, error: string }
 */
export const validateAndTransformImageUrl = (url) => {
  if (!url || typeof url !== 'string') {
    return {
      isValid: false,
      transformedUrl: '',
      format: '',
      error: 'URL is required'
    };
  }

  const trimmedUrl = url.trim();
  
  // Basic URL format validation
  if (!/^https?:\/\//i.test(trimmedUrl)) {
    return {
      isValid: false,
      transformedUrl: '',
      format: '',
      error: 'URL must start with http:// or https://'
    };
  }

  // Check against all hosting service patterns
  for (const [serviceName, service] of Object.entries(IMAGE_HOST_PATTERNS)) {
    for (const pattern of service.patterns) {
      if (pattern.test(trimmedUrl)) {
        const transformedUrl = service.transform(trimmedUrl);
        const format = extractImageFormat(transformedUrl);
        
        return {
          isValid: true,
          transformedUrl,
          format,
          error: '',
          service: serviceName
        };
      }
    }
  }

  // If no pattern matches, check if it's a direct image URL
  const format = extractImageFormat(trimmedUrl);
  if (format) {
    return {
      isValid: true,
      transformedUrl: trimmedUrl,
      format,
      error: '',
      service: 'direct'
    };
  }

  return {
    isValid: false,
    transformedUrl: '',
    format: '',
    error: 'URL does not appear to be a supported image format (.jpg, .jpeg, .png, .webp) or from a supported hosting service'
  };
};

/**
 * Extracts image format from URL
 * @param {string} url - The URL to check
 * @returns {string} - The image format (jpg, png, webp) or empty string
 */
const extractImageFormat = (url) => {
  const match = url.match(/\.([a-zA-Z0-9]+)(?:\?.*)?$/);
  if (match) {
    const extension = match[1].toLowerCase();
    return SUPPORTED_FORMATS[extension] ? extension : '';
  }
  return '';
};

/**
 * Tests if an image URL is accessible and returns an image
 * @param {string} url - The URL to test
 * @returns {Promise<Object>} - { isAccessible: boolean, error: string, dimensions?: {width, height} }
 */
export const testImageUrlAccessibility = (url) => {
  return new Promise((resolve) => {
    const img = new Image();
    
    // Set up CORS handling
    img.crossOrigin = 'anonymous';
    
    const timeout = setTimeout(() => {
      resolve({
        isAccessible: false,
        error: 'Image load timeout (10 seconds)'
      });
    }, 10000);
    
    img.onload = () => {
      clearTimeout(timeout);
      resolve({
        isAccessible: true,
        error: '',
        dimensions: {
          width: img.naturalWidth,
          height: img.naturalHeight
        }
      });
    };
    
    img.onerror = () => {
      clearTimeout(timeout);
      resolve({
        isAccessible: false,
        error: 'Failed to load image. Check URL or try a different image.'
      });
    };
    
    img.src = url;
  });
};

/**
 * Comprehensive image URL validation with accessibility testing
 * @param {string} url - The URL to validate
 * @returns {Promise<Object>} - Complete validation result
 */
export const validateImageUrlComprehensive = async (url) => {
  // First, validate and transform the URL
  const validation = validateAndTransformImageUrl(url);
  
  if (!validation.isValid) {
    return validation;
  }
  
  // Then test accessibility
  const accessibility = await testImageUrlAccessibility(validation.transformedUrl);
  
  let finalUrl = validation.transformedUrl;
  let service = validation.service;
  let dimensions = accessibility.dimensions;
  let isAccessible = accessibility.isAccessible;
  let error = accessibility.error || validation.error;
  
  // Fallback: if not accessible, proxy through imgbb
  if (!isAccessible) {
    try {
      const proxiedUrl = await uploadImageToImgbb(validation.transformedUrl, 0);
      // Re-test accessibility for the proxied image
      const proxyCheck = await testImageUrlAccessibility(proxiedUrl);
      if (proxyCheck.isAccessible) {
        finalUrl = proxiedUrl;
        service = 'imgbb-proxy';
        isAccessible = true;
        dimensions = proxyCheck.dimensions;
        error = '';
      }
    } catch (proxyErr) {
      console.warn('imgbb proxy upload failed:', proxyErr);
    }
  }
  
  return {
    ...validation,
    transformedUrl: finalUrl,
    service,
    isAccessible,
    dimensions,
    error
  };
};

/**
 * Get user-friendly error messages for common issues
 * @param {string} error - The error message
 * @returns {string} - User-friendly error message
 */
export const getUserFriendlyErrorMessage = (error) => {
  const errorMappings = {
    'URL is required': 'Please enter an image URL',
    'URL must start with http:// or https://': 'URL must start with http:// or https://',
    'URL does not appear to be a supported image format': 'Please use a direct link to an image file (.jpg, .jpeg, .png, .webp) or a supported hosting service (Imgur, Google Drive, etc.)',
    'Image load timeout (10 seconds)': 'Image took too long to load. Please try a different URL or check your internet connection.',
    'Failed to load image. Check URL or try a different image.': 'Could not load the image. Please check the URL or try a different image.'
  };
  
  return errorMappings[error] || error;
};

/**
 * Get examples of supported URL formats for user guidance
 * @returns {Array} - Array of example URLs with descriptions
 */
export const getSupportedUrlExamples = () => {
  return [
    {
      service: 'Imgur',
      examples: [
        'https://i.imgur.com/abc123.jpg',
        'https://imgur.com/abc123',
        'https://imgur.com/gallery/abc123'
      ]
    },
    {
      service: 'Google Drive',
      examples: [
        'https://drive.google.com/file/d/abc123/view',
        'https://drive.google.com/open?id=abc123'
      ]
    },
    {
      service: 'Dropbox',
      examples: [
        'https://www.dropbox.com/s/abc123/image.jpg?dl=0'
      ]
    },
    {
      service: 'Discord',
      examples: [
        'https://cdn.discordapp.com/attachments/123/456/image.jpg'
      ]
    },
    {
      service: 'Direct URLs',
      examples: [
        'https://example.com/image.jpg',
        'https://example.com/image.png',
        'https://example.com/image.webp'
      ]
    }
  ];
}; 