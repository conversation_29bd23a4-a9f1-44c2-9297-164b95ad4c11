---
description: 
globs: 
alwaysApply: true
---
# Description
Universal prompt construction rules for all GPT-image-1 YouTube thumbnail generation logic.

# Type: always
# Applies to: /src/components/PromptBuilder.jsx, /utils/promptFormatter.js

# 🎯 Universal GPT-Image-1 Prompt Rules for YouTube Thumbnail Generator

## 🧠 Objective
Guide GPT-image-1 to consistently generate high-quality, cinematic YouTube thumbnails for **any video topic**. Thumbnails must be bold, clear, emotion-driven, and platform-optimized — regardless of the prompt’s subject.

---

## 🧩 Prompt Framework

Use this structure when generating thumbnails:

```plaintext
Create a cinematic YouTube thumbnail image at 1280x720 resolution.

Subject:
- Include a human figure when context suggests a host, reaction, or personality
- Use expressive facial emotion that fits the theme (e.g. excited, serious, curious, shocked)
- Pose: facing camera, pointing, reacting, or holding a relevant object (e.g. device, product, symbol)

Text Overlay:
- Add a bold, uppercase title related to the video topic
- Font: modern sans-serif, bold and legible
- Colors: high-contrast (e.g. yellow + white or red + black)
- Add glow, drop shadow, or slight bevel to increase click appeal
- Placement: top-right or center-right
- Safe zone: ensure **at least 40px margin** from all sides to prevent clipping on mobile and desktop

Visual Focus:
- Keep layout simple and high-impact
- Use icons or emojis **only if relevant to the topic** (limit to 3–5)
- Maintain strong center or rule-of-thirds composition

Background:
- Use vibrant lighting, motion blur, or abstract textures
- Add color contrast between subject and text (e.g. dark background + bright text)
- Optional platform reference if relevant (e.g. YouTube, TikTok, Amazon)

Output Format:
- Resolution: 1280x720 (16:9)
- Ready for mobile + desktop visibility on YouTube
- High sharpness and saturation

✅ Universal Quality Rules

✅ Always center subject or lead with visual hierarchy
✅ Bold text should be readable on small screens
✅ Emotions and gestures should feel human, not robotic
✅ Use background contrast to emphasize subject and text
✅ Avoid clutter — prioritize face + message clarity
🧠 MVP Integration Guidance

When user inputs a prompt:

Dynamically extract topic + intent (e.g. “tech tutorial”, “reaction”, “motivation”)
Select matching tone: energetic / serious / curious
Auto-inject the text overlay rules and visual styling into the GPT prompt before submission
🛡️ Notes

Use this template to guide all prompts regardless of the video category
Do not hardcode emojis, text, or specific themes
Let the visual balance and formatting rules drive quality
