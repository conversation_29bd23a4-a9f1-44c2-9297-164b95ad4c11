---
description: 
globs: 
alwaysApply: false
---
---
title: lut-color-grade-enhancement
id: lut-color-grade-enhancement.mdc
ruleType: manual
---

## 🧠 Feature: Enhanced Prompt Injection for LUT-Based Color Grading

### Objective
Ensure LUT-based color grading styles (e.g., `soft-calm`, `gaming-glow`, `cold-drama`, `cinematic-warm`) are **visibly and consistently applied** when included in prompt construction. This rule addresses cases where simply appending LUT names to a prompt doesn't affect the visual style in generation.

---

## Applies To
- /src/utils/buildPrompt.js
- /src/state/lutGrading.ts
- /src/components/ThumbnailSettingsPanel.jsx

---

## 🎯 Problem
Currently, appending grading terms like `cinematic-warm` or `cold-drama` to the end of prompts is often **ignored or inconsistently applied** by the image generation model. This leads to visuals not matching user expectations.

---

## ✅ Solution: Descriptive Prompt Injection
Use **explicit, descriptive phrases** rather than raw LUT identifiers.

### 🔄 Instead of:
```text
Apply LUT: cinematic-warm
```

### ✅ Use:
```text
Apply a cinematic orange-teal color grade over the entire image, enhancing contrast with warm shadows and cool highlights.
```

---

## 🧩 Mapped Prompt Enhancements

| LUT Name         | Injected Prompt Phrase                                                                 |
|------------------|-----------------------------------------------------------------------------------------|
| cinematic-warm   | cinematic orange-teal LUT with warm shadows and cool highlights                       |
| cold-drama       | cold blue-gray cinematic filter with desaturated highlights                           |
| viral-energy     | high-contrast vivid tone with saturated reds and yellows                              |
| soft-calm        | pastel color grade with muted tones and soft ambient lighting                         |
| gaming-glow      | neon lighting effects with purple and electric blue tones and digital bloom            |
| sunset-mood      | soft peach and orange gradient overlay with warm lowlight ambience                    |
| retro-pop        | 80s pop art grading with magenta, yellow, and cyan tones                              |

---

## ✅ Prompt Output Format (Example)
```json
"color_grading": {
  "preset": "cold-drama",
  "description": "cold blue-gray cinematic filter with desaturated highlights"
}
```

Or injected directly in the main prompt:
```text
Apply a cold blue-gray cinematic color grading filter across the thumbnail background, with soft desaturated lighting.
```

---

## 🔒 Developer Notes
- Implement a mapping object (`lutPromptMap`) that injects full descriptions
- Avoid exposing raw LUT names to the final prompt
- Use fallback logic for unknown LUTs (default to `soft-calm` or skip entirely)

---

## ✅ Done When:
- All listed LUT styles visibly affect generation output
- Descriptions are correctly injected in every case
- Users see consistent LUT previews reflected in output

---

To activate this in Cursor: `@lut-color-grade-enhancement`
