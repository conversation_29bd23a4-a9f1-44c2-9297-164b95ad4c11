// Preload OpenMoji SVG images for better performance and quality assurance
import { moodEmojiMapping, genderEmojiMapping, getOpenMojiUrl, validateSvgQuality, preloadOpenMojiSvg } from './openmojiMapping.js';

export const preloadOpenMojiImages = async () => {
  const allEmojis = [
    ...Object.values(moodEmojiMapping),
    ...Object.values(genderEmojiMapping)
  ];

  console.log(`🚀 Starting OpenMoji SVG preload for ${allEmojis.length} emoji...`);

  const preloadPromises = allEmojis.map(async (emoji) => {
    try {
      const img = await preloadOpenMojiSvg(emoji.openmojiHex);
      
      // Validate SVG quality after loading
      const isQualityOk = validateSvgQuality(img);
      
      if (isQualityOk) {
        console.log(`✅ High-quality SVG loaded: ${emoji.label} (${emoji.openmojiHex})`);
      } else {
        console.warn(`⚠️ Quality issue detected for: ${emoji.label} (${emoji.openmojiHex})`);
      }
      
      return { emoji, success: true, quality: isQualityOk };
    } catch (error) {
      console.error(`❌ Failed to preload ${emoji.label} (${emoji.openmojiHex}):`, error);
      return { emoji, success: false, error };
    }
  });

  // Wait for all preloads to complete
  const results = await Promise.allSettled(preloadPromises);
  
  const successful = results.filter(result => 
    result.status === 'fulfilled' && result.value.success
  ).length;
  
  const highQuality = results.filter(result => 
    result.status === 'fulfilled' && result.value.success && result.value.quality
  ).length;

  console.log(`📊 OpenMoji Preload Summary:`, {
    total: allEmojis.length,
    successful,
    highQuality,
    failed: allEmojis.length - successful
  });

  return results;
};

// Enhanced initialization with quality monitoring
export const initOpenMoji = async () => {
  console.log('🎨 Initializing OpenMoji SVG system...');
  
  try {
    // Preload images after a short delay to not block initial render
    const results = await new Promise((resolve) => {
      setTimeout(async () => {
        const preloadResults = await preloadOpenMojiImages();
        resolve(preloadResults);
      }, 1000);
    });

    // Set up SVG quality monitoring for dynamically loaded images
    setupSvgQualityMonitoring();

    console.log('✅ OpenMoji SVG system initialized successfully');
    return results;
  } catch (error) {
    console.error('❌ OpenMoji initialization failed:', error);
    throw error;
  }
};

// Monitor SVG quality for dynamically loaded images
const setupSvgQualityMonitoring = () => {
  // Create a MutationObserver to watch for new emoji images
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check for newly added OpenMoji images
            const emojiImages = node.querySelectorAll ? 
              node.querySelectorAll('img[src*="openmoji"], img[src*="jsdelivr"]') : 
              [];
            
            emojiImages.forEach((img) => {
              img.addEventListener('load', () => {
                const isQualityOk = validateSvgQuality(img);
                if (!isQualityOk) {
                  console.warn('⚠️ Quality issue detected in dynamically loaded emoji:', img.src);
                }
              });
            });
          }
        });
      }
    });
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('👁️ SVG quality monitoring active');
};

// Utility to force reload a specific emoji with fallback
export const reloadEmojiWithFallback = async (hexCode, targetElement) => {
  try {
    console.log(`🔄 Attempting to reload emoji: ${hexCode}`);
    
    // Try fallback CDN
    const fallbackUrl = getOpenMojiUrl(hexCode, true);
    targetElement.src = fallbackUrl;
    
    return new Promise((resolve, reject) => {
      targetElement.onload = () => {
        const isQualityOk = validateSvgQuality(targetElement);
        if (isQualityOk) {
          console.log(`✅ Successfully reloaded with fallback: ${hexCode}`);
          resolve(true);
        } else {
          console.warn(`⚠️ Fallback also has quality issues: ${hexCode}`);
          resolve(false);
        }
      };
      
      targetElement.onerror = () => {
        console.error(`❌ Fallback also failed: ${hexCode}`);
        reject(new Error('Fallback failed'));
      };
    });
  } catch (error) {
    console.error(`❌ Error during fallback reload: ${hexCode}`, error);
    throw error;
  }
}; 