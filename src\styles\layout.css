/* ================= NAVIGATION LAYOUT FIXES ================= */

/* Ensure main content doesn't get covered by fixed top navigation */
.app-container {
    margin-top: 62px; /* Height of top navigation */
    min-height: calc(100vh - 62px);
}

/* ================= WELCOME SCREEN BUTTON POSITIONING FIXES ================= */

/* Welcome screen button stable positioning to prevent layout shifts */
.welcome-skip-btn,
.welcome-back-btn,
.welcome-skip-demo-btn {
    /* Prevent text selection and mobile tap highlights */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    
    /* Ensure buttons maintain position during state changes */
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
    
    /* Prevent double-tap zoom on mobile */
    touch-action: manipulation;
    
    /* Stable dimensions to prevent layout shifts */
    white-space: nowrap;
    flex-shrink: 0;
    
    /* Prevent event propagation issues */
    isolation: isolate;
}

.welcome-title {
    font-size: 2.1rem;
}

/* Preview workspace header spacing fix */
.workspace-header {
    padding-top: 8px; /* Add top padding to prevent overlap */
    padding-bottom: 15px;
}

/* Mobile hamburger menu positioning */
.hamburger-menu-btn {
    position: fixed;
    top: 12px;
    left: 12px;
    z-index: 1001; /* Above sidebar */
    display: none; /* Hidden by default on desktop */
    width: 44px;
    height: 44px;
    border-radius: 8px;
    background-color: rgba(31, 41, 55, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.5);
    color: #D1D5DB;
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
}

.hamburger-menu-btn:hover {
    background-color: rgba(31, 41, 55, 1);
    border-color: rgba(139, 92, 246, 0.5);
    color: #FFFFFF;
    transform: scale(1.05);
}

.hamburger-menu-btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* Show hamburger menu on tablet and mobile */
@media (max-width: 1024px) {
    .hamburger-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Adjust main content padding for mobile */
    .main-center-panel {
        padding-top: 1.5rem; /* Reduced space for hamburger button */
    }
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
    .hamburger-menu-btn {
        top: 8px;
        left: 8px;
        width: 40px;
        height: 40px;
    }
    
    .main-center-panel {
        padding-top: 1.5rem;
    }
}

/* CSS Custom Properties for responsive controls */
:root {
    --controls-gap-desktop: 1rem;
    --controls-gap-tablet: 1rem;
    --controls-gap-mobile: 0.875rem;
    --controls-padding-desktop: 10px 0;
    --controls-padding-tablet: 10px 0.5rem;
    --controls-padding-mobile: 8px 0.25rem;
    --quality-width-desktop: 219px;
    --quality-width-tablet: 280px;
    --quality-width-mobile: 250px;
    --quality-height-desktop: 44px;
    --quality-height-tablet: 40px;
    --quality-height-mobile: 36px;
}

/* Basic body/layout styles */
body {
    @apply bg-gray-900 text-gray-100;
    font-family: 'Geist', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    margin: 0; 
    padding: 0;
    /* FIXED: Allow vertical scrolling for dashboard pages on mobile portrait */
    overflow-x: hidden; /* Prevent horizontal scrolling only */
    overflow-y: auto; /* Allow vertical scrolling when needed */
}

/* Prevent scrolling only when main app is active (not dashboard) */
body.main-app-active {
    overflow: hidden; /* Prevent all scrolling for main app layout */
}

/* CONTEXT7MCP FIX: Disable vertical scrolling on authentication pages at portrait breakpoints */
/* These pages don't need scrolling as content fits within viewport */
body.auth-page-active {
    overflow-y: auto; /* Default scrolling for auth pages */
}

/* Portrait breakpoint: Disable scrolling for auth pages when content fits */
@media (max-width: 768px) and (orientation: portrait) {
    body.auth-page-active {
        overflow-y: hidden !important; /* Disable vertical scrolling on auth pages in portrait */
        height: 100vh; /* Ensure full viewport height */
    }
    
    /* Ensure auth page containers use full height without overflow */
    body.auth-page-active .min-h-screen {
        min-height: 100vh !important;
        max-height: 100vh !important;
        overflow: hidden !important;
    }
}

/* Additional portrait breakpoints for different device sizes */
@media (max-width: 576px) and (orientation: portrait) {
    body.auth-page-active {
        overflow-y: hidden !important;
        height: 100vh;
        position: fixed; /* Prevent any scrolling attempts */
        width: 100%;
    }
    
    body.auth-page-active .min-h-screen {
        min-height: 100vh !important;
        max-height: 100vh !important;
        overflow: hidden !important;
    }
}

/* Landscape orientation: Allow scrolling if needed */
@media (max-width: 768px) and (orientation: landscape) {
    body.auth-page-active {
        overflow-y: auto !important; /* Allow scrolling in landscape if content overflows */
    }
}

/* SIGNUP PAGE EXCEPTION: Enable vertical scrolling on signup page at mobile breakpoints */
/* Signup forms have more content (4 fields + validation) that may require scrolling */
body.auth-page-signup {
    overflow-y: auto !important; /* Always allow scrolling on signup page */
}

/* Mobile portrait: Enable scrolling for signup page specifically */
@media (max-width: 768px) and (orientation: portrait) {
    body.auth-page-signup {
        overflow-y: auto !important; /* Enable vertical scrolling on signup page */
        height: auto !important; /* Allow content to determine height */
        position: static !important; /* Remove fixed positioning */
        width: auto !important; /* Remove width restriction */
    }
    
    body.auth-page-signup .min-h-screen {
        min-height: 100vh !important; /* Maintain minimum height */
        max-height: none !important; /* Remove height restriction */
        overflow: visible !important; /* Allow content overflow */
    }
}

/* Small mobile portrait: Maintain scrolling for signup */
@media (max-width: 576px) and (orientation: portrait) {
    body.auth-page-signup {
        overflow-y: auto !important; /* Enable scrolling */
        height: auto !important; /* Allow content height */
        position: static !important; /* Remove fixed positioning */
        width: auto !important; /* Remove width restriction */
    }
    
    body.auth-page-signup .min-h-screen {
        min-height: 100vh !important; /* Maintain minimum height */
        max-height: none !important; /* Remove height restriction */
        overflow: visible !important; /* Allow content overflow */
    }
}

.container {
    @apply mx-auto;
}

/* Root layout container */
#root > div {
    @apply min-h-screen;
}

/* ================= THREE-PANEL PROFESSIONAL LAYOUT ================= */

.app-container {
    display: grid;
    grid-template-columns: 368px 1fr; /* Remove right sidebar column - now just left sidebar and center panel */
    grid-template-rows: 1fr; /* Single row layout */
    min-height: calc(100vh - 62px); /* Account for top navigation height */
    max-width: 100vw;
    background-color: #111827; /* bg-gray-900 */
    height: calc(100vh - 62px); /* Adjust for top navigation height */
    overflow-x: visible; /* Allow tooltips to overflow horizontally */
    overflow-y: hidden; /* Prevent vertical scrolling */
    margin-top: 62px; /* Space for fixed navigation */
}

/* Left Sidebar - Controls */
.left-sidebar {
    grid-column: 1;
    grid-row: 1;
    background-color: #1F2937; /* bg-gray-800 */
    border-right: 1px solid #374151; /* border-gray-700 */
    overflow-x: visible; /* Changed from hidden to visible to prevent focus ring clipping */
    overflow-y: auto; /* Allow vertical scroll when content overflows */
    position: sticky;
    top: 0;
    width: 368px;
    padding: 1rem;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 30; /* Added z-index to ensure it's below the modal */
    display: flex; /* ADDED for flex column layout */
    flex-direction: column; /* ADDED for flex column layout */
    height: calc(100vh - 62px); /* FIXED: Adjust for top navigation height */
}

/* Custom dark scrollbar for left sidebar (will apply to inner scrollable div) */
.left-sidebar::-webkit-scrollbar {
    width: 10px;
    background: #18181B; /* Very dark background */
}
.left-sidebar::-webkit-scrollbar-thumb {
    background: #27272A; /* Slightly lighter thumb */
    border-radius: 8px;
    border: 2px solid #18181B;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}
.left-sidebar::-webkit-scrollbar-thumb:hover {
    background: #3F3F46;
}
.left-sidebar::-webkit-scrollbar-corner {
    background: #18181B;
}
/* Firefox */
.left-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #27272A #18181B;
}

/* Hide scrollbar for inner design controls scroller - but NOT for design-controls-scrollable */
.left-sidebar .overflow-y-auto:not(.design-controls-scrollable) {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
.left-sidebar .overflow-y-auto:not(.design-controls-scrollable)::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Main Center Panel - Preview Area */
.main-center-panel {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    padding: 25px 20px 25px 20px; /* Back to original padding */
    /* background-color: #111827; */
    height: calc(100vh - 62px);
    min-height: calc(100vh - 62px);
    max-height: calc(100vh - 62px);
    overflow: hidden;
    gap: 8px; /* Slightly increased gap */
}

/* Right Sidebar - Hidden for current development stage */
.right-sidebar {
    display: none; /* Hidden across all breakpoints */
}

/* ================= PREVIEW CONTAINER STYLING ================= */

.preview-container {
    width: 652px;
    height: 367px; /* 16:9 aspect ratio: 652/16*9 = 367 */
    max-width: 100%; /* Responsive on smaller screens */
    aspect-ratio: 16/9; /* Correct YouTube thumbnail aspect ratio */
    background-color: #212936; /* Slightly lighter than bg for contrast */
    /* border: 2px dashed #4B5563; */ /* Remove dashed border */
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    z-index: 0;
    margin: 0 auto; /* Center horizontally */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive scaling while maintaining aspect ratio */
@media (max-width: 820px) {
    .preview-container {
        width: 100%;
        height: auto;
        max-width: 652px;
    }
}

/* Preview wrapper to contain the preview */
.preview-wrapper {
    width: 100%;
    max-width: 652px;
    margin: 0 auto;
}

/* Ensure generated images fit properly */
.preview-container img.generated-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Fill container completely, no letterboxing */
    display: block;
}

/* Preview workspace section styling */
.preview-workspace-section {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
}

/* workspace-header styles moved to top navigation fixes section */

/* Prompt controls section */
.prompt-controls-section {
    flex: 0 0 auto;
    width: 100%;
    max-width: 652px;
    margin: 40px auto 0 auto; /* Move textarea and buttons down by 40px */
    padding: 0;
}

/* Controls bottom row */
.controls-bottom-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 50 !important;
   
    padding: var(--controls-padding-desktop) !important;
    min-height: 70px !important;
    margin-top: 32px !important; /* Increased spacing between preview and controls */
    justify-content: flex-end !important; /* Align to right */
    max-width: 652px !important; /* Match preview container width */
    margin-left: auto !important;
    margin-right: auto !important;
    gap: var(--controls-gap-desktop) !important;
    flex-wrap: wrap; /* Allow wrapping on very narrow screens */
    transition: all 0.3s ease-in-out; /* Smooth transitions for responsive changes */
}

/* Combined controls container - horizontal layout for desktop and landscape tablet */
.combined-controls {
    display: flex !important;
    /* align-items: center !important; */
    gap: 2.5rem !important; /* Increased spacing to match reference image */
    flex-wrap: nowrap; /* Keep horizontal layout */
    justify-content: space-between; /* Quality on far left, action buttons on right */
    width: 100%; /* Use full width */
    transition: all 0.3s ease-in-out; /* Smooth transitions */
}

.quality-selector {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    min-height: 48px;
    visibility: visible !important;
    min-width: max-content; /* Prevent shrinking below content size */
    transition: all 0.3s ease-in-out; /* Smooth transitions */
}

.quality-selector .quality-options {
    width: 240px;
    height: 48px;
    transition: all 0.3s ease-in-out; /* Smooth transitions */
}

.action-buttons {
    display: flex !important;
    align-items: center;
    gap: 0.875rem;
    flex-shrink: 0;
    min-height: 48px;
    visibility: visible !important;
    min-width: max-content; /* Prevent shrinking below content size */
    transition: all 0.3s ease-in-out; /* Smooth transitions */
}

.action-buttons button {
    transition: all 0.3s ease-in-out; /* Smooth transitions for buttons */
}

.generate-button-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease-in-out; /* Smooth transitions */
}

/* ================= RIGHT SIDEBAR PLACEHOLDER ================= */

.coming-soon-indicator {
    text-align: center;
    padding: 2rem 1rem;
}

.coming-soon-indicator h3 {
    color: #9CA3AF; /* text-gray-400 */
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.coming-soon-indicator p {
    color: #6B7280; /* text-gray-500 */
    font-size: 0.875rem;
    line-height: 1.4;
}

.coming-soon-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    opacity: 0.5;
    color: #6B7280;
}

/* ================= RESPONSIVE DESIGN ================= */

/* Wide screens - maintain horizontal layout with optimal spacing */
@media (min-width: 1400px) {
    .controls-bottom-row {
        max-width: 800px; /* Wider on large screens */
        gap: 2rem !important;
        justify-content: flex-end !important; /* Keep right alignment */
    }
    
    .combined-controls {
        gap: 2rem !important; /* More spacing on large screens */
        /* justify-content: flex-end !important; */
    }
    
    .action-buttons {
        gap: 1.25rem; /* Increased spacing between action buttons */
    }
}

/* Medium-wide screens - maintain horizontal layout */
@media (min-width: 1200px) and (max-width: 1399px) {
    .controls-bottom-row {
        max-width: 720px;
        gap: 1.75rem !important;
        justify-content: flex-end !important; /* Keep right alignment */
    }
    
    .combined-controls {
        gap: 1.75rem !important;
        /* justify-content: flex-end !important; */
    }
    
    .action-buttons {
        gap: 1rem; /* Consistent spacing between action buttons */
    }
}

/* Intermediate breakpoint for narrow but not mobile screens */
@media (min-width: 900px) and (max-width: 1199px) {
    .controls-bottom-row {
        max-width: 100%;
        padding: 10px 1rem !important;
        justify-content: center !important;
    }
    
    .combined-controls {
        flex-direction: row!important;
        align-items: center !important;
        gap: 1rem !important;
        width: 100% !important;
    }
    
    .quality-selector {
        justify-content: center;
        width: 100%;
        max-width: 300px;
    }
    
    .action-buttons {
        justify-content: center;
        width: 100%;
        max-width: 350px;
        gap: 1rem;
    }
}

@media (max-width: 1280px) {
    .app-container {
        grid-template-columns: 368px 1fr; /* Remove right sidebar column for this breakpoint too */
    }
}

@media (max-width: 1024px) {
    .app-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
        position: relative;
        margin-top: 62px; /* Ensure top navigation space */
    }
    
    .left-sidebar {
        position: fixed;
        top: 62px; /* Position below top navigation */
        left: 0;
        width: 350px;
        max-width: 80vw;
        height: calc(100vh - 62px); /* Account for top navigation */
        background-color: #1F2937;
        border-right: 1px solid #374151;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow-y: auto;
        padding: 1rem;
    }
    
    .left-sidebar.open {
        transform: translateX(0);
    }
    
    .main-center-panel {
        grid-column: 1;
        grid-row: 1;
        padding: 0.75rem;
        padding-top: 4rem; /* Space for hamburger button */
        gap: 0.875rem; /* Optimized gap for mobile */
        height: calc(100vh - 62px); /* Account for top navigation */
        overflow-y: auto;
    }
    
    .right-sidebar {
        display: none; /* Hide on tablet */
    }
    
    .preview-container {
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Tablet: Apply vertical stacking for controls with improved organization */
    .controls-bottom-row {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1.25rem;
        width: 100%;
        padding: 0 0.5rem; /* Add padding for mobile */
        max-width: 100% !important; /* Override fixed width */
    }
    
    /* Stack combined controls vertically on tablet */
    .combined-controls {
      
        align-items: center !important;
        gap: 1rem !important;
        width: 100% !important;
    }
    
    .prompt-controls-section {
        padding: 0 1rem; /* Reduced padding on tablet for more space */
        max-width: 100%; /* Use full width on tablet */
    }
    
    .quality-selector {
        justify-content: center;
        width: 100%;
        max-width: 320px; /* Constrain width on mobile */
    }
    
    .quality-selector .quality-options {
        width: 100%;
        max-width: 280px; /* Responsive width for tablet */
        min-width: 200px; /* Minimum usable width */
    }
    
    .action-buttons {
        justify-content: center;
        width: 100%;
        max-width: 400px; /* Provide more space for action buttons */
        gap: 0.875rem; /* Optimized gap for mobile */
        order: 2;
        margin-left: 0;
        flex-wrap: wrap; /* Allow wrapping if needed */
    }
    
    .generate-button-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
        justify-content: center;
        min-width: max-content; /* Prevent button shrinking */
    }
    
    .prompt-textarea {
        font-size: 0.8125rem; /* Slightly smaller text on mobile */
        min-height: 100px; /* Reduce height on mobile for more space */
    }
    
    /* hamburger-menu-btn styles moved to top navigation fixes section */
    
    /* Show hamburger button only on tablet and mobile */
    @media (max-width: 1024px) {
        /* hamburger button styles are in navigation fixes section */
        
        /* Show and style close button inside sidebar */
        .left-sidebar .sidebar-close-btn {
            display: flex !important;
            visibility: visible !important;
        }
        
        .left-sidebar .sidebar-close-btn:hover {
            background-color: #374151;
            color: #F9FAFB;
        }
        
        .left-sidebar .sidebar-close-btn:focus {
            outline: none;
            background-color: #374151;
            color: #F9FAFB;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
        }
    }
    
    /* Backdrop overlay */
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    
    .sidebar-backdrop.open {
        opacity: 1;
        visibility: visible;
    }
    
    /* Close button inside sidebar - hidden on desktop, visible on tablet/mobile */
    .left-sidebar .sidebar-close-btn {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: transparent;
        border: none;
        color: #9CA3AF;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s ease-in-out;
        display: none !important; /* Hidden by default (desktop) with !important */
        visibility: hidden !important; /* Also ensure visibility is hidden */
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
    }
    
    /* Explicitly hide close button on desktop */
    @media (min-width: 1025px) {
        .left-sidebar .sidebar-close-btn {
            display: none !important;
            visibility: hidden !important;
        }
    }
    
    /* Prevent body scroll when sidebar is open */
    body.sidebar-open {
        overflow: hidden;
    }
}

/* Combined controls responsive adjustment for 401px to 767px range */
@media (min-width: 401px) and (max-width: 767px) {
    .combined-controls {
        justify-content: space-between !important;
    }
}

/* Combined controls responsive adjustment for 767px to 1200px range */
@media (min-width: 767px) and (max-width: 1200px) {
    .combined-controls {
        justify-content: space-between !important;
    }
}

/* Tablet layout adjustments for 600px to 768px range */
@media (min-width: 600px) and (max-width: 768px) {
    .templates-grid {
        grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
    
    .mood-expression-picker-section img {
        width: 48px !important;
        height: 48px !important;
        max-width: 48px !important;
        max-height: 48px !important;
        min-width: 48px !important;
        min-height: 48px !important;
    }
}

@media (max-width: 768px) {
    .left-sidebar {
        width: 100vw;
        max-width: 100vw;
        top: 62px; /* Position below top navigation */
        height: calc(100vh - 62px);
    }
}

/* ================= MOBILE PORTRAIT SIDEBAR SCROLLING FIX ================= */
@media (max-width: 768px) and (orientation: portrait) {
    /* Mobile Portrait: Enhanced sidebar scrolling fix */
    .left-sidebar {
        width: 100vw !important;
        max-width: 100vw !important;
        top: 62px !important; /* Position below top navigation */
        height: calc(100vh - 62px) !important; /* Full available height */
        max-height: calc(100vh - 62px) !important;
        overflow-y: auto !important; /* Enable vertical scrolling */
        overflow-x: hidden !important; /* Prevent horizontal scroll */
        padding: 1rem !important;
        padding-bottom: 2rem !important; /* Extra bottom padding for safe scrolling */
        display: flex !important;
        flex-direction: column !important;
        /* iOS Safari safe area support */
        padding-bottom: max(2rem, env(safe-area-inset-bottom, 2rem)) !important;
    }
    
    /* Control panel container - ensure it can expand to full content height */
    .left-sidebar #control-panel-main-container {
        max-height: none !important; /* Remove height constraints */
        min-height: auto !important;
        flex: 1 !important; /* Take available space */
        overflow: visible !important; /* Let parent handle scrolling */
        padding-bottom: 1rem !important; /* Bottom spacing */
    }
    
    /* Scrollable controls area - remove height constraints */
    .left-sidebar .design-controls-scrollable {
        max-height: none !important; /* Remove height limit */
        overflow: visible !important; /* Let parent handle scrolling */
        flex: 1 !important;
        min-height: auto !important;
    }
    
    /* Ensure collapsible sections can expand fully */
    .left-sidebar .collapsible-content {
        overflow: visible !important;
    }
    
    /* Add subtle bottom fade indicator */
    .left-sidebar::after {
        content: '';
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(to top, rgba(31, 41, 55, 0.9), transparent);
        pointer-events: none;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    /* Show fade indicator when sidebar is scrollable */
    .left-sidebar.open::after {
        opacity: 1;
    }
    
    /* Ensure body doesn't scroll when sidebar is open */
    body.sidebar-open {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
    }
    
    /* Main center panel adjustments when sidebar is open */
    body.sidebar-open .main-center-panel {
        overflow: hidden !important;
    }
}

/* Additional mobile landscape fix */
@media (max-width: 768px) and (orientation: landscape) {
    .left-sidebar {
        height: calc(100vh - 62px) !important;
        overflow-y: auto !important;
        padding-bottom: 1.5rem !important;
    }
    
    .left-sidebar #control-panel-main-container {
        max-height: none !important;
        overflow: visible !important;
    }
    
    .left-sidebar .design-controls-scrollable {
        max-height: none !important;
        overflow: visible !important;
    }
}

@media (max-width: 768px) {
    .main-center-panel {
        padding: 0.75rem;
        padding-top: 3.5rem; /* Adjusted for smaller hamburger button */
        gap: 0.875rem; /* Optimized gap for mobile */
        height: calc(100vh - 62px);
    }
    
    .controls-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    /* Mobile: Enhanced organized layout with proper edge spacing */
    .controls-bottom-row {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem; /* Better organized spacing */
        width: 100%;
        padding: 1.25rem 1.5rem; /* Increased horizontal padding from edges */
        max-width: calc(100% - 2rem) !important; /* Account for margin from screen edges */
        background: rgba(17, 24, 39, 0.8);
        border-radius: 16px;
        border: 1px solid rgba(55, 65, 81, 0.3);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        box-shadow: none; /* Remove box shadow as requested */
        margin: 0 1rem; /* Additional margin from screen edges */
    }
    
    /* Organized combined controls with improved spacing */
    .combined-controls {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important; /* Better spacing between quality and actions */
        width: 100% !important;
        max-width: calc(100% - 1rem) !important; /* Account for internal padding */
        padding: 0 0.5rem; /* Additional internal padding */
    }
    
    /* Quality selector with proper spacing */
    .quality-selector {
        justify-content: center;
        width: 100%;
        max-width: calc(100% - 1rem); /* Account for padding */
        padding: 0 0.5rem; /* Padding from container edges */
        order: 1;
        margin-bottom: 0.75rem; /* Better spacing below quality selector */
    }
    
    .quality-selector .quality-options {
        width: 100% !important;
        max-width: 100% !important;
        min-width: 280px !important;
        height: 44px !important; /* Keep original height */
        padding: 3px !important; /* Keep original padding */
        margin: 0 auto;
        /* Preserve original colors and styling */
        background-color: rgb(63, 63, 70) !important; /* Keep original gray */
        border-radius: 13.4px !important; /* Keep original border radius */
        display: grid !important;
        grid-template-columns: 1fr 1fr 1fr !important;
        gap: 0 !important; /* Remove gap to match original */
        align-items: center !important;
        position: relative !important;
        overflow: visible !important;
    }
    
    /* Quality options animated pill - preserve original animation */
    .quality-selector .quality-options > div:first-child {
        height: calc(100% - 6px) !important; /* Keep original pill sizing */
        border-radius: 13.4px !important; /* Keep original border radius */
        background: #3B82F6 !important; /* Keep original blue */
        z-index: 1 !important;
        transition: transform 0.3s ease-in-out !important; /* Keep original animation */
        position: absolute !important;
        top: 3px !important;
        left: 3px !important;
        width: calc(33.333% - 2px) !important; /* Keep original pill width */
    }
    
    /* Quality option buttons - preserve original styling */
    .quality-selector .quality-options button {
        font-size: 0.875rem !important; /* Keep original font size */
        font-weight: 600 !important; /* Keep original font weight */
        padding: 8px 12px !important; /* Keep original padding */
        min-height: 36px !important; /* Keep original button height */
        border-radius: 0 !important; /* Keep original (no border radius on buttons) */
        background: transparent !important;
        border: none !important;
        color: #9CA3AF !important; /* Keep original gray text */
        transition: color 0.2s ease !important; /* Keep original transition */
        z-index: 10 !important; /* Keep original z-index */
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        outline: none !important;
        line-height: 1.2 !important; /* Keep original line height */
    }
    
    /* Active quality button - preserve original colors */
    .quality-selector .quality-options button[aria-pressed="true"] {
        color: #FFFFFF !important; /* Keep original white text when active */
        font-weight: 600 !important; /* Keep original font weight */
    }
    
    /* Hover state - preserve original behavior */
    .quality-selector .quality-options button:hover:not([aria-pressed="true"]) {
        color: #D1D5DB !important; /* Keep original hover color */
    }
    
    /* Focus state - keep original behavior */
    .quality-selector .quality-options button:focus {
        outline: none !important;
    }
    
    /* Quality selector label - keep original styling */
    .quality-selector span {
        font-size: 0.875rem !important; /* Keep original size */
        font-weight: normal !important; /* Keep original weight */
        color: #9CA3AF !important; /* Keep original gray color */
        margin-bottom: 0 !important; /* Keep original spacing */
    }
    
    .action-buttons {
        display: flex;
        justify-content: center;
        width: 100%;
        max-width: calc(100% - 1rem); /* Account for padding */
        gap: 0.875rem; /* Better organized spacing */
        order: 2;
        margin-top: 0; /* Remove top margin for cleaner spacing */
        flex-wrap: nowrap;
        padding: 0 0.5rem; /* Padding from container edges */
        box-shadow: none !important; /* Remove any box shadows */
    }
    
    .action-buttons button {
        flex: 1; /* Equal width buttons */
        min-height: 48px; /* Larger touch targets */
        max-width: 180px; /* Prevent buttons from being too wide */
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.2s ease;
        white-space: nowrap;
        background: rgba(55, 65, 81, 0.8);
        border: 1px solid rgba(75, 85, 99, 0.4);
        color: #F3F4F6;
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
    
    /* Generate button special styling */
    .action-buttons button[class*="generate"] {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
        border: 1px solid rgba(59, 130, 246, 0.4);
        color: #FFFFFF;
        font-weight: 700;
    }
    
    /* Download button styling */
    .action-buttons button[class*="download"] {
        background: linear-gradient(135deg, #10B981 0%, #059669 100%);
        border: 1px solid rgba(16, 185, 129, 0.4);
        color: #FFFFFF;
    }
    
    .action-buttons button:hover {
        transform: translateY(-1px);
        box-shadow: none; /* Remove box shadow from action buttons */
    }
    
    .action-buttons button:active {
        transform: translateY(0);
    }
    
    .generate-button-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        width: 100%;
        justify-content: center;
        min-width: max-content;
    }
    
    .prompt-textarea {
        font-size: 0.8125rem;
        min-height: 100px;
    }
}

/* Landscape orientation adjustments for tablets */
@media (max-width: 1024px) and (orientation: landscape) {
    .controls-bottom-row {
        flex-direction: row !important;
        justify-content: center !important;
        flex-wrap: wrap;
        gap: 1rem !important;
        padding: 8px 1rem !important;
    }
    
    .combined-controls {
        flex-direction: row !important;
        align-items: center !important;
        gap: 1rem !important;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .quality-selector {
        max-width: 280px;
    }
    
    .action-buttons {
        max-width: 320px;
        gap: 0.75rem;
    }
}

/* Very narrow screens - extra small devices */
@media (max-width: 480px) {
    .controls-bottom-row {
        gap: 1rem !important;
        padding: 1rem 1.25rem !important; /* More padding on very small screens */
        margin: 0 0.75rem; /* Margin from screen edges */
        max-width: calc(100% - 1.5rem) !important; /* Account for margins */
    }
    
    .quality-selector {
        max-width: 100% !important;
        padding: 0 !important;
    }
    
    .quality-selector .quality-options {
        max-width: 100% !important;
        min-width: 250px !important; /* Slightly smaller for very narrow screens */
        height: 44px !important; /* Keep original height */
        padding: 3px !important; /* Keep original padding */
        /* Preserve original colors and styling */
        background-color: rgb(63, 63, 70) !important; /* Keep original gray */
        border-radius: 13.4px !important; /* Keep original border radius */
    }
    
    .quality-selector .quality-options > div:first-child {
        height: calc(100% - 6px) !important; /* Keep original pill sizing */
        border-radius: 13.4px !important; /* Keep original border radius */
        background: #3B82F6 !important; /* Keep original blue */
        width: calc(33.333% - 2px) !important; /* Keep original pill width */
    }
    
    .quality-selector .quality-options button {
        font-size: 0.8rem !important; /* Slightly smaller for narrow screens */
        padding: 8px 6px !important; /* Adjust padding for narrow screens */
        min-height: 36px !important; /* Keep original button height */
        /* Preserve original colors */
        color: #9CA3AF !important; /* Keep original gray text */
        font-weight: 600 !important; /* Keep original font weight */
    }
    
    .action-buttons {
        max-width: 100% !important;
        gap: 0.75rem !important;
        padding: 0 !important;
    }
    
    .action-buttons button {
        min-height: 44px !important;
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem !important;
        max-width: 160px !important;
    }
}

/* Extra responsive adjustments for very wide screens */
@media (min-width: 1600px) {
    .controls-bottom-row {
        gap: var(--controls-gap-desktop);
    }

    .combined-controls {
        gap: var(--controls-gap-desktop);
    }

    .action-buttons {
        gap: var(--controls-gap-desktop);
    }
}

/* Additional responsive breakpoint for narrow screens */
@media (max-width: 600px) {
    .controls-bottom-row {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
    }
    
    .combined-controls {
        flex-direction: column !important;
        align-items: center !important;
        gap: 0.875rem !important;
    }
}

/* Additional responsive fine-tuning for aspect ratios */
@media (max-width: 1024px) and (max-height: 600px) {
    /* Short screens - compact layout */
    .controls-bottom-row {
        padding: 6px 0.5rem !important;
        gap: 0.75rem !important;
        min-height: 60px !important;
    }
    
    .quality-selector .quality-options {
        height: 36px;
    }
    
    .action-buttons button {
        min-height: 36px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* Fine-tuning for ultra-wide screens */
@media (min-width: 1920px) {
    .controls-bottom-row {
        max-width: 1000px;
        gap: 2.5rem !important;
    }
    
    .combined-controls {
        gap: 2.5rem !important;
    }
    
    .action-buttons {
        gap: 1.5rem;
    }
    
    .quality-selector .quality-options {
        width: 240px;
        height: 48px;
    }
}

    /* Enhanced mobile prompt controls */
    .prompt-controls-section {
        position: sticky;
        bottom: 0;
        background-color: #111827;
        padding: 1.25rem 1rem; /* Increased padding for better touch targets */
        margin-top: auto;
        max-width: 100%;
        width: 100%;
    }
    
    /* Ensure preview area doesn't get hidden by fixed controls */
    .preview-workspace-section {
        padding-bottom: 1rem;
    }
}

/* ================= ERROR BANNER (Unchanged) ================= */

.error-banner-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    z-index: 1000;
    pointer-events: none;
}

.error-banner {
    background-color: #C53030;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    opacity: 0;
    transform: translateY(-150%);
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
    pointer-events: auto;
    position: relative;
}

.error-banner.show {
    opacity: 1;
    transform: translateY(0);
}

.error-banner-close-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    padding: 0.25rem;
}

/* Optional: Add a simple icon if you don't use Heroicons for this */
.error-banner::before {
    /* content: '⚠️'; */ /* Example with emoji */
    /* font-size: 1.25rem; */
}

/* ================= MODAL OVERLAY & POPUP ================= */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(24, 24, 27, 0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-popup {
    z-index: 11;
    position: relative;
    background: #232336;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.45);
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* ================= INDEPENDENT TOOLTIP SYSTEM ================= */

/* Fixed positioning tooltip that ignores container boundaries */
.tooltip-fixed {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: none;
    transform-origin: center;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

/* Tooltip arrow for fixed tooltips */
.tooltip-arrow-fixed {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
    z-index: -1;
}

/* Arrow positioning classes */
.tooltip-arrow-top {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-bottom {
    top: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-left {
    right: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

.tooltip-arrow-right {
    left: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

/* Ensures tooltips stay within viewport bounds */
.tooltip-container {
    position: relative;
    display: inline-block;
}

/* Override any container overflow that might clip tooltips */
.tooltip-no-clip {
    overflow: visible !important;
}

/* Panel preview container with responsive aspect ratio 16:9 */
.panel-preview-container {
    width: 100%;
    max-width: 652px;
    aspect-ratio: 16 / 9;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #181C23;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.18);
    overflow: hidden;
    margin: 0 auto;
}

@media (max-width: 700px) {
  .panel-preview-container {
    width: 100vw;
    max-width: 100vw;
    aspect-ratio: 16 / 9;
    min-width: 0;
    min-height: 0;
  }
}

/* ================= Face Source Tab Styling (Upload/URL) ================= */

.face-source-tab-navigation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 260px; /* Smaller than main tabs */
    height: 45px; /* Smaller height than main tabs */
    margin: 0 auto;
    background: transparent;
}

.face-source-tab-group {
    display: flex;
    width: 100%;
    height: 100%;
    background: rgba(55, 65, 81, 0.6); /* bg-gray-700/60 */
    border-radius: 12px; /* Slightly smaller border radius */
    padding: 3px;
    position: relative;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(75, 85, 99, 0.3); /* border-gray-600/30 */
}

/* Moving indicator for face source tabs */
.face-source-tab-group::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: calc(50% - 3px);
    height: calc(100% - 6px);
    background: linear-gradient(135deg, rgb(147, 51, 234) 0%, rgb(126, 34, 206) 100%); /* Purple gradient */
    border-radius: 9px;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.face-source-tab-group.url-active::before {
    transform: translateX(calc(100% + 3px)); /* Move to second tab position */
}

.face-source-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px; /* Smaller gap than main tabs */
    width: 50%;
    height: 100%;
    font-size: 14px; /* Smaller font than main tabs */
    font-weight: 500;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 9px;
}

.face-source-tab-button.active-tab {
    background: transparent;
    color: #FFFFFF;
    position: relative;
}

.face-source-tab-button.active-tab .iconify,
.face-source-tab-button.active-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
    color: #FFFFFF;
}

.face-source-tab-button.inactive-tab {
    background: transparent;
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.face-source-tab-button.inactive-tab .iconify,
.face-source-tab-button.inactive-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
}

.face-source-tab-button.inactive-tab:hover {
    color: #F3F4F6;
}

.face-source-tab-button:focus {
    outline: none;
    box-shadow: none;
}

/* Icon styling within face source tabs */
.face-source-tab-button .iconify {
    width: 16px; /* Smaller than main tabs */
    height: 16px;
    flex-shrink: 0;
}

.face-source-tab-button.active-tab .iconify {
    color: #FFFFFF;
}

.face-source-tab-button.inactive-tab .iconify {
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.face-source-tab-button.inactive-tab:hover .iconify {
    color: #F3F4F6;
}

/* Text styling for face source tab labels */
.face-source-tab-button span:not(.iconify) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 14px; /* Smaller than main tabs */
    line-height: 20px;
    letter-spacing: -0.01em;
}

/* Responsive adjustments for face source tabs */
@media (max-width: 640px) {
    .face-source-tab-navigation-container {
        max-width: 240px;
        height: 42px;
    }
    
    .face-source-tab-group {
        border-radius: 10px;
        padding: 2px;
    }
    
    .face-source-tab-group::before {
        top: 2px;
        left: 2px;
        width: calc(50% - 2px);
        height: calc(100% - 4px);
        border-radius: 8px;
    }
    
    .face-source-tab-group.url-active::before {
        transform: translateX(calc(100% + 2px));
    }
    
    .face-source-tab-button {
        font-size: 13px;
        gap: 4px;
        border-radius: 8px;
    }
    
    .face-source-tab-button .iconify {
        width: 14px;
        height: 14px;
    }
    
    .face-source-tab-button span:not(.iconify) {
        font-size: 13px;
        line-height: 18px;
    }
}

@media (max-width: 480px) {
    .face-source-tab-navigation-container {
        max-width: 220px;
        height: 40px;
    }
    
    .face-source-tab-button {
        font-size: 12px;
        gap: 3px;
    }
    
    .face-source-tab-button .iconify {
        width: 12px;
        height: 12px;
    }
    
    .face-source-tab-button span:not(.iconify) {
        font-size: 12px;
        line-height: 16px;
    }
}

/* ================= Main Tab Styling (Editor/Templates) ================= */

.main-tab-navigation-container {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    width: 324px;
    height: 57px;
    margin-left: auto;
    margin-right: auto;
}

.main-tab-group {
    width: 324px;
    height: 57px;
    background: rgb(57, 65, 80); /* #394150 - matches Figma r: 0.2235, g: 0.2549, b: 0.3137 */
    border-radius: 14px;
    padding: 0;
    display: flex;
    position: relative;
    box-shadow: none;
    border: none;
}

/* Animated highlight background that slides between tabs */
.main-tab-group::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 156px; /* Updated from 150px to 156px per Figma */
    height: 51px; /* Updated from 47px to 51px per Figma */
    background: linear-gradient(135deg, #4285F4 0%, #006FEE 100%); /* Updated gradient direction and colors */
    border-radius: 14px; /* cornerRadius from Figma JSON */
    z-index: 1;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0); /* Default position for first tab */
}

/* Move highlight to second tab when templates is active */
.main-tab-group.templates-active::before {
    transform: translateX(162px); /* Move to second tab position */
}

.main-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px; /* Updated from 12px to 8px per Figma */
    width: 162px; /* Updated from 192.5px to 162px per Figma */
    height: 57px;
    font-size: 16px;
    font-weight: 500;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: none;
    background: transparent;
    cursor: pointer;
}

/* Remove old ::before pseudo-elements */
.main-tab-button.active-tab {
    color: #FFFFFF; /* text-white */
    position: relative;
    z-index: 2;
}

.main-tab-button.active-tab .iconify,
.main-tab-button.active-tab span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 3;
}

.main-tab-button.inactive-tab {
    color: #D1D5DB; /* text-gray-300 */
    position: relative;
    z-index: 1;
}

.main-tab-button.inactive-tab .iconify,
.main-tab-button.inactive-tab span:not(.iconify) {
    color: rgb(161, 161, 170) !important; /* #A1A1AA */
    position: relative;
    z-index: 2;
}

.main-tab-button.inactive-tab:hover {
    color: #FFFFFF; /* text-white */
}

.main-tab-button.inactive-tab:hover .iconify,
.main-tab-button.inactive-tab:hover span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 2;
}

.main-tab-button:focus {
    outline: none;
    box-shadow: none;
}

/* Ensure active tab text stays white in all states */
.main-tab-button.active-tab,
.main-tab-button.active-tab:focus,
.main-tab-button.active-tab:hover,
.main-tab-button.active-tab:active {
    color: #FFFFFF !important;
}

.main-tab-button.active-tab .iconify,
.main-tab-button.active-tab:focus .iconify,
.main-tab-button.active-tab:hover .iconify,
.main-tab-button.active-tab:active .iconify,
.main-tab-button.active-tab span:not(.iconify),
.main-tab-button.active-tab:focus span:not(.iconify),
.main-tab-button.active-tab:hover span:not(.iconify),
.main-tab-button.active-tab:active span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 3;
}

/* Icon styling within tabs */
.main-tab-button .iconify {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.main-tab-button.active-tab .iconify {
    color: #FFFFFF;
}

.main-tab-button.inactive-tab .iconify {
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.main-tab-button.inactive-tab:hover .iconify {
    color: #F3F4F6;
}

/* First tab positioning */
.main-tab-button:first-child {
    border-radius: 14px 0 0 14px;
}

/* Second tab positioning */
.main-tab-button:last-child {
    border-radius: 0 14px 14px 0;
}

/* Text styling for tab labels */
.main-tab-button span:not(.iconify) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: -0.01em;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .main-tab-navigation-container {
        width: 100%;
        max-width: 324px;
        height: 57px;
        padding: 0 4px; /* Add small padding for better spacing */
    }
    
    .main-tab-group {
        width: 100%;
        max-width: 324px;
        position: relative;
    }
    
    .main-tab-group::before {
        width: calc(50% - 6px); /* More precise calculation */
        left: 3px; /* Consistent positioning */
        transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .main-tab-group.templates-active::before {
        transform: translateX(calc(100% + 6px)); /* More precise positioning */
    }
    
    .main-tab-button {
        width: calc(50% - 2px); /* Ensure consistent width for both tabs */
        max-width: 158px; /* Prevent overflow */
        font-size: 14px;
        gap: 6px;
        height: 57px;
        margin: 0 1px; /* Small margin for visual separation */
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 14px; /* Consistent border radius on mobile */
    }
    
    /* Override individual border radius on mobile for consistency */
    .main-tab-button:first-child,
    .main-tab-button:last-child {
        border-radius: 14px;
    }
    
    .main-tab-button .iconify {
        width: 18px;
        height: 18px;
        flex-shrink: 0;
    }
    
    .main-tab-button span:not(.iconify) {
        font-size: 14px;
        line-height: 24px;
        white-space: nowrap; /* Prevent text wrapping */
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Additional mobile refinements for very small screens */
@media (max-width: 480px) {
    .main-tab-navigation-container {
        max-width: 300px;
        padding: 0 2px;
    }
    
    .main-tab-group {
        max-width: 300px;
    }
    
    .main-tab-button {
        font-size: 13px;
        gap: 4px;
        padding: 0 8px;
    }
    
    .main-tab-button .iconify {
        width: 16px;
        height: 16px;
    }
    
    .main-tab-button span:not(.iconify) {
        font-size: 13px;
        line-height: 22px;
    }
}

/* ================= Template Grid Layout - macOS Liquid Glass Design - BLUE THEME ================= */

/* Main templates grid - responsive layout */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2 columns by default */
    gap: 11px; /* Reduced from 20px by 45% */
    width: 100%;
    max-width: none;
    margin: 0 auto;
    justify-content: center;
    padding: 0 6px; /* Slightly increased padding */
}

/* Template category cards - macOS Liquid Glass Effect - BLUE THEME */
.template-category-card,
.add-new-card {
    position: relative;
    width: 100%;
    min-height: 140px;
    max-width: 100%;
    border-radius: 22px; /* Increased border radius for more modern look */
    overflow: hidden;
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
    
    /* Blue Glass Background */
    background: linear-gradient(145deg, 
        rgba(0, 111, 238, 0.08) 0%, 
        rgba(0, 111, 238, 0.04) 50%, 
        rgba(0, 111, 238, 0.02) 100%);
    
    /* Blue Glass Border */
    border: 1.5px solid rgba(0, 111, 238, 0.15);
    
    /* Premium Blue Shadows */
    box-shadow: 
        0 8px 32px rgba(0, 111, 238, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.06);
    
    /* Backdrop blur for glass effect */
    backdrop-filter: blur(16px) saturate(140%);
    -webkit-backdrop-filter: blur(16px) saturate(140%);
}

/* Blue Glass shimmer effect - before pseudo-element - DISABLED */
.template-category-card::before,
.add-new-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 111, 238, 0.08) 50%, /* Reduced intensity from 0.15 to 0.08 */
        transparent 100%
    );
    /* animation: liquidShimmer 3s infinite; */ /* Disabled auto animation */
    z-index: 1;
    opacity: 0; /* Keep opacity at 0 to completely disable */
    transition: none; /* Remove transition to prevent any animation */
    display: none; /* Completely hide the shimmer element */
}

/* Blue hover effects */
.template-category-card:hover,
.add-new-card:hover {
    transform: translateY(-4px) scale(1.02);
    
    /* Enhanced blue glass on hover */
    background: linear-gradient(145deg, 
        rgba(0, 111, 238, 0.12) 0%, 
        rgba(0, 111, 238, 0.08) 50%, 
        rgba(0, 111, 238, 0.04) 100%);
    
    /* Enhanced blue border */
    border-color: rgba(0, 111, 238, 0.25);
    
    /* Enhanced blue shadows */
    box-shadow: 
        0 16px 64px rgba(0, 111, 238, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.18),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* Activate shimmer on hover - DISABLED */
.template-category-card:hover::before,
.add-new-card:hover::before {
    opacity: 0; /* Keep at 0 instead of 1 to disable shimmer */
    display: none; /* Ensure it stays hidden */
}

@keyframes liquidShimmer {
    0% { left: -100%; opacity: 0; }
    50% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* Blue feather gradient overlay - after pseudo-element */
.template-category-card::after,
.add-new-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        180deg,
        rgba(0, 111, 238, 0.02) 0%, /* Reduced from 0.05 to 0.02 */
        rgba(0, 111, 238, 0.04) 30%, /* Reduced from 0.08 to 0.04 */
        rgba(0, 111, 238, 0.06) 70%, /* Reduced from 0.12 to 0.06 */
        rgba(0, 111, 238, 0.08) 100% /* Reduced from 0.15 to 0.08 */
    );
    opacity: 0;
    transition: opacity 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: 1;
    pointer-events: none;
}

/* Activate blue feather overlay on hover */
.template-category-card:hover::after,
.add-new-card:hover::after {
    opacity: 1;
}

/* Active state with blue theme */
.template-category-card.active {
    border: 2px solid rgba(0, 111, 238, 0.4); /* Blue glass border */
    background: linear-gradient(145deg, 
        rgba(0, 111, 238, 0.15) 0%, 
        rgba(0, 111, 238, 0.10) 50%, 
        rgba(0, 111, 238, 0.06) 100%);
    
    box-shadow: 
        0 16px 48px rgba(0, 111, 238, 0.20),
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* Active hover state */
.template-category-card.active:hover {
    border-color: rgba(0, 111, 238, 0.5);
    box-shadow: 
        0 20px 64px rgba(0, 111, 238, 0.25),
        0 10px 32px rgba(0, 0, 0, 0.20),
        inset 0 1px 0 rgba(255, 255, 255, 0.10);
}

/* Category card content - enhanced glass container - NO ANIMATIONS */
.template-category-content {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 20px; /* Slightly smaller than parent for inner glass effect */
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-end;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box;
    aspect-ratio: 16/9; /* Maintain proper aspect ratio */
    
    /* Subtle inner shadow for depth */
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    
    /* Fallback background with glass tint */
    background-color: rgba(248, 72, 72, 0.8);
    
    /* Ensure no animations interfere with other UI elements */
    animation: none !important;
    transform: none !important;
}

/* Category label overlay - Enhanced glass effect */
.template-category-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 34px; /* Slightly increased for better proportion */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 14px; /* Increased padding */
    box-sizing: border-box;
    
    /* Enhanced glass background */
    background: linear-gradient(180deg, 
        rgba(0, 0, 0, 0.3) 0%, 
        rgba(0, 0, 0, 0.6) 100%);
    
    /* Premium backdrop blur */
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    
    /* Enhanced shadows and borders */
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        0 -1px 0 rgba(255, 255, 255, 0.1);
    
    border-radius: 0 0 20px 20px; /* Match parent radius */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.template-category-label span {
    color: white;
    font-size: 14px;
    font-weight: 600; /* Slightly bolder for better readability */
    text-align: left;
    line-height: 20px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    /* Enhanced text shadow for glass effect */
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5),
                 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Add New card specific styling - Glass enhancement */
.add-new-card {
    /* Inherits all glass styles from above */
}

.add-new-card .template-category-content {
    /* Glass background for add-new card */
    background: linear-gradient(145deg, 
        rgba(161, 161, 170, 0.15) 0%, 
        rgba(161, 161, 170, 0.08) 50%, 
        rgba(161, 161, 170, 0.05) 100%);
    
    /* Ensure proper centering */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}

.add-new-card .iconify {
    font-size: 32px !important;
    color: rgba(113, 113, 122, 0.9);
    margin-bottom: 8px;
    transition: all 0.35s cubic-bezier(0.4, 0.8, 0.2, 1);
    
    /* Glass icon shadow */
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
}

.add-new-card:hover .iconify {
    transform: scale(1.1);
    color: rgba(139, 139, 150, 1);
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}

/* Responsive adjustments for glass template grid */
@media (max-width: 900px) {
    .templates-grid {
        gap: 10px; /* Reduced from 18px by 45% */
        padding: 0 8px;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 125px;
        border-radius: 20px;
    }
    
    .template-category-content {
        border-radius: 18px;
    }
    
    .template-category-label {
        min-height: 30px;
        padding: 6px 12px;
        border-radius: 0 0 18px 18px;
    }
    
    .template-category-label span {
        font-size: 13px;
        line-height: 18px;
    }
}

@media (max-width: 640px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 9px; /* Reduced from 16px by 45% */
        padding: 0 8px;
        justify-items: stretch;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 110px;
        border-radius: 18px;
    }
    
    .template-category-content {
        border-radius: 16px;
    }
    
    .template-category-label {
        min-height: 28px;
        padding: 6px 10px;
        border-radius: 0 0 16px 16px;
    }
    
    .template-category-label span {
        font-size: 12px;
        line-height: 16px;
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px; /* Reduced from 14px by 45% */
        padding: 0 6px;
        justify-content: center;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 95px;
        border-radius: 16px;
    }
    
    .template-category-content {
        border-radius: 14px;
    }
    
    .template-category-card.active {
        border: 2px solid rgba(99, 102, 241, 0.6);
        box-shadow: 
            0 8px 24px rgba(99, 102, 241, 0.2),
            0 2px 8px rgba(99, 102, 241, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
    
    .template-category-label {
        min-height: 26px;
        padding: 5px 8px;
        border-radius: 0 0 14px 14px;
    }
    
    .template-category-label span {
        font-size: 11px;
        line-height: 15px;
    }
    
    .add-new-card .iconify {
        font-size: 28px !important;
        margin-bottom: 6px;
    }
}

/* Ultra-small screens optimization */
@media (max-width: 360px) {
    .templates-grid {
        gap: 7px; /* Reduced from 12px by 45% */
        padding: 0 4px;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 90px;
        border-radius: 14px;
    }
    
    .template-category-content {
        border-radius: 12px;
        aspect-ratio: 16/9; /* Maintain aspect ratio on mobile */
        background-size: cover;
        background-position: center;
    }
    
    .template-category-label {
        border-radius: 0 0 12px 12px;
    }
    
    .template-category-label span {
        font-size: 10px;
        line-height: 14px;
    }
    
    .add-new-card .iconify {
        font-size: 24px !important;
    }
}

/* Accessibility - Respect reduced motion */
@media (prefers-reduced-motion: reduce) {
    .template-category-card,
    .add-new-card {
        transition: none;
    }
    
    .template-category-card:hover,
    .add-new-card:hover {
        transform: none;
    }
    
    .template-category-card::after,
    .add-new-card::after {
        animation: none;
    }
    
    .add-new-card:hover .iconify {
        transform: none;
    }
}

/* ================= TEMPLATE MODAL & ITEM CARDS - LIQUID GLASS ENHANCEMENT - BLUE THEME ================= */

/* Template item cards within modals - Blue Glass effect */
.template-item-card,
.expanded-template-item-card {
    position: relative;
    border-radius: 16px !important;
    overflow: hidden;
    border: 1.5px solid rgba(0, 111, 238, 0.12) !important;
    
    /* Blue macOS Liquid Glass Background */
    background: linear-gradient(145deg, 
        rgba(0, 111, 238, 0.08) 0%, 
        rgba(0, 111, 238, 0.04) 50%, 
        rgba(0, 111, 238, 0.02) 100%) !important;
    
    /* Backdrop blur for glass effect */
    backdrop-filter: blur(12px) saturate(150%) !important;
    -webkit-backdrop-filter: blur(12px) saturate(150%) !important;
    
    /* Premium blue shadows */
    box-shadow: 
        0 8px 32px rgba(0, 111, 238, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.06) !important;
    
    /* Smooth premium transitions */
    transition: all 0.35s cubic-bezier(0.165, 0.84, 0.44, 1) !important;
}

/* Blue Glass shimmer effect for template items - DISABLED */
.template-item-card::before,
.expanded-template-item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 111, 238, 0.06) 50%, /* Reduced intensity from 0.12 to 0.06 */
        transparent 100%
    );
    /* animation: templateShimmer 3s infinite; */ /* Disabled auto animation */
    z-index: 1;
    opacity: 0; /* Keep at 0 to completely disable */
    transition: none; /* Remove transition to prevent any animation */
    display: none; /* Completely hide the shimmer element */
}

/* Blue hover effects for template items */
.template-item-card:hover,
.expanded-template-item-card:hover {
    transform: translateY(-3px) scale(1.01) !important;
    border-color: rgba(0, 111, 238, 0.20) !important;
    
    /* Enhanced blue glass on hover */
    background: linear-gradient(145deg, 
        rgba(0, 111, 238, 0.12) 0%, 
        rgba(0, 111, 238, 0.08) 50%, 
        rgba(0, 111, 238, 0.04) 100%) !important;
    
    /* Enhanced blue shadows */
    box-shadow: 
        0 16px 48px rgba(0, 111, 238, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
}

/* Activate shimmer on hover - DISABLED */
.template-item-card:hover::before,
.expanded-template-item-card:hover::before {
    opacity: 0; /* Keep at 0 to disable shimmer */
    display: none; /* Ensure it stays hidden */
}

@keyframes templateShimmer {
    0% { left: -100%; opacity: 0; }
    50% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* Template item info styling */
.template-item-info,
.expanded-template-item-info {
    background: rgba(0, 111, 238, 0.04) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    border-top: 1px solid rgba(0, 111, 238, 0.08) !important;
}

/* Template item description styling */
.template-item-description,
.expanded-template-item-description {
    min-height: 2rem;
    margin-top: calc(0.35rem * calc(1 - var(--tw-space-y-reverse)));
}

/* Template item title styling */
.template-item-title,
.expanded-template-item-title {
    font-size: 1.0rem;
    margin-bottom: 0.48rem;
    font-weight: 500;
}

/* Template modal content - Premium Modal Enhancement */
.template-modal-content,
.show-more-modal-content {
    /* Solid, semi-opaque background for focused content */
    background: rgba(18, 22, 34, 0.85) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    border-radius: 16px !important;
    backdrop-filter: blur(20px) saturate(160%) !important;
    -webkit-backdrop-filter: blur(20px) saturate(160%) !important;
    
    /* Enhanced z-index for content layer */
    z-index: 10001 !important;
    position: relative !important;
    
    /* Premium shadows and elevation */
    box-shadow: 
        0 32px 80px rgba(0, 0, 0, 0.35),
        0 16px 40px rgba(0, 0, 0, 0.25),
        0 8px 16px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    
    /* Smooth transitions for premium feel */
    transition: all 350ms cubic-bezier(0.165, 0.84, 0.44, 1) !important;
    transform-origin: center;
}

/* ================= TEMPLATE MODAL RESPONSIVE FIXES ================= */

/* ================= USER DROPDOWN MENU STYLES ================= */

/* Dropdown animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes notificationSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Toast animations for success notifications - Enhanced with smooth fade transitions */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    60% {
        opacity: 1;
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    40% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Desktop-specific dropdown backdrop styles */
@media (min-width: 768px) {
    /* On desktop, create an invisible backdrop for click-outside detection */
    .dropdown-backdrop-desktop {
        position: fixed;
        inset: 0;
        z-index: 45;
        background: transparent; /* No visual backdrop on desktop */
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

/* User dropdown menu enhancements */
.user-dropdown-menu {
    /* Animation is now applied via inline style */
}

/* Enhanced pricing plan cards */
.plan-card {
    position: relative;
    overflow: visible; /* Allow badges to overflow */
}

/* Plan card hover effects */
.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Most Popular and Current Plan badges */
.plan-card > span:first-of-type {
    /* Position badges properly */
    z-index: 10;
}

/* Smooth transitions for all interactive elements */
.plan-button,
.menu-item,
.close-btn {
    transition: all 0.2s ease-in-out;
}

/* Focus states for accessibility */
.plan-button:focus,
.menu-item:focus,
.close-btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* Responsive adjustments for user dropdown */
@media (max-width: 640px) {
    .user-dropdown-menu {
        position: fixed !important; /* Change from absolute to fixed */
        width: calc(100vw - 2rem) !important; /* Leave some margin on sides */
        max-width: 360px !important; /* Max width for better readability */
        right: 1rem !important; /* 1rem margin from right */
        left: auto !important; /* Remove left positioning */
        top: 70px !important; /* Position below the top nav (62px + 8px gap) */
        border-radius: 12px !important; /* Consistent border radius */
        margin-top: 0 !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important; /* Add shadow for elevation */
    }
    
    /* Adjust plan cards for mobile */
    .plans-container {
        padding: 1rem !important;
    }
    
    .plan-card {
        padding: 1rem !important;
    }
    
    .features-list {
        font-size: 0.8125rem;
    }
}

/* Even smaller screens - extreme mobile portrait */
@media (max-width: 480px) {
    .user-dropdown-menu {
        width: calc(100vw - 1rem) !important; /* Less margin on very small screens */
        max-width: 320px !important; /* Smaller max width */
        right: 0.5rem !important; /* Smaller margin */
    }
}

/* Landscape orientation on mobile */
@media (max-width: 640px) and (orientation: landscape) {
    .user-dropdown-menu {
        max-width: 400px !important; /* Wider in landscape */
        max-height: 70vh !important; /* Limit height in landscape */
    }
}

/* Custom scrollbar for dropdown menu */
.user-dropdown-menu::-webkit-scrollbar {
    width: 8px;
}

.user-dropdown-menu::-webkit-scrollbar-track {
    background: #1F2937;
}

.user-dropdown-menu::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 4px;
}

.user-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

/* Firefox scrollbar */
.user-dropdown-menu {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
}

/* ================= NOTIFICATION ICON IMPROVEMENTS ================= */

/* Responsive notification icon sizing */
.notification-icon-size {
    font-size: 20px !important;
}

.notification-bell-icon {
    font-size: 20px !important;
}

/* Mobile responsive sizing - reduce by 15-20% */
@media (max-width: 640px) {
    .notification-icon-size {
        font-size: 16px !important; /* 20% reduction from 20px */
    }
    
    .notification-bell-icon {
        font-size: 17px !important; /* 15% reduction from 20px */
    }
} 
/* ================= PRICING MODAL STYLES ================= */

/* Simplified tooltip styling */
.tooltip-icon-container-simple {
    position: relative !important;
    display: inline-block !important;
    z-index: 1 !important;
}

.tooltip-simple-popup {
    position: absolute !important;
    z-index: 10000 !important;
    pointer-events: none !important;
    /* Ensure it's always visible when active */
    transform-origin: center bottom !important;
    will-change: opacity, visibility !important;
}

/* Make sure the tooltip container doesn't get clipped */
.tooltip-icon-container-simple:hover {
    z-index: 10001 !important;
}

/* Override any overflow hidden that might clip tooltips */
.face-upload-section, 
.person-settings-container,
.control-panel-main-container {
    overflow: visible !important;
}

/* Ensure tooltips are visible even in scrollable containers */
.tooltip-simple-popup {
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-8px) !important;
    margin: 0 !important;
}

/* Debug version - super visible */
.tooltip-simple-popup[style*="opacity: 1"] {
    background-color: #ef4444 !important; /* Bright red */
    color: #ffffff !important;
    border: 3px solid #fbbf24 !important; /* Yellow border */
    font-size: 14px !important;
    font-weight: bold !important;
    padding: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8) !important;
    border-radius: 8px !important;
}

/* Tooltip enhancements - ensure they work properly */
.tooltip-icon-container {
    position: relative;
    display: inline-block;
}

.tooltip-fixed-content {
    z-index: 10000 !important; /* Very high z-index to ensure visibility */
    background-color: #1f2937 !important; /* Clean dark gray background */
    color: #f9fafb !important; /* White text */
    border: none !important; /* Remove border for cleaner look */
    font-size: 12px !important; /* Smaller, cleaner text */
    font-weight: 400 !important; /* Normal weight */
    line-height: 1.4 !important;
    border-radius: 0.5rem !important; /* rounded-lg */
    padding: 0.75rem !important; /* Standard padding */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; /* Reduced by 20% - lighter shadow */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    pointer-events: none !important;
    text-shadow: none !important; /* Remove text shadow for cleaner look */
    min-width: 180px !important; /* Slightly smaller minimum width */
    min-height: auto !important; /* Auto height for better text fit */
}

/* Ensure tooltips don't get clipped by parent containers */
.tooltip-icon-container,
.face-upload-section, 
.person-settings-container,
.control-panel-main-container {
    overflow: visible !important;
}

/* Force visibility for tooltip content */
.tooltip-fixed-content[style*="visibility: visible"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure tooltip text doesn't wrap poorly */
.tooltip-fixed-content p {
    margin: 0 !important;
    padding: 0 !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    line-height: 1.4 !important;
    color: #f9fafb !important; /* Clean white text */
    font-weight: 400 !important; /* Normal weight */
}

/* Ensure modal has proper z-index */
.image-requirements-modal {
    z-index: 9999 !important;
}

/* Debug styles - temporary to check tooltip positioning */
.tooltip-fixed-content.debug {
    background: #ef4444 !important; /* Red background for debugging */
    border: 3px solid #fbbf24 !important; /* Yellow border for debugging */
    font-size: 16px !important;
    padding: 1rem !important;
}

/* ================= FOCUS RING CLIPPING FIX ================= */

/* Ensure mood expression picker and all its parents allow focus ring overflow */
.mood-expression-picker-section,
.mood-expression-picker-grid,
.person-settings-container,
.control-panel-main-container,
.left-sidebar,
.collapsible-section,
.collapsible-content {
    overflow: visible !important;
}

/* Specific fix for mood expression buttons to prevent focus ring clipping */
.mood-expression-picker-grid button:focus {
    position: relative;
    z-index: 10;
}

/* Ensure the left sidebar doesn't clip focus rings horizontally */
.left-sidebar {
    overflow-x: visible !important;
    overflow-y: auto !important; /* Keep vertical scrolling but allow horizontal overflow */
}

/* Ensure collapsible sections don't clip focus rings when expanded */
.collapsible-content {
    overflow: visible !important;
}

/* Force all containers in the chain to allow focus ring overflow */
.mood-expression-picker-section *,
.person-settings-container *,
.control-panel-main-container * {
    overflow: visible !important;
}

.tooltip-fixed-content {
    z-index: 10000 !important; /* Very high z-index to ensure visibility */
    background-color: #1f2937 !important; /* Clean dark gray background */
    color: #f9fafb !important; /* White text */
    border: none !important; /* Remove border for cleaner look */
    font-size: 12px !important; /* Smaller, cleaner text */
    font-weight: 400 !important; /* Normal weight */
    line-height: 1.4 !important;
    border-radius: 0.5rem !important; /* rounded-lg */
    padding: 0.75rem !important; /* Standard padding */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; /* Reduced by 20% - lighter shadow */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    pointer-events: none !important;
    text-shadow: none !important; /* Remove text shadow for cleaner look */
    min-width: 180px !important; /* Slightly smaller minimum width */
    min-height: auto !important; /* Auto height for better text fit */
}

/* ================= TOOLTIP OVERFLOW FIX ================= */
/* Ensure all parent containers allow tooltip overflow */
.premade-templates-section,
.premade-templates-section *,
.templates-tab-content,
.templates-tab-content *,
.flex-grow.overflow-y-auto,
.left-sidebar *,
.app-container *,
.main-tab-navigation-container,
.main-tab-group {
    overflow-x: visible !important;
}

/* ================= MODAL INTERFERENCE FIX ================= */
/* Hide tab navigation when any modal is open */
body:has(.template-modal-container[style*="opacity-100"]) .main-tab-navigation-container,
body:has(.show-more-modal-container[style*="opacity-100"]) .main-tab-navigation-container {
    display: none !important;
}

/* ================= TOP NAVIGATION LOGO STYLES ================= */

/* ================= TOP NAVIGATION ENHANCED LOGO POSITIONING ================= */

.top-nav-container {
    position: relative;
}

.top-nav-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0;
}

.top-nav-logo-img {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    vertical-align: middle;
    transition: all 0.2s ease;
}

.top-nav-logo-img:hover {
    filter: brightness(1.1) !important;
}

/* Logo positioning - Mobile centered, Desktop left-aligned */
.top-nav-center {
    z-index: 10;
    pointer-events: none; /* Allow clicks to pass through to underlying elements */
}

.top-nav-center .top-nav-logo-img {
    pointer-events: auto; /* Re-enable clicks on the logo itself */
}

/* Responsive logo sizing - Single logo element */
@media (max-width: 480px) {
    .top-nav-center .top-nav-logo-img {
        height: 36px !important;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .top-nav-center .top-nav-logo-img {
        height: 42px !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .top-nav-center .top-nav-logo-img {
        height: 48px !important;
    }
}

@media (min-width: 1025px) {
    .top-nav-center .top-nav-logo-img {
        height: 56px !important;
    }
}

@media (min-width: 1440px) {
    .top-nav-center .top-nav-logo-img {
        height: 60px !important;
    }
}

/* ================= END TOP NAVIGATION LOGO STYLES ================= */

/* ================= FORCE ACTION BUTTONS VISIBILITY ================= */

/* Force action buttons to be visible */
#action-buttons-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    min-height: 50px !important;
    align-items: center !important;
    gap: 1rem !important;
}

/* Ensure buttons are visible */
#action-buttons-row button {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 44px !important;
    border-radius: 200px;
}

/* Generate button shimmer effect - Enhanced implementation for reliable loading animation */
button.generating,
#action-buttons-row button:not(.download-btn),
.generate-button-container button {
    position: relative !important;
    overflow: hidden !important;
}

/* Shimmer pseudo-element setup */
button.generating::before,
#action-buttons-row button:not(.download-btn)::before,
.generate-button-container button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 60%;
    height: 100%;
    background: linear-gradient(120deg, 
        rgba(255,255,255,0) 0%, 
        rgba(255,255,255,0.1) 25%,
        rgba(255,255,255,0.2) 50%, 
        rgba(255,255,255,0.1) 75%,
        rgba(255,255,255,0) 100%);
    transform: skewX(-20deg);
    transition: none;
    pointer-events: none;
    opacity: 0;
    z-index: 1;
    border-radius: inherit;
}

/* Hover shimmer - single sweep animation when not loading */
#action-buttons-row button:not(.download-btn):not(.generating):hover::before,
.generate-button-container button:not(.generating):hover::before {
    animation: generate-shimmer-move 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 1;
}

/* Loading shimmer - continuous loop when generating (main enhancement) */
button.generating::before,
#action-buttons-row .generating::before,
.generate-button-container .generating::before {
    animation: generate-shimmer-move 1.4s cubic-bezier(0.4, 0, 0.2, 1) infinite !important;
    opacity: 1 !important;
}

/* Force shimmer animation on any button with generating class (failsafe) */
.generating::before {
    animation: generate-shimmer-move 1.4s cubic-bezier(0.4, 0, 0.2, 1) infinite !important;
    opacity: 1 !important;
    background: linear-gradient(120deg, 
        rgba(255,255,255,0) 0%, 
        rgba(255,255,255,0.1) 25%,
        rgba(255,255,255,0.2) 50%, 
        rgba(255,255,255,0.1) 75%,
        rgba(255,255,255,0) 100%) !important;
}

/* Enhanced shimmer animation with smooth movement */
@keyframes generate-shimmer-move {
    0% { 
        left: -100%; 
        opacity: 0;
    }
    25% {
        opacity: 1;
    }
    75% {
        opacity: 1;
    }
    100% { 
        left: 140%; 
        opacity: 0;
    }
}

/* Ensure button content stays above shimmer */
#action-buttons-row button:not([disabled]):not(.download-btn) > *,
.generate-button-container button > * {
    position: relative;
    z-index: 2;
}

/* Enhanced generate button hover effects (when not loading) */
#action-buttons-row button:not([disabled]):not(.download-btn):not(.generating):hover,
.generate-button-container button:not([disabled]):not(.generating):hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px rgba(0, 111, 238, 0.4) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Prevent shimmer on disabled buttons */
#action-buttons-row button[disabled]::before,
.generate-button-container button[disabled]::before {
    display: none !important;
}

/* Loading state enhancements - subtle pulse effect combined with shimmer */
button.generating,
.generating {
    animation: generate-button-pulse 2s ease-in-out infinite !important;
}

@keyframes generate-button-pulse {
    0%, 100% { 
        box-shadow: 0 4px 15px rgba(0, 111, 238, 0.3);
    }
    50% { 
        box-shadow: 0 6px 20px rgba(0, 111, 238, 0.5);
    }
}

/* Additional button states */
#action-buttons-row button:not([disabled]):not(.download-btn):focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(0, 111, 238, 0.3) !important;
    transform: translateY(-1px) !important;
}

#action-buttons-row button:not([disabled]):not(.download-btn):active {
    transform: translateY(0px) scale(0.98) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Responsive adjustments for generate button shimmer */
@media (max-width: 768px) {
    #action-buttons-row button:not([disabled]):not(.download-btn)::before {
        width: 60%;
        left: -80%;
    }
    
    @keyframes generate-shimmer-move {
        0% { 
            left: -80%; 
            opacity: 1;
        }
        100% { 
            left: 130%; 
            opacity: 0;
        }
    }
}

/* Force controls bottom row to be visible */
.controls-bottom-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    background-color: #111827 !important;
    padding: 5px 0 !important;
    min-height: 60px !important;
    margin-top: 5px !important;
    justify-content: center !important; /* Center everything */
    max-width: 652px !important; /* Match preview container width */
    margin-left: auto !important;
    margin-right: auto !important;
    gap: 2rem !important; /* Space between quality and buttons */
}

/* Ensure main container removes scrollbar */
.main-center-panel {
    overflow: hidden !important; /* Remove scrollbar */
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
}

@media (min-width: 1025px) {
    .left-sidebar .sidebar-close-btn {
        display: none !important;
    }
}

/* ================= WELCOME SCREEN INPUT FIELD ENHANCEMENTS ================= */

/* Enhanced input field container with focus-within for smooth icon transitions */
.welcome-input-group {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced input field icon container with smooth color transitions */
.welcome-input-icon {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 2.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    pointer-events: none !important;
    z-index: 10 !important;
    transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
    color: #9CA3AF !important;
}

/* Ensure the icon shows even if iconify fails */
.welcome-input-icon:empty::before {
    content: "✉";
    font-size: 1.125rem;
    color: inherit;
}

/* Icon color transitions - FIXES DISAPPEARING ICON ISSUE */
/* Target the span before iconify processes it */
.welcome-input-icon span {
    color: #9CA3AF !important;
    font-size: 1.125rem !important;
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Target after iconify converts to SVG */
.welcome-input-icon svg {
    color: #9CA3AF !important;
    width: 1.125rem !important;
    height: 1.125rem !important;
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    fill: currentColor !important;
}

/* Icon enhancement on focus - SMOOTH COLOR CHANGE */
.welcome-input-group:focus-within .welcome-input-icon {
    color: #A855F7 !important;
}

.welcome-input-group:focus-within .welcome-input-icon span,
.welcome-input-group:focus-within .welcome-input-icon svg {
    color: #A855F7 !important;
    fill: currentColor !important;
}

/* Enhanced input field styling with smooth transitions */
.welcome-input-field {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem !important;
    background-color: #1F2937; /* bg-gray-800 */
    border: 1px solid #374151; /* border-gray-700 */
    border-radius: 0.75rem;
    color: #FFFFFF;
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
    line-height: 1.5;
    position: relative;
    z-index: 1;
}

/* Input field placeholder styling with smooth transitions */
.welcome-input-field::placeholder {
    color: #6B7280; /* placeholder-gray-500 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

/* Enhanced focus state with smooth ring animation */
.welcome-input-field:focus {
    border-color: #A855F7; /* border-purple-500 */
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.15);
}

/* Placeholder fade on focus */
.welcome-input-field:focus::placeholder {
    opacity: 0.7;
    transform: translateX(4px);
}

/* Input field error state with smooth transitions */
.welcome-input-field.error {
    border-color: #EF4444; /* border-red-500 */
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
}

/* Error icon styling */
.welcome-input-error-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #EF4444; /* text-red-500 */
    z-index: 10;
    pointer-events: none;
    animation: errorPulse 0.6s ease-in-out;
}

@keyframes errorPulse {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.1); }
}

/* Password visibility toggle button */
.welcome-password-toggle {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding-right: 0.75rem;
    color: #9CA3AF; /* text-gray-400 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    z-index: 10;
}

.welcome-password-toggle:hover {
    color: #FFFFFF; /* hover:text-white */
    transform: scale(1.1);
}

/* Input group focus-within effects */
.welcome-input-group:focus-within {
    /* Remove transform to prevent upward movement */
}

/* Enhanced error message styling with smooth entrance */
.welcome-input-error-message {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #EF4444; /* text-red-500 */
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideInError 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 2rem;
    }
}

/* Smooth hover effects for input containers */
.welcome-input-group:hover:not(:focus-within) .welcome-input-field {
    border-color: #4B5563; /* Slightly lighter border on hover */
    background-color: #1F2937;
}

.welcome-input-group:hover:not(:focus-within) .welcome-input-icon .iconify {
    color: #D1D5DB; /* Slightly brighter icon on hover */
}

/* FORCE ICON VISIBILITY - HIGH SPECIFICITY OVERRIDE */
.welcome-input-group .welcome-input-icon *,
.welcome-input-group:focus-within .welcome-input-icon * {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}

/* Specifically target iconify spans with data-icon attribute */
span.iconify[data-icon] {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
    font-size: 1.125rem !important;
}

/* Specific iconify targeting - FORCE ALL CHILD ELEMENTS */
.welcome-input-icon > *,
.welcome-input-icon svg,
.welcome-input-icon .iconify,
.welcome-input-icon span.iconify,
.welcome-input-icon span[data-icon],
.welcome-input-icon [class*="iconify"],
.welcome-input-icon .iconify-inline {
    opacity: 1 !important;
    visibility: visible !important; 
    display: inline-block !important;
    color: #9CA3AF !important;
    fill: #9CA3AF !important;
    width: 1.125rem !important;
    height: 1.125rem !important;
    min-width: 1.125rem !important;
    min-height: 1.125rem !important;
    vertical-align: middle !important;
}

.welcome-input-group:focus-within .welcome-input-icon > *,
.welcome-input-group:focus-within .welcome-input-icon svg,
.welcome-input-group:focus-within .welcome-input-icon .iconify,
.welcome-input-group:focus-within .welcome-input-icon span.iconify,
.welcome-input-group:focus-within .welcome-input-icon span[data-icon],
.welcome-input-group:focus-within .welcome-input-icon [class*="iconify"],
.welcome-input-group:focus-within .welcome-input-icon .iconify-inline {
    color: #A855F7 !important;
    fill: #A855F7 !important;
}

/* Mobile optimizations for input fields */
@media (max-width: 768px) {
    .welcome-input-field {
        font-size: 1rem; /* Prevent zoom on iOS */
        padding: 0.875rem 1rem 0.875rem 2.5rem; /* Slightly larger touch targets */
    }
    
    .welcome-input-icon {
        padding-left: 0.875rem;
    }
    
    .welcome-password-toggle {
        padding-right: 0.875rem;
    }
}

/* Notification panel specific styles */
.notification-dropdown {
    transition: opacity 200ms ease-out, transform 200ms ease-out;
    transform-origin: top right;
}

.notification-dropdown.slide-in {
    animation: notificationSlideIn 200ms ease-out forwards;
}

.notification-dropdown.slide-out {
    animation: notificationSlideOut 200ms ease-out forwards;
}

/* Ensure notification panel is above other elements */
.notification-dropdown {
    z-index: 1000 !important;
}

/* Smooth transitions for notification items */
.notification-item {
    transition: background-color 200ms ease-in-out;
}



/* ================= NOTIFICATION DROPDOWN MOBILE RESPONSIVE STYLES ================= */

/* Mobile responsive notification dropdown */
@media (max-width: 640px) {
    .notification-dropdown {
        position: fixed !important;
        width: calc(100vw - 2rem) !important;
        max-width: 360px !important;
        right: 1rem !important;
        left: auto !important;
        top: 70px !important;
        border-radius: 12px !important;
        margin-top: 0 !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* Mobile font size adjustments for notification items */
    .notification-header h2 {
        font-size: 1rem !important; /* Reduced from text-lg (1.125rem) */
    }
    
    .notification-item h3 {
        font-size: 0.875rem !important; /* Reduced from default (1rem) */
        line-height: 1.25rem !important;
    }
    
    .notification-item p {
        font-size: 0.75rem !important; /* Reduced from text-sm (0.875rem) */
        line-height: 1.125rem !important;
    }
    
    .notification-item button {
        font-size: 0.625rem !important; /* Reduced from 0.7rem */
    }
    
    .notification-header span {
        font-size: 0.75rem !important; /* Reduced from text-sm (0.875rem) */
    }
    
    .notification-header button {
        font-size: 0.7rem !important; /* Reduced from 0.8rem */
    }
}

@media (max-width: 480px) {
    .notification-dropdown {
        width: calc(100vw - 1rem) !important;
        max-width: 320px !important;
        right: 0.5rem !important;
    }
}

@media (max-width: 640px) and (orientation: landscape) {
    .notification-dropdown {
        max-width: 400px !important;
        max-height: 70vh !important;
    }
}

.notification-backdrop {
    pointer-events: auto;
}

.notification-dropdown::-webkit-scrollbar {
    width: 8px;
}

.notification-dropdown::-webkit-scrollbar-track {
    background: #1F2937;
}

.notification-dropdown::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 4px;
}

.notification-dropdown::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

.notification-dropdown {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
}

/* Welcome page form spacing improvements */
.welcome-login-form {
    /* Enhanced spacing for better visual hierarchy */
    gap: 1rem; /* Reduced from 1.5rem (24px) to 1rem (16px) */
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
    .welcome-login-form {
        gap: 0.875rem; /* Reduced from 1.25rem (20px) to 0.875rem (14px) on mobile */
    }
    
    .email-field-container,
    .password-field-container {
        margin-bottom: 0.875rem !important; /* Reduced from 1.25rem (20px) to 0.875rem (14px) on mobile */
    }
    
    .remember-forgot-section {
        margin-bottom: 1rem !important; /* Reduced from 1.5rem (24px) to 1rem (16px) on mobile */
    }
    
    .signup-link-section {
        margin-top: 1rem !important; /* Reduced from 1.5rem (24px) to 1rem (16px) on mobile */
    }
    
    .welcome-header-section {
        margin-bottom: 1.5rem !important; /* Reduced from 2rem (32px) to 1.5rem (24px) on mobile */
    }
    
    .form-content-container {
        padding: 1.5rem !important; /* Keep 24px on mobile */
    }
}

@media (max-width: 480px) {
    .welcome-login-form {
        gap: 0.75rem; /* Reduced from 1rem (16px) to 0.75rem (12px) on small mobile */
    }
    
    .email-field-container,
    .password-field-container {
        margin-bottom: 0.75rem !important; /* Reduced from 1rem (16px) to 0.75rem (12px) on small mobile */
    }
    
    .remember-forgot-section {
        margin-bottom: 0.875rem !important; /* Reduced from 1.25rem (20px) to 0.875rem (14px) on small mobile */
    }
    
    .form-content-container {
        padding: 1rem !important; /* Keep 16px on small mobile */
    }
}

/* Enhanced form field spacing */
.email-field-label,
.password-field-label {
    margin-bottom: 0.5rem; /* 8px between label and input */
}

/* Input field internal spacing */
.welcome-email-field,
.welcome-password-field {
    padding: 0.875rem 2.5rem 0.875rem 2.5rem; /* 14px top/bottom, 40px left/right */
}

/* Error message spacing */
.email-error-message,
.password-error-message {
    margin-top: 0.5rem; /* 8px above error message */
}

/* Remember me section internal spacing */
.remember-me-container {
    gap: 0.5rem; /* 8px between checkbox and label */
}

/* Sign in button enhanced spacing */
.welcome-signin-btn {
    padding: 1rem 1.5rem; /* 16px top/bottom, 24px left/right */
    margin-top: 0.5rem; /* 8px above button */
}

/* Mobile logo section spacing */
.mobile-logo-section {
    margin-bottom: 2.5rem; /* 40px on mobile */
}

@media (min-width: 768px) {
    .mobile-logo-section {
        margin-bottom: 3rem; /* 48px on larger screens */
    }
}

/* Group hover opacity and border radius utilities */
.group:hover .group-hover\:opacity-100 {
    opacity: 1 !important;
}

.border-radius-50 {
    border-radius: 50px !important;
}

/* Combined utility for elements that need both */
.group-hover-rounded {
    border-radius: 50px !important;
    transition: all 0.3s ease !important;
}

.group:hover .group-hover-rounded {
    opacity: 1 !important;
    transform: scale(1.05) !important;
}

/* Enhanced group hover effects with 50px border radius */
.group-hover-element {
    border-radius: 50px !important;
    opacity: 0.7;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.group:hover .group-hover-element {
    opacity: 1 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

/* Specific variants for different use cases */
.group-hover-button {
    border-radius: 50px !important;
    opacity: 0.8;
    transition: all 0.25s ease !important;
}

.group:hover .group-hover-button {
    opacity: 1 !important;
    transform: scale(1.02) !important;
}

.group-hover-overlay {
    border-radius: 50px !important;
    opacity: 0;
    transition: opacity 0.3s ease !important;
}

.group:hover .group-hover-overlay {
    opacity: 1 !important;
}

/* Responsive adjustments for group hover effects */
@media (max-width: 768px) {
    .group-hover-element,
    .group-hover-button {
        border-radius: 25px !important; /* Smaller radius on mobile */
    }
    
    .group:hover .group-hover-element {
        transform: translateY(-1px) !important; /* Less movement on mobile */
    }
}

@media (max-width: 480px) {
    .group-hover-element,
    .group-hover-button {
        border-radius: 20px !important; /* Even smaller radius on small mobile */
    }
}

/* Pro Upgrade CTA Button - Custom styling with #006FEE color and continuous shimmer effect */
.pro-upgrade-cta-btn,
#pro-upgrade-cta-btn {
    background: #006FEE !important;
    position: relative !important;
    overflow: hidden !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Continuous shimmer effect for Pro upgrade CTA button - always running */
.pro-upgrade-cta-btn::before,
#pro-upgrade-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(120deg, 
        transparent 0%, 
        rgba(255,255,255,0.18) 30%, 
        rgba(255,255,255,0.25) 50%, 
        rgba(255,255,255,0.18) 70%, 
        transparent 100%
    );
    transform: skewX(-20deg);
    pointer-events: none;
    z-index: 1;
    border-radius: inherit;
    animation: pro-upgrade-continuous-shimmer 3s ease-in-out infinite;
}

/* Hover effects matching Sign In and dashboard upgrade buttons */
.pro-upgrade-cta-btn:hover,
#pro-upgrade-cta-btn:hover {
    background: #006feecb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px rgba(0, 111, 238, 0.3) !important;
}

.pro-upgrade-cta-btn:hover::before,
#pro-upgrade-cta-btn:hover::before {
    animation: pro-upgrade-hover-shimmer 0.6s ease-out, pro-upgrade-continuous-shimmer 3s ease-in-out infinite;
}

.pro-upgrade-cta-btn:active,
#pro-upgrade-cta-btn:active {
    transform: translateY(0px) scale(0.98) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Pro upgrade CTA text styling */
.pro-upgrade-cta-text,
#pro-upgrade-cta-text {
    font-weight: 500 !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Keyframes for continuous shimmer effect */
@keyframes pro-upgrade-continuous-shimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    20% {
        opacity: 0.8;
    }
    50% {
        left: 100%;
        opacity: 0.6;
    }
    100% {
        left: 150%;
        opacity: 0;
    }
}

/* Keyframes for hover shimmer effect */
@keyframes pro-upgrade-hover-shimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 150%;
        opacity: 0;
    }
}

/* Ensure Pro upgrade CTA button content stays above shimmer */
.pro-upgrade-cta-btn > *,
#pro-upgrade-cta-btn > * {
    position: relative;
    z-index: 2;
}

/* Focus states for Pro upgrade CTA button */
.pro-upgrade-cta-btn:focus,
#pro-upgrade-cta-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(101, 50, 255, 0.5) !important;
}

/* Disabled state for Pro upgrade CTA button */
.pro-upgrade-cta-btn:disabled,
#pro-upgrade-cta-btn:disabled {
    background: #374151 !important;
    color: #9CA3AF !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.pro-upgrade-cta-btn:disabled::before,
#pro-upgrade-cta-btn:disabled::before {
    display: none !important;
}

/* Responsive adjustments for Pro upgrade CTA */
@media (max-width: 768px) {
    .pro-upgrade-cta-btn,
    #pro-upgrade-cta-btn {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }
}

@media (max-width: 480px) {
    .pro-upgrade-cta-btn,
    #pro-upgrade-cta-btn {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.8125rem !important;
    }
}

@media (max-width: 768px) and (orientation: portrait) {
  /* Prevent bottom Safari toolbar overlap */
  html, body {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 44px) !important;
  }
  
  /* Mobile-only: Shift content to top and optimize layout */
  .form-content-container,
  .flex.flex-col.justify-center.items-center {
    justify-content: flex-start !important;
    padding-top: 1rem !important;
    padding-bottom: 2rem !important;
  }
  
  /* Mobile-only: Increase logo size by 25% (60% -> 75%) */
  .mobile-logo-section img,
  .lg\\:hidden.text-center img {
    width: 75% !important;
  }
  
  /* Mobile-only: Reduce logo section margins */
  .mobile-logo-section,
  .lg\\:hidden.text-center.mb-8 {
    margin-bottom: 1rem !important;
  }
  
  /* Mobile-only: Compact header sections */
  .welcome-header-section,
  .text-center.mb-6,
  .text-center.mb-8 {
    margin-bottom: 1.25rem !important;
  }
  
  /* Mobile-only: Optimize form spacing */
  .google-auth-section {
    margin-bottom: 1rem !important;
  }
  
  .auth-divider {
    margin-top: 0.75rem !important;
    margin-bottom: 1rem !important;
  }
  
  /* Mobile-only: Compact form fields */
  .email-field-container, 
  .password-field-container,
  .space-y-6 > div {
    margin-bottom: 1rem !important;
  }
  
  /* Mobile-only: Optimize navigation spacing */
  .welcome-footer-links,
  .mt-6.text-center.space-y-3,
  .space-y-4 {
    margin-top: 1rem !important;
  }
  
  /* Mobile-only: Compact form layout */
  form.space-y-6 {
    gap: 1rem !important;
  }
  
  /* Mobile-only: Optimize message spacing */
  .bg-red-500\/10,
  .bg-blue-900\/20 {
    margin-bottom: 1rem !important;
  }
  
  /* Mobile-only: Center preview container */
  .preview-container {
    justify-content: center;
  }
}

/* ================= GOOGLE BUTTON ENHANCED STYLING ================= */

/* Google auth section spacing */
.google-auth-section {
  margin-bottom: 1rem !important;
}

/* Base Google button styling with unique branding */
.google-signin-btn {
  /* Reset and override any conflicting Tailwind classes */
  background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #252525 100%) !important;
  border: 1px solid rgba(80, 80, 80, 0.3) !important;
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 500 !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Shimmer effect overlay for Google button */
.google-signin-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(120deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.15) 30%, 
    rgba(255, 255, 255, 0.25) 50%, 
    rgba(255, 255, 255, 0.15) 70%, 
    transparent 100%
  );
  transform: skewX(-20deg);
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Enhanced hover effects for Google button */
.google-signin-btn:hover {
  background: linear-gradient(135deg, #404040 0%, #555555 50%, #383838 100%) !important;
  border-color: rgba(100, 100, 100, 0.4) !important;
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(100, 100, 100, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Shimmer animation on hover */
.google-signin-btn:hover::before {
  opacity: 1;
  animation: google-shimmer 0.8s ease-out;
}

/* Active/click effect */
.google-signin-btn:active {
  transform: translateY(0px) scale(0.98) !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Focus states for accessibility */
.google-signin-btn:focus {
  outline: none !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.5),
    0 0 0 3px rgba(80, 80, 80, 0.6) !important;
}

/* Ensure content stays above shimmer effect */
.google-signin-btn > * {
  position: relative;
  z-index: 2;
}

/* Google icon styling within button */
.google-signin-btn svg {
  filter: brightness(1.1) !important;
  transition: filter 0.2s ease !important;
}

.google-signin-btn:hover svg {
  filter: brightness(1.2) drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)) !important;
}

/* Disabled state styling */
.google-signin-btn:disabled {
  background: #374151 !important;
  border-color: #4B5563 !important;
  color: #9CA3AF !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  opacity: 0.6 !important;
}

.google-signin-btn:disabled::before {
  display: none !important;
}

.google-signin-btn:disabled svg {
  filter: grayscale(1) opacity(0.5) !important;
}

/* Loading state with subtle pulse */
.google-signin-btn.loading {
  background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #1f1f1f 100%) !important;
  cursor: wait !important;
  animation: google-loading-pulse 2s ease-in-out infinite !important;
}

/* Shimmer keyframe animation */
@keyframes google-shimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 150%;
    opacity: 0;
  }
}

/* Loading pulse animation */
@keyframes google-loading-pulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6);
  }
}

/* Responsive adjustments for Google button */
@media (max-width: 768px) {
  .google-signin-btn {
    padding: 0.875rem 1rem !important;
    font-size: 0.9375rem !important;
  }
  
  .google-signin-btn:hover {
    transform: translateY(-1px) scale(1.01) !important;
  }
}

@media (max-width: 480px) {
  .google-signin-btn {
    padding: 0.75rem 0.875rem !important;
    font-size: 0.875rem !important;
  }
  
  .google-signin-btn svg {
    width: 1.125rem !important;
    height: 1.125rem !important;
  }
}

/* Disabled state for "Coming Soon" / Add New card */
.add-new-card.add-new-disabled {
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
}

.add-new-card.add-new-disabled:hover {
    transform: none !important; /* Disable hover transform */
    background: linear-gradient(145deg, 
        rgba(161, 161, 170, 0.06) 0%, 
        rgba(161, 161, 170, 0.04) 50%, 
        rgba(161, 161, 170, 0.02) 100%) !important; /* Keep original background */
    border-color: rgba(161, 161, 170, 0.15) !important; /* Keep original border */
    box-shadow: 
        0 8px 32px rgba(161, 161, 170, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.06) !important; /* Keep original shadow */
}

.add-new-card.add-new-disabled::before {
    display: none !important; /* Disable shimmer effect */
}

.add-new-card.add-new-disabled::after {
    display: none !important; /* Disable feather overlay */
}

.add-new-card.add-new-disabled .iconify {
    color: rgba(113, 113, 122, 0.5) !important; /* Grayed out icon */
    transform: none !important; /* Disable icon hover transform */
}

/* ================= TEMPLATE MODAL PREMIUM ANIMATIONS ================= */

/* Template Modal Container - Premium Animation with Closing Support */
.template-modal-container,
.show-more-modal-container {
    transition: opacity 320ms cubic-bezier(0.22, 0.61, 0.36, 1);
    animation-duration: 320ms;
    animation-timing-function: cubic-bezier(0.22, 0.61, 0.36, 1);
    animation-fill-mode: both;
}

.template-modal-container.opacity-100,
.show-more-modal-container.opacity-100 {
    animation-name: modalFadeIn;
}

.template-modal-container.opacity-0,
.show-more-modal-container.opacity-0 {
    animation-name: modalFadeOut;
}

/* Template Modal Backdrop - Premium Full-Viewport Blur Enhancement */
.template-modal-backdrop,
.show-more-modal-backdrop {
    /* Full viewport coverage with enhanced blur and dimming */
    background: rgba(10, 12, 20, 0.7) !important;
    backdrop-filter: blur(16px) brightness(0.7) !important;
    -webkit-backdrop-filter: blur(16px) brightness(0.7) !important;
    
    /* Proper layering below content */
    z-index: 10000 !important;
    position: absolute !important;
    inset: 0 !important;
    
    /* Smooth transitions for premium feel */
    transition: all 320ms cubic-bezier(0.22, 0.61, 0.36, 1) !important;
}

/* Template Modal Content - Premium Scale and Fade with Closing Support */
/* Content styling is handled above in "Premium Modal Enhancement" section */

.template-modal-content.scale-100,
.show-more-modal-content.scale-100 {
    transform: scale(1) translateY(0);
    opacity: 1;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25), 
        0 8px 16px -8px rgba(0, 0, 0, 0.15);
}

.template-modal-content.scale-95,
.show-more-modal-content.scale-95 {
    transform: scale(0.96) translateY(16px);
    opacity: 0;
    box-shadow: 
        0 8px 16px -8px rgba(0, 0, 0, 0.1);
}

/* Enhanced Closing Animation Support */
.template-modal-container.closing,
.show-more-modal-container.closing {
    animation-name: modalFadeOut;
    animation-duration: 320ms;
}

.template-modal-content.closing,
.show-more-modal-content.closing {
    animation-name: modalExitScale;
    animation-duration: 320ms;
}

/* Premium Modal Animation Keyframes */
@keyframes modalFadeIn {
    0% {
        opacity: 0;
        visibility: hidden;
    }
    100% {
        opacity: 1;
        visibility: visible;
    }
}

@keyframes modalFadeOut {
    0% {
        opacity: 1;
        visibility: visible;
    }
    100% {
        opacity: 0;
        visibility: hidden;
    }
}

/* Enhanced Modal Entrance Animation */
@keyframes modalEnterScale {
    0% {
        transform: scale(0.96) translateY(16px);
        opacity: 0;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Enhanced Modal Exit Animation */
@keyframes modalExitScale {
    0% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    100% {
        transform: scale(0.96) translateY(16px);
        opacity: 0;
    }
}

/* Accessibility - Respect Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .template-modal-container,
    .show-more-modal-container,
    .template-modal-backdrop,
    .show-more-modal-backdrop,
    .template-modal-content,
    .show-more-modal-content {
        transition: none !important;
        animation: none !important;
    }
    
    .template-modal-content.scale-95,
    .show-more-modal-content.scale-95 {
        transform: none !important;
        opacity: 0 !important;
    }
    
    .template-modal-content.scale-100,
    .show-more-modal-content.scale-100 {
        transform: none !important;
        opacity: 1 !important;
    }
}

/* Hide any remaining tooltips or info icons in templates section */
#premade-templates-info,
[data-tooltip-id="premade-templates-info"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Also hide any tooltip containers that might be floating */
[id*="premade-templates-info"],
[class*="premade-templates-info"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* ================= TEMPLATE ITEMS GRID RESPONSIVE ================= */
/* Set 2 columns for template items grid between 500px and 639px */
@media (min-width: 500px) and (max-width: 639px) {
    .template-items-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    /* Hide template item descriptions to save space */
    .template-item-description {
        display: none !important;
    }
}

/* ================= MODAL ALIGNMENT FIXES ================= */
/* Fix modal and banner alignment for delete avatar, banner, and change avatar image modals */

/* Template Modal Alignment Fix with Enhanced Z-Index */
.template-modal-container,
.show-more-modal-container {
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important; /* px-4 py-4 equivalent */
    z-index: 10002 !important; /* Ensure modal container is above everything */
}

/* Confirmation Modal Alignment Fix */
.confirmation-modal-overlay {
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

/* Avatar Upload Modal Alignment Fix */
.fixed.inset-0.z-50.flex.items-center.justify-center.p-4 {
    align-items: center !important;
    justify-content: center !important;
}

/* Mobile Portrait Responsive Fixes */
@media (max-width: 768px) and (orientation: portrait) {
    .template-modal-container,
    .show-more-modal-container,
    .confirmation-modal-overlay {
        align-items: center !important;
        justify-content: center !important;
        padding: 0.75rem !important; /* Slightly less padding on mobile */
    }
    
    /* Ensure modal content doesn't get too close to screen edges */
    .template-modal-content,
    .show-more-modal-content,
    .confirmation-modal-container {
        margin: 0.75rem !important;
        max-height: calc(100vh - 1.5rem) !important;
    }
    
    /* Avatar upload modal mobile fix */
    .fixed.inset-0.z-50.flex.items-center.justify-center.p-4 {
        padding: 0.75rem !important;
        align-items: center !important;
        justify-content: center !important;
    }
}

/* Mobile Landscape Responsive Fixes */
@media (max-width: 768px) and (orientation: landscape) {
    .template-modal-container,
    .show-more-modal-container,
    .confirmation-modal-overlay {
        align-items: center !important;
        justify-content: center !important;
        padding: 0.5rem !important; /* Even less padding on landscape mobile */
    }
    
    .template-modal-content,
    .show-more-modal-content,
    .confirmation-modal-container {
        margin: 0.5rem !important;
        max-height: calc(100vh - 1rem) !important;
    }
}

/* Tablet Responsive Fixes */
@media (min-width: 769px) and (max-width: 1024px) {
    .template-modal-container,
    .show-more-modal-container,
    .confirmation-modal-overlay {
        align-items: center !important;
        justify-content: center !important;
        padding: 1rem !important;
    }
}

/* Ensure backdrop doesn't interfere with centering */
.template-modal-backdrop,
.show-more-modal-backdrop {
    position: absolute !important;
    inset: 0 !important;
    z-index: -1 !important;
}

/* Fix any conflicting positioning */
.template-modal-content,
.show-more-modal-content {
    position: relative !important;
    z-index: 1 !important;
}


/* Expanded category thumbnail responsive styling */
.expanded-category-thumbnail {
    aspect-ratio: 16/9;
    overflow: hidden;
}

.expanded-category-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    aspect-ratio: 16/9;
}

/* Responsive adjustments for expanded category cards */
@media (max-width: 768px) {
    .expanded-category-thumbnail {
        aspect-ratio: 16/9;
        min-height: 200px;
    }
    
    .expanded-category-thumbnail img {
        object-fit: cover;
        object-position: center;
    }
}

@media (max-width: 480px) {
    .expanded-category-thumbnail {
        aspect-ratio: 16/9;
        min-height: 180px;
    }
}/* Ge
neral responsive image utilities for cards */
.card-image-responsive {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    aspect-ratio: 16/9;
    border-radius: inherit;
}

.card-image-responsive.portrait {
    aspect-ratio: 2/3;
}

.card-image-responsive.square {
    aspect-ratio: 1/1;
}

/* Ensure all card images are responsive by default */
.history-item img,
.template-card img,
.category-card img {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
}

/* Fix for any remaining fixed-size images in cards */
@media (max-width: 768px) {
    .card-image-responsive {
        aspect-ratio: 16/9;
        object-fit: cover;
    }
    
    .card-image-responsive.portrait {
        aspect-ratio: 2/3;
    }
}

@media (max-width: 480px) {
    .card-image-responsive {
        aspect-ratio: 16/9;
        object-fit: cover;
        object-position: center;
    }
}