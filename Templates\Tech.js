export default [
  {
    id: "tech-review",
    name: "[PRODUCT] Tech Review!",
    description: "For gadget and technology review content.",
    promptBase: "Create a modern YouTube thumbnail for '[PRODUCT] Tech Review!'. Show the product with digital effects and a person holding it. Text overlay: '[PRODUCT] REVIEW'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Curious', textOverlay: true, overlayText: "[PRODUCT]\nREVIEW" },
    templateImagePlaceholder: { text: "Tech Review", bgColor: "bg-blue-500" }
  },
  {
    id: "tech-vs",
    name: "[PRODUCT 1] vs [PRODUCT 2]",
    description: "Comparison between two tech products.",
    promptBase: "Design a split-screen thumbnail for '[PRODUCT 1] vs [PRODUCT 2]'. Use device icons, bold text overlay, and a digital background.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "[PRODUCT 1] VS [PRODUCT 2]" },
    templateImagePlaceholder: { text: "Tech Showdown", bgColor: "bg-blue-400" }
  },
  {
    id: "tech-unboxing",
    name: "[PRODUCT] Unboxing!",
    description: "For unboxing and first impressions.",
    promptBase: "Create an exciting thumbnail for '[PRODUCT] Unboxing!'. Show a person opening a box with sparkles and device icons. Text overlay: 'UNBOXING!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "UNBOXING!" },
    templateImagePlaceholder: { text: "Unboxing", bgColor: "bg-blue-300" }
  },
  {
    id: "tech-tips",
    name: "[TOPIC] Tech Tips",
    description: "For tech tips and how-to content.",
    promptBase: "Design a helpful thumbnail for '[TOPIC] Tech Tips'. Use a person pointing at a device, with lightbulb and gear icons. Text overlay: 'TECH TIPS'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Pointing', includeIcons: true, textOverlay: true, overlayText: "TECH\nTIPS" },
    templateImagePlaceholder: { text: "Tech Tips", bgColor: "bg-blue-200" }
  },
  {
    id: "tech-myths",
    name: "[TOPIC] Myths Busted!",
    description: "Debunking tech myths.",
    promptBase: "Create a myth-busting thumbnail for '[TOPIC] Myths Busted!'. Use a person with a surprised expression, warning and checkmark icons, and bold text overlay: 'MYTHS BUSTED!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "MYTHS\nBUSTED!" },
    templateImagePlaceholder: { text: "Myths Busted", bgColor: "bg-blue-100" }
  },
  {
    id: "tech-future",
    name: "The Future of [TECHNOLOGY]",
    description: "For futuristic tech discussions.",
    promptBase: "Design a futuristic thumbnail for 'The Future of [TECHNOLOGY]'. Use glowing circuit icons, a person looking forward, and a digital cityscape. Text overlay: 'THE FUTURE'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Serious', includeIcons: true, textOverlay: true, overlayText: "THE\nFUTURE" },
    templateImagePlaceholder: { text: "Future Tech", bgColor: "bg-blue-50" }
  }
]; 