---
description: 
globs: 
alwaysApply: false
---
---
title: comprehensive-thumbnail-prompt-enhancement
id: comprehensive-thumbnail-prompt-enhancement.mdc
ruleType: manual
---

## 🧠 Feature: Comprehensive Thumbnail Prompt Enhancement

### Objective
Ensure that prompt enhancement for AI-generated thumbnails goes beyond just improving titles or phrases. The enhancement must also address creative scene setup, rendering order, cinematic effects, glow, and color grading (LUT), resulting in visually balanced, cinematic, and high-impact thumbnails.

---

## Applies To
- /src/utils/promptBuilder.ts
- /src/utils/promptFormatter.js
- /src/hooks/usePromptEnhancer.ts

---

## 🎯 Problem
Current prompt enhancement logic often focuses only on rewording titles or overlay text, neglecting the creative and visual aspects of the thumbnail. This can lead to images that are well-titled but visually flat or unbalanced.

---

## ✅ Solution: Holistic Prompt Enhancement Rules

- **Rendering Order:**  
  - Explicitly define the order in which elements are rendered (e.g., background first, then subject, then overlays/icons, then text).
  - Ensure the main subject is visually prominent and not obscured by overlays or effects.

- **Cinematic Effects:**  
  - Add instructions for depth of field, motion blur, dramatic lighting, and other cinematic effects to enhance visual impact.
  - Use these effects to guide viewer focus and create a sense of depth.

- **Glow Effects:**  
  - Apply glow to key elements (e.g., text, icons, subject edges) to increase click appeal and separation from the background.
  - Specify glow color and intensity based on the scene’s palette.

- **Scene Color Grading (LUT):**  
  - Set up the scene’s color grading or LUT to match the intended mood (e.g., warm/cinematic, cool/dramatic, neon/gaming).
  - Use descriptive phrases for LUTs, not just preset names.

- **Prompt Construction:**  
  - When building the prompt, inject all of the above creative instructions, not just improved text.
  - Example:  
    - “Render the background first, then the main subject in the foreground. Add a soft depth of field and dramatic side lighting. Apply a subtle blue glow to the text overlay. Use a cinematic orange-teal color grade across the entire scene.”

---

## 🛠️ Implementation Notes

- Update prompt enhancer logic to include rendering order, cinematic effects, glow, and LUT/color grading in every enhanced prompt.
- Use descriptive, actionable language for each visual effect.
- Ensure all enhancements work together for a cohesive, professional result.

---

## ✅ Done When

- Enhanced prompts consistently produce thumbnails with improved composition, cinematic effects, glow, and color grading—not just better titles.
- Visual balance and click appeal are noticeably improved in all generated thumbnails.

---

To activate this in Cursor: @comprehensive-thumbnail-prompt-enhancement