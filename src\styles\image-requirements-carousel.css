/* ================= IMAGE REQUIREMENTS CAROUSEL - MACOS LIQUID GLASS ================= */

/* Main carousel modal animations with responsive height */
.image-requirements-carousel-modal {
    animation: carouselModalIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    transform-origin: center center;
}

/* Responsive height adjustments for smaller screens */
@media (max-height: 600px) {
    .image-requirements-carousel-modal {
        min-height: 90vh !important;
        max-height: 95vh !important;
    }
}

@media (max-height: 500px) {
    .image-requirements-carousel-modal {
        min-height: 95vh !important;
        max-height: 98vh !important;
    }
}

/* Compact layout - no scrollbar needed with enhanced top padding */
.carousel-content-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 2.5rem !important; /* Add extra top padding for breathing room from window edge */
}

/* Custom image sizing - 337x505 resolution */
.comparison-card > div > div {
    width: 337px !important;
    height: 505px !important;
    aspect-ratio: 2/3; /* Maintain proper aspect ratio */
    overflow: hidden;
}

/* Contextual Status Icon Overlay - macOS Liquid Glass Integration */
.comparison-card .status-icon-overlay {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.comparison-card .status-icon-overlay:hover {
    transform: scale(1.05);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Success (Green) variant */
.comparison-card .status-icon-overlay.success {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(34, 197, 94, 0.6);
    box-shadow: 
        0 8px 32px rgba(34, 197, 94, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Error (Red) variant */
.comparison-card .status-icon-overlay.error {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 
        0 8px 32px rgba(239, 68, 68, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.comparison-card img {
    width: 337px;
    height: 505px;
    object-fit: cover;
    object-position: center;
    aspect-ratio: 2/3;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .comparison-card > div > div {
        width: 250px !important;
        height: 375px !important;
        aspect-ratio: 2/3;
    }
    
    .comparison-card img {
        width: 250px;
        height: 375px;
        object-fit: cover;
        object-position: center;
        aspect-ratio: 2/3;
    }
}

@media (max-width: 480px) {
    .comparison-card > div > div {
        width: 200px !important;
        height: 300px !important;
        aspect-ratio: 2/3;
    }
    
    .comparison-card img {
        width: 200px;
        height: 300px;
        object-fit: cover;
        object-position: center;
        aspect-ratio: 2/3;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .carousel-content-area {
        padding: 1.5rem !important;
        padding-top: 2rem !important; /* Maintain good top spacing on mobile */
    }
    
    .image-requirements-carousel-modal {
        margin: 0.5rem;
        min-height: 85vh;
        max-height: 90vh;
    }
}

@media (max-width: 480px) {
    .carousel-content-area {
        padding: 1rem !important;
        padding-top: 1.75rem !important; /* Adequate top spacing for small screens */
    }
    
    .image-requirements-carousel-modal {
        margin: 0.25rem;
    }
}

@keyframes carouselModalIn {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0);
    }
}

/* Carousel glass effect enhancements */
.image-requirements-carousel-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3) 50%, transparent);
    border-radius: 24px 24px 0 0;
}

/* Comparison card hover effects */
.comparison-card {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.comparison-card:hover {
    transform: translateY(-4px);
}

.comparison-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
    border-radius: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.comparison-card:hover::before {
    opacity: 1;
}

/* Image container enhancements */
.comparison-card img {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.comparison-card:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.05);
}

/* Slide transition effects */
.carousel-content-area > * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Navigation button enhancements */
.carousel-content-area button {
    position: relative;
    overflow: hidden;
}

.carousel-content-area button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.carousel-content-area button:hover::before {
    left: 100%;
}

/* Bullet navigation enhanced styling */
.carousel-content-area > div:last-child .flex.items-center.gap-3 button {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-content-area > div:last-child .flex.items-center.gap-3 button::after {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(59,130,246,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.carousel-content-area > div:last-child .flex.items-center.gap-3 button:hover::after,
.carousel-content-area > div:last-child .flex.items-center.gap-3 button[aria-current="true"]::after {
    opacity: 1;
}

/* Glass reflection effect on modal header */
.image-requirements-carousel-modal > div:first-child {
    position: relative;
}

.image-requirements-carousel-modal > div:first-child::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, rgba(255,255,255,0.08) 0%, transparent 100%);
    border-radius: 24px 24px 0 0;
    pointer-events: none;
}

/* Slide indicator pulse animation */
.carousel-content-area + div + div {
    animation: slideIndicatorPulse 2s ease-in-out infinite;
}

@keyframes slideIndicatorPulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* Enhanced focus states for accessibility */
.image-requirements-carousel-modal button:focus-visible {
    outline: 2px solid rgba(59, 130, 246, 0.6);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Smooth image loading states */
.comparison-card img {
    background: linear-gradient(45deg, 
        rgba(255,255,255,0.1) 25%, 
        transparent 25%, 
        transparent 75%, 
        rgba(255,255,255,0.1) 75%
    );
    background-size: 20px 20px;
    animation: imageLoadingShimmer 1.5s infinite linear;
}

.comparison-card img[src] {
    animation: none;
    background: none;
}

@keyframes imageLoadingShimmer {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .image-requirements-carousel-modal {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
    
    .carousel-content-area {
        padding: 1.5rem !important;
    }
    
    .comparison-card {
        margin-bottom: 1rem;
        max-width: 16rem;
        aspect-ratio: 4 / 5;
        transform:scale(0.95);
    }
    
    .carousel-content-area > div:last-child {
        flex-direction: column;
        gap: 1rem;
    }
    
    .carousel-content-area > div:last-child > button {
        order: 2;
        width: 100%;
        justify-content: center;
    }
    
    .carousel-content-area > div:last-child > .flex.items-center.gap-3 {
        order: 1;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .image-requirements-carousel-modal {
        box-shadow: 
            0 32px 64px rgba(0,0,0,0.6), 
            0 16px 32px rgba(0,0,0,0.4), 
            inset 0 1px 0 rgba(255,255,255,0.15);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .image-requirements-carousel-modal,
    .comparison-card,
    .comparison-card img,
    .carousel-content-area > * {
        animation: none !important;
        transition: none !important;
    }
    
    .comparison-card:hover {
        transform: none;
    }
    
    .comparison-card:hover img {
        transform: none;
        filter: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .image-requirements-carousel-modal {
        border: 2px solid rgba(255,255,255,0.8);
        background: rgba(0,0,0,0.95);
    }
    
    .comparison-card > div {
        border-width: 2px;
    }
}

/* Print styles (hide modal) */
@media print {
    .image-requirements-carousel-modal {
        display: none !important;
    }
} 