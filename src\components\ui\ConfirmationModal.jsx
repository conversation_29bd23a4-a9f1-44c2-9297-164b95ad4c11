import React, { useState, useEffect } from 'react'

export const ConfirmationModal = ({ 
    isOpen, 
    onClose, 
    onConfirm, 
    title = "Confirmation", 
    message = "Are you sure you want to continue?",
    confirmText = "OK",
    cancelText = "Cancel",
    confirmButtonId = "confirm-modal-ok-btn",
    cancelButtonId = "confirm-modal-cancel-btn"
}) => {
    const [isClosing, setIsClosing] = useState(false);

    useEffect(() => {
        if (!isOpen) {
            setIsClosing(false);
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const handleClose = () => {
        setIsClosing(true);
        // Delay the actual close to allow animation to complete
        setTimeout(() => {
            onClose();
        }, 200); // Match the animation duration
    };

    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            handleClose();
        }
    };

    const handleConfirm = () => {
        onConfirm();
        handleClose();
    };

    return React.createElement('div', {
        className: `confirmation-modal-overlay fixed inset-0 z-[10002] flex items-center justify-center${isClosing ? ' closing' : ''}`,
        onClick: handleBackdropClick,
        id: 'confirmation-modal-overlay'
    },
        // Backdrop
        React.createElement('div', {
            className: 'absolute inset-0 bg-black/50 backdrop-blur-sm'
        }),
        
        // Modal Container
        React.createElement('div', {
            className: 'confirmation-modal-container relative w-full max-w-md mx-4',
            id: 'confirmation-modal-container'
        },
            // Alert Frame
            React.createElement('div', {
                className: 'alert-composition bg-gray-800/65 border border-gray-200/20 rounded-2xl p-3 backdrop-blur-sm',
                id: 'alert-composition'
            },
                // Main Content Container
                React.createElement('div', {
                    className: 'container-main flex items-start gap-4',
                    id: 'container-main'
                },
                    // Info Icon
                    React.createElement('div', {
                        className: 'icon-container flex-shrink-0 mt-1',
                        id: 'info-icon-container'
                    },
                        React.createElement('span', {
                            className: 'iconify text-gray-400',
                            'data-icon': 'solar:info-circle-bold',
                            style: { fontSize: '24px' }
                        })
                    ),
                    
                    // Content Container
                    React.createElement('div', {
                        className: 'content-container flex-1 min-w-0',
                        id: 'content-container'
                    },
                        // Upper Section
                        React.createElement('div', {
                            className: 'upper-section mb-6',
                            id: 'upper-section'
                        },
                            // Title
                            React.createElement('h3', {
                                className: 'modal-title text-gray-900 dark:text-gray-100 font-medium text-base mb-2 leading-5',
                                id: 'modal-title'
                            }, title),
                            
                            // Message
                            React.createElement('p', {
                                className: 'modal-message text-gray-600 dark:text-gray-400 text-sm leading-5',
                                id: 'modal-message'
                            }, message)
                        ),
                        
                        // Button Container
                        React.createElement('div', {
                            className: 'button-tabs flex gap-2 justify-end',
                            id: 'button-tabs'
                        },
                            // Cancel Button
                            React.createElement('button', {
                                className: 'cancel-btn px-5 py-2 rounded-lg text-gray-400 hover:text-gray-300 transition-colors text-sm font-medium',
                                onClick: handleClose,
                                id: cancelButtonId
                            }, cancelText),
                            
                            // Confirm Button  
                            React.createElement('button', {
                                className: 'confirm-btn px-5 py-2 rounded-lg border border-red-500 bg-red-500/10 text-red-400 hover:bg-red-500/20 hover:border-red-400 transition-colors text-sm font-medium',
                                onClick: handleConfirm,
                                id: confirmButtonId
                            }, confirmText)
                        )
                    ),
                    
                    // Close Icon
                    React.createElement('div', {
                        className: 'close-icon-container flex-shrink-0',
                        id: 'close-icon-container'
                    },
                        React.createElement('button', {
                            className: 'close-btn text-gray-400 hover:text-gray-300 transition-colors p-1',
                            onClick: handleClose,
                            'aria-label': 'Close modal',
                            id: 'modal-close-btn'
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:close-circle-bold',
                                style: { fontSize: '24px' }
                            })
                        )
                    )
                )
            )
        )
    );
}; 