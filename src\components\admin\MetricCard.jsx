const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;

export const MetricCard = ({ id, title, value, change, changeType, icon }) => {
    const getIconPath = (iconName) => {
        const icons = {
            'currency-dollar': 'M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z',
            'shopping-cart': 'M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z',
            'users': 'M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z',
            'cpu-chip': 'M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z',
            'chart-bar': 'M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z'
        };
        return icons[iconName] || icons['chart-bar'];
    };

    const changeColorClass = changeType === 'positive' ? 'text-green-400' : 'text-red-400';
    const changeIcon = changeType === 'positive' ? 'M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941' : 'M2.25 6 9 12.75l4.306-4.306a11.95 11.95 0 0 1 5.814 5.518l2.74 1.22m0 0-5.94 2.281m5.94-2.28-2.28-5.941';

    return React.createElement('div', {
        className: 'metric-card bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-purple-500 transition-colors',
        id: `metric-card-${id}`
    },
        // Header with icon and title
        React.createElement('div', {
            className: 'metric-card-header flex items-center justify-between mb-4',
            id: `metric-card-header-${id}`
        },
            React.createElement('div', {
                className: 'metric-card-icon-wrapper flex items-center justify-center w-10 h-10 bg-purple-600 rounded-lg'
            },
                React.createElement('svg', {
                    className: 'metric-card-icon w-5 h-5 text-white',
                    fill: 'none',
                    stroke: 'currentColor',
                    viewBox: '0 0 24 24'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: getIconPath(icon)
                    })
                )
            ),
            React.createElement('h3', {
                className: 'metric-card-title text-sm font-medium text-gray-400'
            }, title)
        ),

        // Value
        React.createElement('div', {
            className: 'metric-card-value-section mb-2',
            id: `metric-card-value-${id}`
        },
            React.createElement('p', {
                className: 'metric-card-value text-2xl font-bold text-white'
            }, value)
        ),

        // Change indicator
        React.createElement('div', {
            className: 'metric-card-change-section flex items-center',
            id: `metric-card-change-${id}`
        },
            React.createElement('svg', {
                className: `metric-card-change-icon w-4 h-4 mr-1 ${changeColorClass}`,
                fill: 'none',
                stroke: 'currentColor',
                viewBox: '0 0 24 24'
            },
                React.createElement('path', {
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round',
                    strokeWidth: 2,
                    d: changeIcon
                })
            ),
            React.createElement('span', {
                className: `metric-card-change-text text-sm font-medium ${changeColorClass}`
            }, change),
            React.createElement('span', {
                className: 'metric-card-change-period text-sm text-gray-500 ml-1'
            }, 'vs last month')
        )
    );
}; 