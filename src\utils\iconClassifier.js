// Icon Classification Utility for Intelligent Rendering
// Automatically determines whether icons should be rendered realistically or in 3D cartoonish style

// ================= Real-World Objects (70-80% Realistic Rendering) =================

const healthFitnessRealistic = [
  'stethoscope', 'dumbbells', 'heart rate monitor', 'blood pressure cuff',
  'medical syringe', 'pills', 'thermometer', 'bandage', 'scales',
  'yoga mat', 'resistance bands', 'protein powder', 'water bottle',
  'running shoes', 'fitness tracker', 'massage gun', 'weights',
  'exercise bike', 'treadmill', 'medicine ball', 'foam roller'
];

const foodNutritionRealistic = [
  'apple', 'banana', 'broccoli', 'carrot', 'tomato', 'avocado',
  'chef knife', 'cutting board', 'mixing bowl', 'measuring cup',
  'frying pan', 'oven', 'blender', 'coffee mug', 'dinner plate',
  'fork', 'spoon', 'wine glass', 'grocery bag', 'lunch box',
  'recipe book', 'ingredients', 'vegetables', 'fruits', 'meat'
];

const technologyRealistic = [
  'smartphone', 'laptop', 'desktop computer', 'tablet', 'smartwatch',
  'wireless earbuds', 'camera', 'microphone', 'keyboard', 'mouse',
  'router', 'usb cable', 'hard drive', 'graphics card', 'monitor',
  'vr headset', 'drone', 'smart speaker', 'gaming controller',
  'headphones', 'charger', 'battery', 'processor', 'circuit board'
];

const financeBusinessRealistic = [
  'coins', 'banknotes', 'credit card', 'wallet', 'briefcase',
  'calculator', 'pen', 'documents', 'folder', 'filing cabinet',
  'office chair', 'desk', 'whiteboard', 'marker', 'stapler',
  'business card', 'handshake', 'signature', 'contract', 'seal',
  'money', 'cash', 'dollar bill', 'piggy bank', 'safe'
];

const educationRealistic = [
  'books', 'notebook', 'pencil', 'eraser', 'ruler', 'backpack',
  'chalkboard', 'graduation cap', 'diploma', 'globe', 'microscope',
  'calculator', 'highlighter', 'sticky notes', 'binder', 'desk lamp',
  'chair', 'classroom desk', 'library shelf', 'textbook',
  'blackboard', 'whiteboard', 'school supplies', 'scissors'
];

const transportationRealistic = [
  'car', 'bicycle', 'airplane', 'train', 'bus', 'motorcycle',
  'suitcase', 'passport', 'ticket', 'map', 'compass', 'camera',
  'hotel key', 'luggage tag', 'travel pillow', 'sunglasses',
  'vehicle', 'transportation', 'wheels', 'engine', 'fuel'
];

// ================= Abstract Concepts (3D Cartoonish Rendering) =================

const emotionsCartoonish = [
  'joy', 'happiness', 'sadness', 'anger', 'surprise', 'fear',
  'love', 'excitement', 'disappointment', 'pride', 'confidence',
  'stress', 'relaxation', 'motivation', 'inspiration', 'determination',
  'energy', 'enthusiasm', 'frustration', 'satisfaction', 'hope'
];

const digitalConceptsCartoonish = [
  'cloud computing', 'data storage', 'wifi signal', 'download',
  'upload', 'sync', 'backup', 'security shield', 'firewall',
  'algorithm', 'artificial intelligence', 'machine learning',
  'blockchain', 'cryptocurrency', 'digital transformation',
  'network', 'connection', 'signal', 'bandwidth', 'streaming'
];

const abstractConceptsCartoonish = [
  'time', 'past', 'present', 'future', 'growth', 'success',
  'innovation', 'creativity', 'strategy', 'goal', 'target',
  'progress', 'achievement', 'vision', 'mission', 'values',
  'teamwork', 'leadership', 'communication', 'collaboration',
  'idea', 'concept', 'plan', 'solution', 'opportunity'
];

const weatherCartoonish = [
  'sunshine', 'rain', 'snow', 'lightning', 'wind', 'storm',
  'rainbow', 'cloud', 'hurricane', 'tornado', 'earthquake',
  'volcano', 'meteor', 'aurora', 'eclipse', 'thunder',
  'heat', 'cold', 'weather', 'climate', 'temperature'
];

// ================= Enhanced Keyword Detection =================

const enhancedKeywordDetection = {
  realistic: [
    'product', 'tool', 'equipment', 'device', 'machine', 'vehicle',
    'food', 'ingredient', 'material', 'object', 'item', 'gear',
    'instrument', 'apparatus', 'gadget', 'hardware', 'physical'
  ],
  cartoonish: [
    'feeling', 'emotion', 'concept', 'idea', 'energy', 'vibe',
    'mood', 'spirit', 'essence', 'aura', 'flow', 'power',
    'abstract', 'virtual', 'digital', 'metaphor', 'symbol'
  ]
};

// ================= Classification Functions =================

/**
 * Classifies an icon keyword to determine appropriate rendering style
 * @param {string} iconKeyword - The icon keyword to classify
 * @param {string} category - The template category context
 * @returns {object} Classification result with style, level, and description
 */
export const classifyIconRenderingStyle = (iconKeyword, category = 'general') => {
  const keyword = iconKeyword.toLowerCase().trim();
  
  // Category-specific realistic objects
  const realisticObjects = [
    ...healthFitnessRealistic,
    ...foodNutritionRealistic,
    ...technologyRealistic,
    ...financeBusinessRealistic,
    ...educationRealistic,
    ...transportationRealistic,
    ...enhancedKeywordDetection.realistic
  ];
  
  // Abstract/cartoonish concepts
  const cartoonishConcepts = [
    ...emotionsCartoonish,
    ...digitalConceptsCartoonish,
    ...abstractConceptsCartoonish,
    ...weatherCartoonish,
    ...enhancedKeywordDetection.cartoonish
  ];
  
  // Check for realistic rendering (exact match or partial match)
  const isRealistic = realisticObjects.some(obj => 
    keyword.includes(obj) || obj.includes(keyword) || 
    keyword === obj || obj === keyword
  );
  
  if (isRealistic) {
    return {
      style: 'realistic',
      renderingLevel: '70-80% photorealistic',
      description: 'High-detail realistic rendering with natural lighting and textures',
      confidence: 'high'
    };
  }
  
  // Check for cartoonish rendering
  const isCartoonish = cartoonishConcepts.some(concept => 
    keyword.includes(concept) || concept.includes(keyword) ||
    keyword === concept || concept === keyword
  );
  
  if (isCartoonish) {
    return {
      style: 'cartoonish',
      renderingLevel: '3D stylized',
      description: 'Clean 3D cartoonish style with vibrant colors and smooth surfaces',
      confidence: 'high'
    };
  }
  
  // Default fallback based on category
  return getDefaultStyleByCategory(category);
};

/**
 * Returns default rendering style based on template category
 * @param {string} category - The template category
 * @returns {object} Default style configuration
 */
const getDefaultStyleByCategory = (category) => {
  const realisticCategories = [
    'health-fitness', 'food', 'tech', 'finance', 'business', 
    'education', 'travel', 'transportation'
  ];
  
  const cartoonishCategories = [
    'emotions', 'abstract', 'digital', 'weather', 'gaming',
    'reactions', 'vlogging'
  ];
  
  if (realisticCategories.includes(category)) {
    return {
      style: 'realistic',
      renderingLevel: '70-80% photorealistic',
      description: 'High-detail realistic rendering with natural lighting and textures',
      confidence: 'medium'
    };
  }
  
  if (cartoonishCategories.includes(category)) {
    return {
      style: 'cartoonish',
      renderingLevel: '3D stylized',
      description: 'Clean 3D cartoonish style with vibrant colors and smooth surfaces',
      confidence: 'medium'
    };
  }
  
  // Ultimate fallback - slightly favor cartoonish for general use
  return {
    style: 'cartoonish',
    renderingLevel: '3D stylized',
    description: 'Clean 3D cartoonish style with vibrant colors and smooth surfaces',
    confidence: 'low'
  };
};

/**
 * Extracts potential icon keywords from a prompt text
 * @param {string} prompt - The text prompt to analyze
 * @returns {array} Array of potential icon keywords
 */
export const extractIconKeywords = (prompt) => {
  if (!prompt || typeof prompt !== 'string') return [];
  
  // Icon indicator words that suggest the following word might be an icon
  const iconIndicators = [
    'icon', 'symbol', 'graphic', 'illustration', 'element',
    'with', 'showing', 'featuring', 'including', 'displaying',
    'contains', 'has', 'using', 'depicting', 'representing'
  ];
  
  // Split prompt and look for potential icon references
  const words = prompt.toLowerCase().split(/[\s,.:;!?()[\]{}]+/).filter(word => word.length > 2);
  const iconKeywords = [];
  
  // Look for words that follow icon indicators
  words.forEach((word, index) => {
    const prevWord = words[index - 1];
    if (prevWord && iconIndicators.includes(prevWord)) {
      iconKeywords.push(word);
    }
  });
  
  // Also check for known icon words directly in the prompt
  const allIconWords = [
    ...healthFitnessRealistic,
    ...foodNutritionRealistic,
    ...technologyRealistic,
    ...financeBusinessRealistic,
    ...educationRealistic,
    ...transportationRealistic,
    ...emotionsCartoonish,
    ...digitalConceptsCartoonish,
    ...abstractConceptsCartoonish,
    ...weatherCartoonish
  ];
  
  words.forEach(word => {
    if (allIconWords.some(iconWord => word.includes(iconWord) || iconWord.includes(word))) {
      if (!iconKeywords.includes(word)) {
        iconKeywords.push(word);
      }
    }
  });
  
  return iconKeywords;
};

/**
 * Analyzes a prompt and returns classification summary for all detected icons
 * @param {string} prompt - The prompt to analyze
 * @param {string} category - The template category
 * @returns {object} Summary of icon classifications
 */
export const analyzePromptIcons = (prompt, category = 'general') => {
  const keywords = extractIconKeywords(prompt);
  const classifications = keywords.map(keyword => ({
    keyword,
    ...classifyIconRenderingStyle(keyword, category)
  }));
  
  const realisticIcons = classifications.filter(c => c.style === 'realistic');
  const cartoonishIcons = classifications.filter(c => c.style === 'cartoonish');
  
  return {
    totalIcons: keywords.length,
    realisticIcons: realisticIcons.map(c => c.keyword),
    cartoonishIcons: cartoonishIcons.map(c => c.keyword),
    realisticCount: realisticIcons.length,
    cartoonishCount: cartoonishIcons.length,
    classifications
  };
};

// Export constants for external use
export {
  healthFitnessRealistic,
  foodNutritionRealistic,
  technologyRealistic,
  financeBusinessRealistic,
  educationRealistic,
  transportationRealistic,
  emotionsCartoonish,
  digitalConceptsCartoonish,
  abstractConceptsCartoonish,
  weatherCartoonish,
  enhancedKeywordDetection
}; 