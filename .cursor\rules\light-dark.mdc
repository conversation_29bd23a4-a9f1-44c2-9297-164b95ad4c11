---
description: 
globs: 
alwaysApply: false
---

---

## 🛠️ Implementation Steps

1. **Create light theme CSS variants for all components.**
2. **Add ThemeContext for global state and persistence.**
3. **Build and style the ThemeToggleButton component.**
4. **Add the toggle button to UserDashboard.**
5. **Ensure all components respond to theme changes.**
6. **Test responsiveness, accessibility, and contrast.**

---

## 📝 Notes

- Do **not** add the toggle to authentication pages.
- Use CSS variables for easy theme switching if possible.
- If the dashboard has a sidebar, consider a secondary toggle location for redundancy.
- Use Heroicons/Solar icons for sun/moon.
- Add a tooltip: “Switch between Light and Dark mode”.

---

## 🔗 Related Files

- `/src/components/UserDashboard.jsx`
- `/src/components/ui/ThemeToggleButton.jsx`
- `/src/contexts/ThemeContext.jsx`
- `/src/styles/layout.css`
- `/src/styles/controls.css`
- `/src/App.jsx`
- `/src/index.html`

---

**This rule ensures a premium, accessible, and user-friendly global theme toggle, with best-practice placement and implementation for modern web apps.**