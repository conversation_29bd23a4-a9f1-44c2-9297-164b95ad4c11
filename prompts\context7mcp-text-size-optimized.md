# Context7MCP Text Size Optimized Overlay

Design a cinematic YouTube thumbnail at 1280x720 resolution with **context-aware, visually optimal text overlay sizing and placement**.

**Text Overlay Sizing Rules:**
- **Small:** Use a font size between 48–60px (3.75–4.7% of image height). The text should be subtle, always legible on mobile, and never dominate the thumbnail.
- **Medium (Default):** Use a font size between 72–96px (5.6–7.5% of image height). The text should be prominent and easily readable, but well-balanced with other visual elements. Never crowd the image or touch the edges.
- **Large:** Use a font size between 110–140px (8.5–11% of image height). The text should be the main focal point, but must always fit within the 40px safe zone. If the text is too long, automatically reduce the font size or wrap to multiple lines for optimal fit and legibility.

**Universal Rules:**
- Always use a modern, bold, sans-serif font for maximum legibility.
- All text overlays must be written in ALL CAPS.
- Maintain at least a 40px margin from all edges (safe zone).
- If the background is busy, increase text contrast with a strong outline or glow.
- Never allow text to overlap faces, key objects, or brand logos.
- For 3+ word headlines, use a pyramid or stacked layout for visual balance.
- For short headlines, use a single line or two lines with balanced spacing.
- Ensure text is always readable on both desktop and mobile.

**Output:**  
A thumbnail with perfectly sized, readable, and visually balanced text overlay, following all Context7MCP sizing and placement rules.