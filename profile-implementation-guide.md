# 🚀 User Profile Dashboard - Quick Implementation Guide

## 📋 Implementation Checklist

### ✅ Phase 1: Setup & Navigation (Start Here)
1. **Add Profile Dropdown to Header**
   ```javascript
   // In main app header, add user avatar with dropdown
   React.createElement('div', { className: 'profile-dropdown-container' },
       React.createElement('button', { 
           className: 'profile-avatar-button',
           onClick: toggleDropdown 
       },
           React.createElement('img', { 
               src: userAvatar || defaultAvatar,
               className: 'profile-avatar'
           }),
           React.createElement('span', {
               className: 'iconify',
               'data-icon': 'solar:alt-arrow-down-linear',
               style: { color: '#ffffff', width: '16px', height: '16px' }
           })
       )
   )
   ```

2. **Create Profile Route**
   ```javascript
   // Add to your routing system
   { path: '/profile', component: UserProfilePage }
   ```

### ✅ Phase 2: Core Components

#### ProfileHeader Component
```javascript
const ProfileHeader = ({ user }) => {
    return React.createElement('div', { className: 'profile-header profile-card' },
        // Avatar section
        React.createElement('div', { className: 'profile-avatar-section' },
            React.createElement('img', {
                src: user.avatar,
                className: 'profile-avatar-large',
                alt: 'User avatar'
            }),
            React.createElement('button', { className: 'profile-change-avatar-btn' },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:camera-bold',
                    style: { color: '#006FEE', width: '20px', height: '20px' }
                }),
                'Change Photo'
            )
        ),
        // User info
        React.createElement('div', { className: 'profile-user-info' },
            React.createElement('h2', { className: 'profile-username' }, user.username),
            React.createElement('p', { className: 'profile-email' }, user.email),
            React.createElement('span', { className: 'profile-plan-badge' }, user.plan)
        )
    );
};
```

#### UsageDashboard Component
```javascript
const UsageDashboard = ({ credits, usage }) => {
    const usagePercentage = (credits.used / credits.total) * 100;
    
    return React.createElement('div', { className: 'profile-usage-dashboard profile-card' },
        React.createElement('div', { className: 'profile-section-header' },
            React.createElement('span', {
                className: 'iconify',
                'data-icon': 'solar:chart-2-bold',
                style: { color: '#006FEE', width: '24px', height: '24px' }
            }),
            'Usage & Credits'
        ),
        // Progress bar
        React.createElement('div', { className: 'profile-credits-overview' },
            React.createElement('div', { className: 'profile-credits-numbers' },
                React.createElement('span', { className: 'profile-credits-used' }, 
                    `${credits.used} used`
                ),
                React.createElement('span', { className: 'profile-credits-total' }, 
                    `of ${credits.total} credits`
                )
            ),
            React.createElement('div', { className: 'profile-progress-bar' },
                React.createElement('div', { 
                    className: 'profile-progress-fill',
                    style: { width: `${usagePercentage}%` }
                })
            )
        ),
        // Quality breakdown
        React.createElement('div', { className: 'profile-quality-breakdown' },
            Object.entries(usage).map(([quality, data]) =>
                React.createElement('div', { 
                    key: quality,
                    className: 'profile-quality-item' 
                },
                    React.createElement('span', { className: 'profile-quality-name' }, 
                        quality.charAt(0).toUpperCase() + quality.slice(1)
                    ),
                    React.createElement('span', { className: 'profile-quality-usage' }, 
                        `${data.used} generations`
                    ),
                    React.createElement('span', { className: 'profile-quality-cost' }, 
                        `${data.cost} credits each`
                    )
                )
            )
        )
    );
};
```

### ✅ Phase 3: Styling (profile.css)

```css
/* Profile Container */
.user-profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
    min-height: 100vh;
    background: #1a1a1a;
}

/* Profile Cards */
.profile-card {
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

/* Profile Header */
.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #006FEE;
    margin-bottom: 1rem;
}

.profile-username {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.profile-email {
    color: #a0a0a0;
    margin-bottom: 1rem;
}

.profile-plan-badge {
    background: #006FEE;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Usage Dashboard */
.profile-credits-overview {
    margin-bottom: 1.5rem;
}

.profile-credits-numbers {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.profile-credits-used {
    color: #ffffff;
    font-weight: 600;
}

.profile-credits-total {
    color: #a0a0a0;
}

.profile-progress-bar {
    width: 100%;
    height: 8px;
    background: #3a3a3a;
    border-radius: 4px;
    overflow: hidden;
}

.profile-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #006FEE, #0080ff);
    transition: width 0.3s ease;
}

/* Quality Breakdown */
.profile-quality-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.profile-quality-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #3a3a3a;
}

.profile-quality-name {
    font-weight: 500;
    color: #ffffff;
    text-transform: capitalize;
}

.profile-quality-usage {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.profile-quality-cost {
    color: #006FEE;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .user-profile-container {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1.5rem;
    }
    
    .profile-avatar-large {
        width: 100px;
        height: 100px;
    }
    
    .profile-quality-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
```

### ✅ Phase 4: Mock Data Setup

```javascript
// src/utils/profileMockData.js
export const mockUserProfile = {
    id: 'user_123',
    username: 'john_creator',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    plan: 'Basic',
    joinDate: '2024-01-15',
    credits: {
        total: 700,
        used: 245,
        remaining: 455
    },
    usage: {
        low: { used: 120, cost: 1 },
        medium: { used: 85, cost: 2 },
        high: { used: 40, cost: 4 }
    }
};

export const mockGenerationHistory = [
    {
        id: 'gen_001',
        thumbnail: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300',
        prompt: 'Epic mountain adventure vlog thumbnail',
        quality: 'High',
        date: '2024-03-15T10:30:00Z',
        credits: 4
    },
    {
        id: 'gen_002',
        thumbnail: 'https://images.unsplash.com/photo-1551632811-561732d1e306?w=300',
        prompt: 'Gaming setup review thumbnail',
        quality: 'Medium',
        date: '2024-03-14T15:45:00Z',
        credits: 2
    },
    // Add more mock data...
];
```

## 🎯 Key Solar Icons to Use

```javascript
// Common icons for profile features
const profileIcons = {
    user: 'solar:user-circle-bold',
    settings: 'solar:settings-linear',
    history: 'solar:history-bold',
    credits: 'solar:wallet-money-bold',
    chart: 'solar:chart-2-bold',
    upgrade: 'solar:crown-bold',
    billing: 'solar:credit-card-bold-duotone',
    camera: 'solar:camera-bold',
    edit: 'solar:pen-bold',
    download: 'solar:download-bold',
    search: 'solar:magnifer-linear',
    filter: 'solar:filter-bold',
    arrow: 'solar:alt-arrow-down-linear'
};
```

## 🚨 Important Reminders

1. **No Backend Calls**: Everything should work with mock data
2. **CSS Prefixing**: All CSS classes start with `.profile-`
3. **Mobile First**: Design for mobile, enhance for desktop
4. **Hero UI Only**: Use Hero UI components from CDN
5. **Solar Icons**: Use the specified format for all icons
6. **Dark Mode**: Consistent with existing app theme
7. **No Conflicts**: Don't modify existing components or styles

## 🔗 Integration Points

- Add profile dropdown to main app header
- Create `/profile` route
- Import profile.css in main CSS file
- Keep profile state isolated from main app state

This guide provides everything needed to implement a complete, professional user profile dashboard that matches your requirements! 