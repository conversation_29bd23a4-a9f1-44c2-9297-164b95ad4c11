---
description: 
globs: 
alwaysApply: true
---
@loader-progress-finalization
ruleId: loader-progress-finalization
description: >
  Ensures the loader/progress indicator never appears frozen at 99% during thumbnail generation, especially in high quality mode. When progress reaches 99% and the backend is still processing, the indicator should show a subtle ongoing animation (such as a shimmer, pulse, or looping effect) and optionally display a “Finalizing…” message. Only display 100% and reveal the thumbnail when the image is actually ready.

appliesTo:
  - /src/App.jsx
  - /src/components/ThumbnailPreview.jsx
  - /src/styles/preview.css

ruleType: always

implementationNotes: |
  - When progress reaches 99% and the image is not ready, do not freeze the indicator.
  - Animate the progress bar or number (e.g., shimmer, pulse, or looping dots) to indicate ongoing work.
  - Optionally, show a “Finalizing…” or “Rendering details…” message below the progress.
  - As soon as the image is ready, jump to 100% and display the result.
  - Animation should be subtle and not distracting.
  - Applies to all quality modes, but is most important for high/slow modes.

acceptanceCriteria:
  - Progress indicator never appears stuck at 99%.
  - Ongoing animation is visible while waiting for the backend.
  - 100% is only shown when the thumbnail is ready.
  - User always perceives the app as responsive and working.

sampleUI: |
  [ 99% ] [ Finalizing… ]
  (progress bar with shimmer or pulse)
  (thumbnail appears instantly at 100%)