// PromptInput.jsx - Enhanced version with better UX, real-time preview, and suggestions
import React, { useState, useEffect, useRef } from 'react'
import { suggestedPrompts } from '../utils/typewriterSuggestions.js';

// Named export for PromptInput component
export const PromptInput = ({ value, onChange, isLocked, onImprovePrompt, isTyping, cursorVisible, isLoading = false }) => {
    
    // State for suggestions, preview, and enhancements
    const [placeholderText, setPlaceholderText] = useState('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
    const [enhancedPrompt, setEnhancedPrompt] = useState('');
    const [wasAutoEnhanced, setWasAutoEnhanced] = useState(false);
    const [charCount, setCharCount] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const [focusPlaceholder, setFocusPlaceholder] = useState('');
    
    // Refs for animation and input element
    const typewriterRef = useRef(null);
    const inputRef = useRef(null);
    
    // Focus-state placeholder suggestions
    const focusPlaceholders = [
        'Start typing your thumbnail idea...',
        'Describe your video content...',
        'What\'s the main subject of your video?',
        'Try: "Epic gaming setup reveal with RGB lighting"',
        'Try: "Cooking the perfect pasta from scratch"',
        'Try: "Reviewing the latest iPhone features"'
    ];
    
    // Set focus placeholder when component mounts
    useEffect(() => {
        const randomPlaceholder = focusPlaceholders[Math.floor(Math.random() * focusPlaceholders.length)];
        setFocusPlaceholder(randomPlaceholder);
    }, []);
    
    // Update character count when value changes
    useEffect(() => {
        setCharCount(value?.length || 0);
    }, [value]);
    
    // Simulate enhanced prompt (in a real implementation, this would call the actual enhance function)
    useEffect(() => {
        if (value) {
            // Simple simulation of enhancement for demo
            const simulateEnhancedPrompt = () => {
                let enhanced = value;
                
                // Detect if this is a gaming-related prompt
                const gamingKeywords = ['fortnite', 'game', 'gaming', 'minecraft', 'cod', 'gta', 'playstation', 'xbox'];
                const isGamingPrompt = gamingKeywords.some(keyword => 
                    value.toLowerCase().includes(keyword)
                );
                
                if (isGamingPrompt) {
                    enhanced = `A cinematic, high-energy gaming scene showing ${value} with dramatic lighting, vivid colors, and professional composition.`;
                    setWasAutoEnhanced(true);
                } else {
                    enhanced = `A cinematic, visually striking scene depicting ${value} with professional lighting, balanced composition, and eye-catching visual elements.`;
                    setWasAutoEnhanced(false);
                }
                
                setEnhancedPrompt(enhanced);
            };
            
            // Debounce the enhancement simulation
            const timer = setTimeout(simulateEnhancedPrompt, 300);
            return () => clearTimeout(timer);
        } else {
            setEnhancedPrompt('');
            setWasAutoEnhanced(false);
        }
    }, [value]);
    
    // Typewriter animation effect from original component
    useEffect(() => {
        if (window.startTypewriter && !value && !isLocked && !isTyping && 
            !window.prefersReducedMotion?.()) {
            const timeoutId = setTimeout(() => {
                if (inputRef.current) {
                    typewriterRef.current = window.startTypewriter(
                        inputRef.current,
                        suggestedPrompts,
                        setPlaceholderText
                    );
                }
            }, 800);
            
            return () => {
                clearTimeout(timeoutId);
                if (typewriterRef.current) typewriterRef.current.stop();
            };
        }
    }, []);
    
    // Stop animation when content changes
    useEffect(() => {
        if (value && typewriterRef.current) {
            typewriterRef.current.stop();
            setPlaceholderText('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
        }
    }, [value]);
    
    // Handle focus/blur for animation control
    const handleFocus = () => {
        setIsFocused(true);
        if (typewriterRef.current && !value) typewriterRef.current.stop();
    };
    
    const handleBlur = () => {
        setIsFocused(false);
        if (!value && !isLocked && !isTyping && !window.prefersReducedMotion?.()) {
            if (inputRef.current) {
                typewriterRef.current = window.startTypewriter(
                    inputRef.current,
                    suggestedPrompts,
                    setPlaceholderText
                );
            }
        }
    };
    
    // Handle suggestion click
    const handleSuggestionClick = (suggestion) => {
        if (!isLocked && !isTyping && !isLoading) {
            onChange({ target: { value: suggestion } });
        }
    };
    
    // Render the component
    return (
        React.createElement('div', { 
            className: 'prompt-section w-full bg-gray-800 rounded-lg shadow-md p-4', 
            id: 'prompt-section' 
        },
            // Section header with label
            React.createElement('div', { 
                className: 'prompt-header flex items-center justify-between mb-2',
                id: 'prompt-header'
            },
                React.createElement('label', {
                    htmlFor: 'prompt-textarea',
                    className: 'prompt-label block text-md font-medium text-purple-300'
                }, 'Thumbnail Prompt'),
                React.createElement('span', {
                    className: 'prompt-char-count text-xs text-gray-400',
                    id: 'prompt-char-count'
                }, `${charCount}/200`)
            ),
            
            // Main textarea input
            React.createElement('div', { 
                className: 'prompt-textarea-container relative mb-2', 
                id: 'prompt-textarea-container' 
            },
                React.createElement('textarea', {
                    id: 'prompt-textarea',
                    className: `prompt-textarea w-full p-3 rounded-md text-gray-100 placeholder-gray-400 resize-none transition-all ${
                        isLocked || isTyping || isLoading 
                            ? 'border-gray-600 opacity-70 bg-gray-750 cursor-not-allowed' 
                            : 'border-gray-600 bg-gray-700 focus:ring-2 focus:ring-purple-500 focus:border-purple-500'
                    }`,
                    rows: '4',
                    placeholder: isFocused && !value ? '' : placeholderText,
                    value: isTyping ? value + (cursorVisible ? '|' : '') : value,
                    onChange: isLoading ? undefined : onChange,
                    onFocus: isLoading ? undefined : handleFocus,
                    onBlur: isLoading ? undefined : handleBlur,
                    disabled: isLocked || isTyping || isLoading,
                    maxLength: 200,
                    'aria-describedby': 'prompt-char-count prompt-preview-label',
                    'aria-disabled': isLocked || isTyping || isLoading,
                    'aria-label': isLoading ? 'Prompt input disabled during generation' : 'Enter your thumbnail prompt',
                    title: isLoading ? 'Please wait for generation to complete' : 'Enter your thumbnail prompt',
                    ref: inputRef
                }),
                
                // Focus-state placeholder overlay
                isFocused && !value && !isLocked && !isTyping && React.createElement('div', {
                    className: 'prompt-focus-placeholder absolute top-3 left-3 pointer-events-none text-gray-400 transition-opacity duration-200',
                    id: 'prompt-focus-placeholder',
                    style: {
                        fontFamily: "'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace",
                        fontSize: '0.875rem',
                        lineHeight: '1.4',
                        letterSpacing: '0.025em',
                        fontWeight: '400'
                    }
                }, focusPlaceholder),
                
                // Improve prompt button
                React.createElement('button', {
                    id: 'prompt-improve-btn',
                    className: `prompt-improve-btn control-panel-improve-btn absolute bottom-3 right-3 text-xs flex items-center gap-1 rounded p-1 transition-all duration-200 ${
                        isLocked || isTyping || isLoading
                            ? 'text-gray-500 cursor-not-allowed opacity-50'
                            : 'text-gray-400 hover:text-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500'
                    }`,
                    onClick: (isLocked || isTyping || isLoading) ? undefined : () => onImprovePrompt(),
                    disabled: isLocked || isTyping || isLoading,
                    'aria-label': isLoading ? 'Enhance prompt disabled during generation' : 'Enhance prompt with AI',
                    title: isLoading ? 'Please wait for generation to complete' : 'Enhance prompt with AI',
                    type: 'button'
                },
                    React.createElement('span', {
                        className: 'improve-btn-icon iconify',
                        id: 'improve-btn-icon',
                        'data-icon': 'solar:file-text-line-duotone',
                        style: { width: '16px', height: '16px' }
                    }),
                    'Improve prompt'
                ),
                
                // Locked template indicator (from original)
                isLocked && React.createElement('div', {
                    id: 'prompt-lock-indicator',
                    className: 'prompt-lock-indicator absolute top-0 right-0 mt-1 mr-1 p-1 bg-yellow-500 text-black text-xs rounded-sm shadow-md flex items-center gap-1 group',
                    role: 'note'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        viewBox: '0 0 20 20',
                        fill: 'currentColor',
                        className: 'w-4 h-4'
                    },
                        React.createElement('path', {
                            fillRule: 'evenodd',
                            d: 'M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v3a2 2 0 002 2h10a2 2 0 002-2v-3a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z',
                            clipRule: 'evenodd'
                        })
                    ),
                    'Locked',
                    React.createElement('div', {
                        className: 'prompt-lock-tooltip absolute bottom-full right-0 mb-2 w-72 bg-gray-800 text-gray-100 text-xs rounded-md p-2 shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 z-50',
                        id: 'prompt-lock-tooltip',
                        role: 'tooltip'
                    }, 'Prompt is locked while a template is active. Use the controls below to customize the thumbnail.')
                )
            ),
            
            // Status indicators (enhancing, auto-enhanced)
            React.createElement('div', { 
                className: 'prompt-status-row flex items-center gap-3 mb-2',
                id: 'prompt-status-row'
            },
                isTyping && React.createElement('div', {
                    className: 'prompt-enhancing-badge py-1 px-2 bg-purple-700/30 border border-purple-600 rounded-full text-xs text-purple-300 flex items-center gap-1',
                    id: 'prompt-enhancing-badge'
                },
                    React.createElement('svg', {
                        className: 'animate-spin h-3 w-3 text-purple-400',
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('circle', {
                            className: 'opacity-25',
                            cx: '12',
                            cy: '12',
                            r: '10',
                            stroke: 'currentColor',
                            strokeWidth: '4'
                        }),
                        React.createElement('path', {
                            className: 'opacity-75',
                            fill: 'currentColor',
                            d: 'M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                        })
                    ),
                    'Enhancing...'
                ),
                wasAutoEnhanced && !isTyping && React.createElement('div', {
                    className: 'prompt-auto-enhanced-badge py-1 px-2 bg-blue-700/30 border border-blue-600 rounded-full text-xs text-blue-300 flex items-center gap-1',
                    id: 'prompt-auto-enhanced-badge'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        strokeWidth: 1.5,
                        stroke: 'currentColor',
                        className: 'w-3 h-3'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            d: 'M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z'
                        })
                    ),
                    'Auto-enhanced for gaming'
                )
            ),
            
            // Suggestions
            React.createElement('div', { 
                className: 'prompt-suggestions-row mb-3',
                id: 'prompt-suggestions-row'
            },
                React.createElement('label', { 
                    className: 'prompt-suggestions-label block text-xs text-gray-400 mb-1.5',
                    id: 'prompt-suggestions-label'
                }, 'Suggestions:'),
                React.createElement('div', { 
                    className: 'prompt-suggestions-chips flex flex-wrap gap-2',
                    id: 'prompt-suggestions-chips'
                },
                    // Show 4 suggestions (could be randomized or sorted by relevance in real impl)
                    suggestedPrompts.slice(0, 4).map((suggestion, i) => (
                        React.createElement('button', {
                            key: i,
                            id: `prompt-suggestion-${i}`,
                            className: `prompt-suggestion-chip py-1 px-3 text-xs rounded-full border transition-all duration-200 ${
                                isLocked || isTyping || isLoading
                                    ? 'bg-gray-750 text-gray-500 border-gray-700 cursor-not-allowed opacity-50'
                                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600 hover:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500'
                            }`,
                            onClick: (isLocked || isTyping || isLoading) ? undefined : () => handleSuggestionClick(suggestion),
                            disabled: isLocked || isTyping || isLoading,
                            'aria-label': isLoading ? `Suggestion "${suggestion.split(' ').slice(0, 3).join(' ')}..." disabled during generation` : `Use suggestion: ${suggestion}`,
                            title: isLoading ? 'Please wait for generation to complete' : `Use suggestion: ${suggestion}`,
                            type: 'button'
                        }, suggestion.split(' ').slice(0, 3).join(' ') + '...')
                    ))
                )
            ),
            
            // Enhanced Prompt Preview
            enhancedPrompt && React.createElement('div', { 
                className: 'prompt-preview-block p-2 bg-gray-700/50 border border-gray-600 rounded-md',
                id: 'prompt-preview-block'
            },
                React.createElement('label', { 
                    className: 'prompt-preview-label block text-xs font-medium text-gray-400 mb-1',
                    id: 'prompt-preview-label'
                }, 'Preview of enhanced prompt:'),
                React.createElement('div', { 
                    className: 'prompt-preview-text text-xs text-gray-300 line-clamp-2',
                    id: 'prompt-preview-text'
                }, enhancedPrompt)
            )
        )
    );
};

// Default export for the component
export default PromptInput; 