/* ================= LIQUID GLASS SIDEBAR REDESIGN ================= */
/* MacOS-inspired glass morphism design matching auth pages */

:root {
    --glass-bg-primary: rgba(255, 255, 255, 0.04);
    --glass-bg-secondary: rgba(255, 255, 255, 0.02);
    --glass-border-primary: rgba(255, 255, 255, 0.08);
    --glass-border-secondary: rgba(255, 255, 255, 0.06);
    --glass-text-primary: #e2e8f0;
    --glass-text-secondary: #94a3b8;
    --glass-accent: #8b5cf6;
    --glass-accent-hover: #7c3aed;
    --glass-shadow-primary: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    --glass-shadow-inset: inset 0 1px 0 rgba(255, 255, 255, 0.08);
    --glass-transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Main sidebar container with glass morphism */
.design-controls-glass-container {
    background: var(--glass-bg-primary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-primary);
    border-radius: 24px;
    /* box-shadow: 
        var(--glass-shadow-primary),
        0 0 0 1px rgba(255, 255, 255, 0.04),
        var(--glass-shadow-inset); */
    position: relative;
    overflow: hidden;
    transition: var(--glass-transition);
    animation: glass-sidebar-entrance 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Entrance animation */
@keyframes glass-sidebar-entrance {
    0% { 
        opacity: 0;
        transform: translateX(20px) scale(0.95);
    }
    100% { 
        opacity: 1;
        transform: translateX(0px) scale(1);
    }
}

/* Subtle shimmer effect on the top border */
.design-controls-glass-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    animation: glass-shimmer 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes glass-shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Section cards within the sidebar */
.glass-section-card {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border-secondary);
    border-radius: 12px;
    padding: 0.5rem;
    margin: 0.375rem 0;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: var(--glass-transition);
    position: relative;
    overflow: hidden;
}

.glass-section-card:hover {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced typography for glass design */
.glass-section-title {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    color: var(--glass-text-primary);
    /* margin-bottom: 0.75rem; */
    letter-spacing: -0.025em;
}

.glass-label {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--glass-text-primary);
    margin-bottom: 0.5rem;
    display: block;
    transition: var(--glass-transition);
}

.glass-description {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.8rem;
    font-weight: 400;
    color: var(--glass-text-secondary);
    line-height: 1.5;
}

/* Glass toggle switches */
.glass-toggle-switch {
    background: rgba(139, 92, 246, 0.2);
    /* border: none; */
    box-shadow: inset 0 0 0 1px rgba(139, 92, 246, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 12px;
    transition: var(--glass-transition);
    position: relative;
    overflow: hidden;
}

.glass-toggle-switch.active,
.glass-toggle-switch:checked {
    background: linear-gradient(135deg, var(--glass-accent) 0%, var(--glass-accent-hover) 100%);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.glass-toggle-switch:hover {
    /* transform: translateY(-1px); */
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
}

/* Glass input fields and dropdowns */
.glass-input {
    width: 100%;
    padding: 0.625rem;
    max-height: 5rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: var(--glass-transition);
    outline: none;
}

/* Apple Liquid Glass Native Select */
.glass-select {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem; /* Extra padding-right for custom chevron */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Geist', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: border-color 200ms ease-in-out, background-color 200ms ease-in-out;
    outline: none;
    cursor: pointer;
    appearance: none; /* Remove default browser styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
}

/* Glass select wrapper for custom chevron positioning */
.glass-select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

/* Custom chevron for glass select */
.glass-select-chevron {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    color: #B0B3B8;
    pointer-events: none;
    transition: color 200ms ease-in-out;
}

/* Glass select focus and hover states */
.glass-select:focus {
    border-color: rgba(139, 92, 246, 0.4);
    background: rgba(255, 255, 255, 0.08);
}

.glass-select:hover:not(:focus) {
    border-color: rgba(255, 255, 255, 0.18);
    background: rgba(255, 255, 255, 0.07);
}

.glass-select:hover:not(:focus) + .glass-select-chevron {
    color: #D1D5DB;
}

/* Glass select disabled state */
.glass-select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.06);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-select {
        border-color: rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.1);
    }
    
    .glass-select:focus {
        border-color: rgba(139, 92, 246, 0.8);
    }
}

.glass-input::placeholder {
    color: var(--glass-text-secondary);
    transition: var(--glass-transition);
}

.glass-input:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 
        0 0 0 3px rgba(139, 92, 246, 0.1),
        0 4px 12px rgba(139, 92, 246, 0.15);
    transform: translateY(-1px);
}

.glass-input:focus::placeholder {
    color: #94a3b8;
}

/* Glass color picker container */
.glass-color-picker {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.375rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: var(--glass-transition);
    position: relative;
}

.glass-color-picker:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
}

/* Glass button groups (text size selector) */
.glass-button-group {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 0.125rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    position: relative;
    overflow: hidden;
}

.glass-button-group button {
    flex: 1;
    padding: 0.25rem 0.5rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--glass-text-secondary);
    background: transparent;
    border: none;
    border-radius: 6px;
    transition: var(--glass-transition);
    cursor: pointer;
    position: relative;
    z-index: 2;
}

.glass-button-group button.active,
.glass-button-group button[aria-pressed="true"] {
    color: #ffffff;
    background: linear-gradient(135deg, var(--glass-accent) 0%, var(--glass-accent-hover) 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.glass-button-group button:hover:not(.active) {
    color: var(--glass-text-primary);
    background: rgba(255, 255, 255, 0.05);
}

/* Enhanced collapsible sections */
.glass-collapsible-section {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border-secondary);
    border-radius: 12px;
    margin: 0.45rem 0;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: var(--glass-transition);
    overflow: hidden;
}

.glass-collapsible-header {
    padding: 0.5rem 0.5rem !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: between;
    /* background: rgba(255, 255, 255, 0.02); */
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.04); */
    transition: var(--glass-transition);
}

.glass-collapsible-header:hover {
    background: rgba(255, 255, 255, 0.04);
    border-bottom-color: rgba(255, 255, 255, 0.08);
}

.glass-collapsible-content {
    padding: 0.5rem;
    transition: all 0.3s ease-in-out;
}

.glass-collapsible-content.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    overflow: hidden;
}

.glass-collapsible-content.expanded {
    max-height: 1000px;
}

/* Dynamic max-height fix for face upload collapsible section */
.glass-collapsible-content.expanded.face-upload-active {
    max-height: none !important;
    height: auto !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .design-controls-glass-container {
        border-radius: 16px;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }
    
    .glass-section-card {
        padding: 0.375rem;
        margin: 0.25rem 0;
    }
    
    .glass-collapsible-header,
    .glass-collapsible-content {
        padding: 0.375rem;
    }
}

/* Browser compatibility fallbacks */
@supports not (backdrop-filter: blur(20px)) {
    .design-controls-glass-container {
        background: rgba(15, 23, 42, 0.95);
    }
    
    .glass-section-card {
        background: rgba(30, 41, 59, 0.8);
    }
    
    .glass-input {
        background: rgba(51, 65, 85, 0.8);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .design-controls-glass-container {
        border: 2px solid #ffffff;
        background: rgba(0, 0, 0, 0.9);
    }
    
    .glass-section-card {
        border: 1px solid #ffffff;
        background: rgba(30, 30, 30, 0.9);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .design-controls-glass-container,
    .glass-section-card,
    .glass-toggle-switch,
    .glass-input,
    .glass-button-group button {
        animation: none;
        transition: none;
    }
}

/* ================= END LIQUID GLASS SIDEBAR ================= */

/* Styles specific to the control panel components */
/* (e.g., toggle switches, dropdowns) if needed beyond Tailwind utilities */

.some-control-class {
	/* Example */
 	   /* Mix space and tab */
    border: 1px solid red; /* Example - unlikely needed with Tailwind */
}

/* ================= COLOR PICKER TOOLTIPS ================= */

/* Color tooltip styles */
.color-tooltip {
    pointer-events: none;
    animation: tooltipFadeIn 0.2s ease-out;
    min-width: max-content;
}

/* ================= TEXT COLOR & TYPOGRAPHY SECTION ================= */

/* Prevent overlap in text color section */
.text-color-typography-control-section {
    clear: both;
    position: relative;
    z-index: 1;
    padding: 0 8px; /* Add horizontal padding to prevent clipping */
    margin: 0 -8px; /* Negative margin to maintain visual alignment */
}

/* Enhanced color palette circles - optimized for 9 colors with responsive spacing */
.color-palette-circles {
    position: relative;
    z-index: 2;
    /* margin-bottom: 1rem; */
    padding: 4px 6px; /* Slightly more horizontal padding */
    margin-left: -6px;
    margin-right: -6px;
    display: flex;
    align-items: center;
    justify-content: space-between; /* Center the circles */
    gap: 0.25rem; /* Optimized gap for 9 colors */
    flex-wrap: nowrap; /* Prevent wrapping */
}

/* Enhanced color circle styling with optimized responsiveness */
.color-circle {
    width: 22px !important;
    height: 22px !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    /* Faster transitions for better responsiveness */
    transition: transform 0.15s ease, box-shadow 0.15s ease !important;
    will-change: transform, box-shadow !important;
    position: relative !important;
    flex-shrink: 0 !important;
    border: none !important;
    outline: none !important;
}

/* Hover state with snappy feedback */
.color-circle:hover {
    transform: scale(1.2) !important;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4) !important;
}

/* Active state with immediate click feedback */
.color-circle:active {
    transform: scale(0.9) !important;
    transition: transform 0.1s ease !important; /* Even faster for click feedback */
}

/* Focus state for accessibility */
.color-circle:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8) !important;
    /* transform: scale(1.1) !important; */
}

/* Enhanced active/selected state */
.color-circle.active,
.color-circle.selected {
    transform: scale(1.15) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9), 0 4px 16px rgba(0, 0, 0, 0.5) !important;
}

/* Special border for black color circle for better visibility */
.color-circle[style*="#000000"],
.color-circle[style*="#000"] {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Prevent text selection on color circles */
.color-circle {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* Ensure consistent sizing across browsers */
.color-circle,
.color-circle * {
    box-sizing: border-box !important;
}

/* Mobile optimizations for color circles */
@media (max-width: 640px) {
    .color-palette-circles {
        gap: 0.1rem; /* Slightly smaller gap on mobile */
        padding: 4px 4px;
        /* margin-left: -4px;
        margin-right: -4px; */
    }
    
    .color-circle {
        width: 26px !important; /* Slightly smaller on mobile */
        height: 26px !important;
    }
    
    /* Adjust touch targets for mobile */
    .color-circle:hover {
        transform: scale(1.3) !important; /* Larger hover effect for touch */
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .color-circle {
        /* Ensure crisp edges on retina displays */
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
    }
}

/* Selected color display spacing */
.selected-color-display {
    position: relative;
    z-index: 1;
    margin-bottom: 0.75rem; /* 12px */
}

/* Secondary color note spacing */
.secondary-color-note {
    position: relative;
    z-index: 1;
    margin-bottom: 1.5rem; /* 24px */
    clear: both;
}

/* Live preview container spacing */
.text-style-live-preview-container {
    position: relative;
    z-index: 1;
    margin-top: 1rem; /* 16px */
    clear: both;
}

/* Color tooltip arrows - different positions based on tooltip alignment */
.color-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    border: 4px solid transparent;
    border-top-color: #111827;
    z-index: 1;
}

/* Default center arrow for middle tooltips */
.color-tooltip.absolute.left-1\/2::after {
    left: 50%;
    transform: translateX(-50%);
}

/* Left-aligned tooltip arrow */
.color-tooltip.absolute.left-0::after {
    left: 1rem; /* Position arrow towards the left side */
}

/* Right-aligned tooltip arrow */
.color-tooltip.absolute.right-0::after {
    right: 1rem; /* Position arrow towards the right side */
}

/* Color circle container positioning */
.color-circle-container {
    position: relative;
    z-index: 1;
    flex-shrink: 0; /* Prevent shrinking */
}

/* Color circle styling improvements */
.color-circle {
    position: relative;
    z-index: 2;
    /* Ensure proper spacing for ring offset */
    min-width: 14px;
    min-height: 14px;
}

/* Fix for clipped rings on first/last items */
.color-palette-circles .color-circle-container:first-child {
    margin-left: 2px; /* Extra space for left ring */
}

.color-palette-circles .color-circle-container:last-child {
    margin-right: 2px; /* Extra space for right ring */
}

/* Ensure tooltips appear above other elements */
.color-tooltip {
    z-index: 100 !important;
}

/* Dead CSS */
/* .old-toggle-style { color: blue; } */

.bgModal-backdrop {
	z-index: 50 !important;
}

.bgModal-container {
	z-index: 60 !important;
}

/* ================= PREMIUM LOADING INDICATOR ================= */

/* Loading indicator container */
.loading-indicator-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
}

/* Loading indicator container for preview panel */
.preview-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
}

/* Numeric percentage display - UPDATED: Lighter colors */
.loading-percentage {
    font-size: 3.5rem;
    font-weight: 900;
    color: #E5E7EB; /* Light gray instead of sky blue */
    text-shadow: 0 0 12px rgba(229, 231, 235, 0.3); /* Subtle light glow */
    letter-spacing: -0.02em;
    line-height: 1;
    margin-bottom: 0.5rem;
}

/* Container for the animated text */
.loading-text-container {
    position: relative;
    color: rgba(181, 181, 181, 0.7); /* Base fallback color */
}

/* Cursor thinking text animation */
.cursor-thinking-text {
    font-weight: 500;
    font-size: 1rem;
    color: transparent; /* Crucial for background-clip to work */
    background: linear-gradient(
        90deg, 
        rgba(181, 181, 181, 0.85) 20%, 
        #FFFFFF 50%, 
        rgba(181, 181, 181, 0.85) 80%
    );
    background-size: 200% 100%; /* Ensure gradient is wide enough to move */
    -webkit-background-clip: text;
    background-clip: text;
    animation: cursorThinking 2.42s linear infinite;
}

@keyframes cursorThinking {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* Ensure cursor thinking animation is available globally */
.cursor-thinking-text {
    font-weight: 500;
    font-size: 1rem;
    color: transparent;
    background: linear-gradient(
        90deg, 
        rgba(181, 181, 181, 0.85) 20%, 
        #FFFFFF 50%, 
        rgba(181, 181, 181, 0.85) 80%
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    animation: cursorThinking 2.42s linear infinite;
}

/* Enhanced Desktop Preview Loader Number Sizing - 25% increase at 768px+ */
@media (min-width: 768px) {
    .loading-percentage {
        font-size: 4.375rem; /* 3.5rem * 1.25 = 4.375rem (25% increase) */
    }
}

/* Additional enhancement for larger desktop screens */
@media (min-width: 1024px) {
    .loading-percentage {
        font-size: 4.5rem; /* Slightly larger for large desktop screens */
    }
}

/* Enhanced sizing for very large screens */
@media (min-width: 1440px) {
    .loading-percentage {
        font-size: 4.75rem; /* Maximum size for very large screens */
    }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .loading-percentage {
        font-size: 2.5rem;
    }
    
    .cursor-thinking-text {
        font-size: 1rem;
    }
    
    .loading-indicator-container {
        padding: 1.5rem;
        gap: 0.75rem;
    }
}

@media (max-width: 768px) {
    .loading-indicator-container {
        padding: 1.5rem;
        gap: 0.75rem;
    }
}

/* ================= ENHANCED VISIBLE PROGRESS BAR ================= */

/* Preview container loading state - NO animations */
.preview-container.preview-loading {
    overflow: hidden;
    /* No animations - completely static */
}

/* Remove all glow animations */
@keyframes preview-container-fade-pulse {
    /* Animation removed for performance */
}

/* More visible progress bar with lighter colors */
.preview-progress-bar {
    position: relative;
    overflow: hidden;
    background-color: rgba(189, 147, 249, 1.0); /* Lighter gray base - more visible */
    border: 2px solid rgba(217, 185, 255, 1.0); /* Add bright purple border */
    box-shadow: 0 0 20px rgba(189, 147, 249, 0.6); /* Add purple glow */
    height: 8px; /* Increase height for better visibility */
    border-radius: 4px; /* Rounded corners */
}

/* Enhanced scanning animation similar to "Generating Thumbnail" text */
.preview-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(
        90deg,
        rgba(189, 147, 249, 0.0) 0%,
        rgba(255, 255, 255, 0.9) 40%,
        rgba(255, 255, 255, 1.0) 50%, /* Bright white center */
        rgba(255, 255, 255, 0.9) 60%,
        rgba(189, 147, 249, 0.0) 100%
    );
    background-size: 200% 100%; /* Match the text animation */
    animation: progress-bar-flow 2.42s linear infinite; /* Same timing as text */
    opacity: 1; /* Full opacity for visibility */
}

/* Smooth flowing animation matching "Generating Thumbnail" text */
@keyframes progress-bar-flow {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* Enhanced progress overlay container */
.preview-progress-overlay {
    /* Remove any blur effects for cleaner appearance */
    backdrop-filter: none;
    background-color: transparent;
    border: 1px solid rgba(217, 185, 255, 0.5); /* Add subtle purple border */
    box-shadow: 0 -2px 10px rgba(189, 147, 249, 0.3); /* Add subtle shadow */
}

/* Hide glow container completely */
.preview-glow-container {
    display: none !important; /* Hide completely */
}

/* Make progress bar more visible on smaller screens */
@media (max-width: 640px) {
    .preview-progress-overlay {
        height: 10px; /* Increased from 4px for better visibility */
    }
    
    .preview-progress-bar {
        background-color: rgba(196, 167, 231, 1.0); /* Even more visible on mobile */
    }
}

/* ================= ENHANCED PREMIUM LOADING INDICATOR ================= */
.preview-loading-backdrop {
    position: absolute !important;
    inset: 0 !important;
    z-index: 10 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    /* Enhanced gradient background with shimmer effect */
    background: linear-gradient(135deg, 
        rgba(30, 58, 138, 0.8) 0%,
        rgba(124, 58, 237, 0.6) 50%,
        rgba(6, 182, 212, 0.4) 100%) !important;
    border-radius: 12px !important;
    box-shadow: inset 0 0 20px rgba(59, 130, 246, 0.1) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    transition: all 300ms ease-in-out !important;
    overflow: hidden !important;
}

/* Shimmer effect overlay */
.preview-loading-backdrop::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 20%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    animation: shimmer 2s infinite;
    transform: skewX(-45deg);
    z-index: 1;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Progress Bar Shimmer Animation */
@keyframes progressShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Ensure content appears above shimmer */
.preview-loading-backdrop > * {
    position: relative;
    z-index: 2;
}

/* Responsive shimmer optimization */
@media (max-width: 768px) {
    .preview-loading-backdrop::before {
        width: 15%; /* Smaller shimmer width for mobile */
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .preview-loading-backdrop::before {
        animation: none;
    }
    .preview-loading-backdrop {
        transition: none !important;
    }
}

/* Removed progress animation and pseudo-element for performance */
/* .preview-loading-backdrop::after { ... } */
/* @keyframes bottom-progress-fill { ... } */

@keyframes enhanced-backdrop-fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .preview-loading-container {
        padding: 2rem !important;
        gap: 1rem !important;
    }
}

/* ================= NOTIFICATIONS DROPDOWN STYLES ================= */

/* Slide down animation for dropdowns */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Notification dropdown styling */
.notifications-dropdown {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Notification item hover effects */
.notification-item:hover .notification-title {
    color: #E5E7EB;
}

/* Badge pulse animation for unread notifications */
.notification-badge {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* Smooth transitions for notification states */
.notification-item {
    transition: all 0.2s ease;
}

/* Unread notification styling */
.notification-item:not(.read) {
    border-left: 3px solid #3B82F6;
}

/* Icon container styling - optimized for linear icons */
.notification-icon {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Enhanced linear icon styling */
.notification-icon .iconify {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Timestamp calendar icon styling */
.notification-timestamp {
    align-items: center;
    transition: color 0.2s ease;
}

.notification-timestamp .iconify {
    flex-shrink: 0;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-timestamp .iconify {
    opacity: 1;
}

/* Empty state icon styling */
.empty-icon {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(75, 85, 99, 0.05));
}

/* All Caught Up empty state styling */
.notifications-empty-state {
    min-height: 240px;
}

.illustration-container {
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(29, 36, 47, 0.3);
}

.illustration-container:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(29, 36, 47, 0.4);
}

.empty-state-illustration img {
    transition: all 0.3s ease;
}

.empty-state-illustration:hover img {
    filter: opacity(1) !important;
}

/* Revert button styling */
.notifications-header button {
    font-weight: 500;
    transition: all 0.2s ease;
}

.notifications-header button:hover {
    transform: translateY(-1px);
}

/* Enhanced empty state animations */
.notifications-empty-state h3 {
    animation: fadeInUp 0.6s ease-out;
}

.notifications-empty-state p {
    animation: fadeInUp 0.8s ease-out;
}

.empty-state-illustration {
    animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive adjustments for notifications */
@media (max-width: 640px) {
    .notifications-dropdown {
        width: 320px;
        right: -20px; /* Adjust position on smaller screens */
    }
    
    .notification-item {
        padding: 12px 16px;
    }
    
    .notification-title {
        font-size: 13px;
    }
    
    .notification-description {
        font-size: 12px;
    }

    .notifications-empty-state {
        min-height: 200px;
        padding: 2rem 1.5rem;
    }

    .illustration-container {
        width: 10rem !important;
        height: 10rem !important;
    }

    .empty-state-illustration img {
        width: 7rem;
        height: 7rem;
    }

    .notifications-empty-state h3 {
        font-size: 1.25rem;
    }
}

/* ================= IMAGE REQUIREMENTS MODAL ================= */
.image-requirements-modal {
    animation: modalFadeIn 0.3s ease-out;
}

.image-requirements-modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Face Upload Section Enhancements */
.face-upload-section {
    position: relative;
    z-index: 1;
}

/* Face Upload Disabled Message */
.face-upload-disabled-message {
    position: relative;
    overflow: hidden;
}

.face-upload-disabled-message::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    animation: disabledShimmer 3s linear infinite;
    pointer-events: none;
}

@keyframes disabledShimmer {
    0% {
        transform: translateX(-100%) skewX(-10deg);
    }
    100% {
        transform: translateX(200%) skewX(-10deg);
    }
}

.face-upload-section .upload-dropzone {
    position: relative;
    overflow: hidden;
}

.face-upload-section .upload-dropzone::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.face-upload-section .upload-dropzone:hover::before {
    opacity: 1;
    animation: shimmer 2s linear infinite;
}

/* Ensure the button container is visible */
.face-upload-section .mb-4 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 1rem !important;
    position: relative !important;
    z-index: 5 !important;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Image Requirements Button Enhancement */
.view-requirements-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%) !important;
    border: 2px solid rgba(59, 130, 246, 0.4) !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
    font-weight: 600 !important;
    letter-spacing: 0.025em !important;
    z-index: 10;
}

.view-requirements-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #7C3AED 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
    transform: translateY(-2px) scale(1.02) !important;
}

.view-requirements-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.view-requirements-btn:hover::before {
    opacity: 1;
    animation: buttonShimmer 1.5s linear infinite;
}

.view-requirements-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    pointer-events: none;
}

.view-requirements-btn:hover::after {
    transform: translateX(100%);
}

/* Ensure button is always visible */
.view-requirements-btn {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 48px !important;
}

@keyframes buttonShimmer {
    0% {
        transform: translateX(-100%) skewX(-10deg);
    }
    100% {
        transform: translateX(200%) skewX(-10deg);
    }
}

/* Focus state for accessibility */
.view-requirements-btn:focus {
    outline: 2px solid #60A5FA !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2) !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    .view-requirements-btn {
        font-size: 0.875rem !important;
        padding: 0.75rem 1rem !important;
        min-height: 44px !important;
    }
}

/* ================= REDESIGNED UPLOAD ZONE STYLES ================= */

/* Upload drop zone styling */
.upload-drop-zone {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
    /* Ensure drop zone can receive drop events */
    pointer-events: auto !important;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Ensure child elements don't interfere with drag events */
.upload-drop-zone * {
    pointer-events: none !important;
}

.upload-drop-zone::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        45deg,
        transparent 25%,
        rgba(147, 51, 234, 0.1) 25%,
        rgba(147, 51, 234, 0.1) 50%,
        transparent 50%,
        transparent 75%,
        rgba(147, 51, 234, 0.1) 75%
    );
    background-size: 20px 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none !important;
}

.upload-drop-zone:hover::before {
    opacity: 1;
}

.upload-drop-zone.dragover {
    border-color: #a855f7 !important;
    background-color: rgba(147, 51, 234, 0.1) !important;
}

.upload-drop-zone.dragover::before {
    opacity: 1;
    animation: uploadShimmer 1s linear infinite;
}

@keyframes uploadShimmer {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

/* Upload icon circle enhancement */
.upload-drop-zone .group:hover .w-12 {
    transform: scale(1.1);
    box-shadow: 0 4px 20px rgba(147, 51, 234, 0.4);
}

/* URL input wrapper styling - matches reference design */
.url-input-wrapper {
    transition: all 0.2s ease-in-out;
    position: relative;
}

.url-input-wrapper:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px);
}

.url-input-wrapper:focus-within {
    box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.3) !important;
}

/* Image Requirements Button (secondary styling) */
.image-requirements-btn {
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
}

.image-requirements-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(147, 51, 234, 0.1),
        transparent
    );
    transition: left 0.5s ease;
}

.image-requirements-btn:hover::before {
    left: 100%;
}

.image-requirements-btn:hover {
    transform: translateY(-1px);
}

/* Face upload section responsive fixes */
.face-upload-section {
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Ensure proper containment - removed fixed min-height for better responsiveness */
.face-upload-section.custom-face-image-block {
    contain: layout;
    min-height: auto; /* Allow natural height based on content */
    transition: all 0.3s ease-in-out;
}

/* When image is present, allow natural expansion */
.face-upload-section:has(#custom-face-image-preview-img),
.face-upload-section.has-preview {
    min-height: auto; /* Let content determine height */
}

/* Fallback for browsers without :has() support */
@supports not selector(:has(*)) {
    .face-upload-section.has-preview {
        min-height: auto;
    }
}

/* Tab navigation spacing */
.face-source-tab-navigation-container {
    padding: 0.25rem 0;
}

/* Preview section container */
.preview-section-container {
    will-change: opacity;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.face-upload-section .mt-3,
.face-upload-section .mt-4 {
    position: relative;
}

/* Ensure no border conflicts */
#custom-face-image-preview-block {
    border: none !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    overflow: visible !important; /* Changed from hidden to allow absolute positioned button to be visible */
    /* Enhanced layout for better spacing */
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 1.5rem !important; /* 24px gap between elements */
    padding: 1rem !important;
    /* Positioning context for absolute remove button */
    position: relative !important;
}

/* Ensure preview image is always a perfect circle */
#custom-face-image-preview-img {
    width: 130px !important; /* Increased size for better visibility */
    height: 130px !important; /* Same as width for perfect circle */
    border-radius: 50% !important; /* Perfect circle */
    object-fit: cover !important; /* Crop to fill circle properly */
    border: 3px solid #a855f7 !important; /* Slightly thicker purple border */
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.3), 0 2px 8px rgba(0, 0, 0, 0.2) !important; /* Enhanced shadow with purple glow */
    display: block !important;
    overflow: hidden !important;
    flex-shrink: 0 !important; /* Prevent shrinking */
    aspect-ratio: 1/1 !important; /* Force 1:1 aspect ratio */
}

/* Face preview label styling */
#custom-face-image-preview-label {
    align-self: flex-start !important;
    margin-bottom: -0.5rem !important; /* Reduce gap to image */
}

/* Compact trash icon button styling - absolute top-right overlay */
.custom-face-image-remove-btn,
#custom-face-image-remove-btn {
    /* Absolute positioning with custom offset */
    position: absolute !important;
    top: 48px !important; /* Custom top offset */
    right: 68px !important; /* Custom right offset */
    z-index: 10 !important; /* Ensure button appears above other elements */
    
    /* Remove previous centering and margin styles */
    margin: 0 !important;
    align-self: unset !important;
    
    /* Circular button styling with increased size */
    width: 32px !important; /* Increased from 24px */
    height: 32px !important; /* Increased from 24px */
    background-color: #dc2626 !important; /* red-600 */
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* Enhanced transitions and effects */
    opacity: 1 !important;
    transition: all 0.2s ease-in-out !important;
    cursor: pointer !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced hover state matching avatar delete behavior */
.custom-face-image-remove-btn:hover,
#custom-face-image-remove-btn:hover {
    background-color: #ef4444 !important; /* red-500 */
    transform: scale(1.05) !important; /* Removed translateY to prevent position shift */
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4) !important;
}

/* Focus state for accessibility */
.custom-face-image-remove-btn:focus,
#custom-face-image-remove-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(248, 113, 113, 0.5) !important; /* red-400 */
    background-color: #ef4444 !important; /* red-500 */
}

/* Active state */
.custom-face-image-remove-btn:active,
#custom-face-image-remove-btn:active {
    transform: scale(0.95) !important; /* Removed translateY to maintain position */
    box-shadow: 0 2px 6px rgba(220, 38, 38, 0.3) !important;
}

/* Tab content sections */
.upload-tab-content-section,
.url-tab-content-section {
    flex: 1;
}

/* Tab content wrapper */
.tab-content-wrapper {
    display: flex;
    flex-direction: column;
    padding-top: 0.25rem; /* Small padding for better visual spacing */
}

/* Responsive enhancements */
@media (max-width: 640px) {
    .upload-drop-zone {
        height: 4rem; /* Compact on mobile to prevent overflow */
    }
    
    .tab-content-wrapper {
        padding-top: 0.5rem; /* Slightly more padding on mobile for touch targets */
    }
    
    .face-upload-section.custom-face-image-block {
        min-height: auto; /* Natural height on mobile */
        padding: 0.75rem; /* Slightly less padding on mobile */
    }
    
    .face-upload-section:has(#custom-face-image-preview-img),
    .face-upload-section.has-preview {
        min-height: auto; /* Natural height expansion on mobile */
    }
    
    .url-input-wrapper {
        height: 2.75rem; /* More compact height on mobile */
        padding: 0 0.75rem; /* Adjust padding for mobile */
    }
    
    .url-input-wrapper input {
        font-size: 0.875rem; /* Slightly smaller text on mobile */
    }
    
    .image-requirements-btn {
        text-align: center;
        justify-content: center;
        padding: 0.5rem 0.75rem; /* More compact on mobile */
        font-size: 0.75rem; /* Smaller text on mobile */
    }
}

/* Responsive design for face preview block */
@media (max-width: 640px) {
    #custom-face-image-preview-block {
        gap: 1rem !important; /* Smaller gap on mobile */
        padding: 0.75rem !important; /* Less padding on mobile */
    }
    
    #custom-face-image-preview-img {
        width: 130px !important; /* Smaller image on mobile */
        height: 130px !important;
        border: 2px solid #a855f7 !important; /* Thinner border on mobile */
    }
    
    .custom-face-image-remove-btn,
    #custom-face-image-remove-btn {
        /* Maintain proportional size on mobile */
        width: 28px !important; /* Proportionally larger for mobile (was 22px) */
        height: 28px !important; /* Proportionally larger for mobile (was 22px) */
        /* Adjust positioning for mobile touch accessibility */
        top: 40px !important; /* Proportionally adjusted from desktop 48px */
        right: 56px !important; /* Proportionally adjusted from desktop 68px */
        margin: 0 !important; /* Remove any margin on mobile */
    }
    
    .custom-face-image-remove-btn .iconify,
    #custom-face-image-remove-btn .iconify {
        font-size: 13px !important; /* Slightly larger icon for the bigger button */
    }
}

/* Enhanced focus states for accessibility */
#custom-face-image-preview-img:focus,
#custom-face-image-remove-btn:focus {
    outline: 2px solid #a855f7 !important;
    outline-offset: 2px !important;
}

/* ================= TEXT SIZE BUTTON GROUP STYLES ================= */
.text-size-button-group {
    background: rgba(55, 65, 81, 0.5) !important;
    padding: 2px !important; /* Reduced padding for better fit */
    position: relative;
    isolation: isolate;
    border-radius: 12px !important;
    overflow: visible !important; /* Ensure content is not clipped */
    min-height: 40px !important; /* Proper height for 36px buttons + 2px padding */
    width: 100% !important; /* Ensure full width */
    display: flex !important; /* Explicit flexbox */
}

/* Animated pill */
.text-size-selector-pill {
    background: linear-gradient(135deg, #2563EB 0%, #3B82F6 100%) !important;
    border-radius: 10px !important; /* Match button group radius */
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    will-change: transform;
    pointer-events: none;
    position: absolute !important; /* Ensure proper positioning */
    top: 2px !important;
    bottom: 2px !important;
    left: 2px !important;
    width: calc(33.333% - 1px) !important; /* Precise width calculation */
}

/* Button styling */
.text-size-button {
    position: relative;
    z-index: 10;
    font-weight: 500 !important;
    transition: all 0.2s ease-in-out !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    border-radius: 10px !important; /* Match pill radius */
    min-height: 36px !important; /* Ensure minimum height */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 8px 12px !important; /* Adequate padding */
    line-height: 1.2 !important; /* Better line height */
    flex: 1 !important; /* Equal distribution */
    text-align: center !important; /* Center text */
    /* Safari-specific fixes for text rendering */
    -webkit-font-smoothing: antialiased !important;
    -webkit-text-size-adjust: 100% !important;
    -webkit-transform: translateZ(0) !important; /* Force hardware acceleration */
}

/* Button hover state when not selected */
.text-size-button:not([aria-pressed="true"]):hover {
    color: #D1D5DB !important;
    /* Safari-specific hover enhancement */
    -webkit-text-fill-color: #D1D5DB !important;
}

/* Selected state text - Enhanced for Safari */
.text-size-button[aria-pressed="true"] {
    color: #FFFFFF !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    /* Safari-specific fixes for active state text */
    -webkit-text-fill-color: #FFFFFF !important;
    -webkit-font-smoothing: antialiased !important;
    font-weight: 600 !important; /* Ensure text stands out in Safari */
}

/* Safari-specific fix for active state persistence */
@supports (-webkit-appearance: none) {
    .text-size-button[aria-pressed="true"] {
        /* Force Safari to recognize the active state */
        background: transparent !important;
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        /* Ensure text remains white on Safari */
        opacity: 1 !important;
        /* Force repaint to fix text rendering issues */
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
    }
    
    /* Additional Safari-specific hover fixes */
    .text-size-button[aria-pressed="true"]:hover {
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
    }
    
    /* Force focus state to work properly in Safari */
    .text-size-button[aria-pressed="true"]:focus {
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        outline: none !important;
    }
}

/* Webkit-specific fallback for active button text */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .text-size-button[aria-pressed="true"] {
        /* Force white text color for active buttons in WebKit browsers */
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        /* Prevent any background color interference */
        background-color: transparent !important;
        background-image: none !important;
        /* Force hardware acceleration for smoother rendering */
        -webkit-backface-visibility: hidden !important;
        -webkit-perspective: 1000 !important;
        -webkit-transform: translate3d(0, 0, 0) !important;
    }
    
    /* Ensure text remains visible during state changes */
    .text-size-button[aria-pressed="true"]:active,
    .text-size-button[aria-pressed="true"]:focus,
    .text-size-button[aria-pressed="true"]:hover {
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
    }
}

/* Remove focus outline */
.text-size-button:focus {
    outline: none !important;
}

.text-size-button:focus-visible {
    outline: none !important;
}

/* Remove default button styles on mobile */
.text-size-button:active {
    transform: none !important;
}

/* Ensure buttons don't have borders or outlines */
.text-size-button {
    outline: none !important;
    box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* Individual button border radius removed - using uniform radius instead */

/* Ensure Medium is prominently displayed - Enhanced for Safari */
#text-size-button-medium[aria-pressed="true"] {
    font-weight: 600 !important;
    /* Safari-specific fix for Medium button */
    color: #FFFFFF !important;
    -webkit-text-fill-color: #FFFFFF !important;
}

/* Safari-specific fix for all named button states */
@supports (-webkit-appearance: none) {
    #text-size-button-small[aria-pressed="true"],
    #text-size-button-medium[aria-pressed="true"],
    #text-size-button-large[aria-pressed="true"] {
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        font-weight: 600 !important;
        /* Force Safari to maintain active appearance */
        opacity: 1 !important;
        visibility: visible !important;
    }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    .text-size-button[aria-pressed="true"] {
        /* iOS Safari text rendering fix */
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        /* Prevent tap highlight interference */
        -webkit-tap-highlight-color: transparent !important;
        /* Force text to remain visible */
        text-rendering: optimizeLegibility !important;
        -webkit-font-smoothing: antialiased !important;
    }
}

/* Safari WebKit text stroke fix for better contrast */
@supports (-webkit-text-stroke: 1px) {
    .text-size-button[aria-pressed="true"] {
        /* Subtle text stroke for better visibility on Safari */
        -webkit-text-stroke: 0.5px rgba(255, 255, 255, 0.1) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 
                     0 0 1px rgba(255, 255, 255, 0.1) !important;
    }
}

/* Force Safari to use proper text rendering */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    .text-size-button {
        /* Force Safari to render text correctly */
        will-change: color, background-color !important;
        -webkit-text-rendering: optimizeLegibility !important;
        text-rendering: optimizeLegibility !important;
    }
    
    .text-size-button[aria-pressed="true"] {
        /* Force white text in Safari active state */
        color: white !important;
        -webkit-text-fill-color: white !important;
        /* Prevent any background bleed */
        background-clip: padding-box !important;
    }
}

/* Mobile adjustments */
@media (max-width: 640px) {
    .text-size-button-group {
        min-height: 40px !important;
        padding: 2px !important;
    }
    
    .text-size-button {
        font-size: 0.813rem !important;
        padding: 6px 10px !important;
        min-height: 34px !important;
        /* Enhanced Safari mobile fixes */
        -webkit-font-smoothing: antialiased !important;
        -webkit-text-size-adjust: 100% !important;
    }
    
    /* Mobile Safari active state fix */
    .text-size-button[aria-pressed="true"] {
        color: #FFFFFF !important;
        -webkit-text-fill-color: #FFFFFF !important;
        font-weight: 600 !important;
    }
}

/* ================= PRESET COLOR SWATCHES ================= */
.preset-color-swatches-container {
    margin-bottom: 1rem;
}

.preset-color-swatches-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.375rem;
    padding: 0.75rem 8px 0.75rem 0.75rem; /* Add right padding */
    margin-right: -8px; /* Negative margin to maintain alignment */
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 0.5rem;
    border: 1px solid rgba(75, 85, 99, 0.3);
    justify-content: space-between;
    overflow: visible !important;
}

.preset-color-swatch-wrapper {
    position: relative;
}

.preset-color-swatch {
    position: relative;
    transition: all 0.2s ease;
    transform: scale(1);
}

.preset-color-swatch:hover {
    transform: scale(1.1);
}

.preset-color-swatch:active {
    transform: scale(0.95);
}

.preset-color-swatch[aria-pressed="true"] {
    transform: scale(1.1);
}

/* Tooltip styling */
.preset-color-tooltip {
    animation: tooltipFadeIn 0.2s ease-out;
    white-space: nowrap;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -5px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* Make the gold gradient more visible */
.preset-color-swatch[aria-label*="Gold"] {
    background: linear-gradient(135deg, #FFD700 0%, #FFAA00 100%) !important;
}

/* Enhanced stroke indicator for black color */
.preset-color-swatch[aria-label*="Black"] span {
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.8);
}

/* Primary color picker row adjustments */
.primary-text-color-picker-row {
    padding-top: 0.5rem;
    border-top: 1px solid rgba(75, 85, 99, 0.3);
}

/* ================= GRADIENT TEXT OVERLAY ENHANCEMENTS ================= */

/* Enhanced Color Picker Styling */
.enhanced-color-picker-container {
    /* Override default color picker styles for dark mode */
    --cp-bg-color: #1f2937;
    --cp-border-color: #374151;
    --cp-input-color: #f9fafb;
    --cp-button-hover-color: #6366f1;
}

/* Make color swatches 25% smaller and adjust spacing */
.enhanced-color-picker-container .rbgcp-swatch {
    width: 18px !important;
    height: 18px !important;
    margin: 3px !important;
    border-radius: 50% !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.enhanced-color-picker-container .rbgcp-swatch:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.enhanced-color-picker-container .rbgcp-swatch.active {
    transform: scale(1.1) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9), 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

/* Hide unnecessary elements from the color picker */
.enhanced-color-picker-container .rbgcp-eyedropper,
.enhanced-color-picker-container .rbgcp-inputs,
.enhanced-color-picker-container .rbgcp-presets,
.enhanced-color-picker-container .rbgcp-gradient,
.enhanced-color-picker-container .rbgcp-color-guide,
.enhanced-color-picker-container .rbgcp-input-type,
.enhanced-color-picker-container .rbgcp-color-type-btns,
.enhanced-color-picker-container .rbgcp-advanced-sliders,
.enhanced-color-picker-container .rbgcp-gradient-type,
.enhanced-color-picker-container .rbgcp-gradient-angle,
.enhanced-color-picker-container .rbgcp-gradient-stop,
.enhanced-color-picker-container .rbgcp-gradient-controls {
    display: none !important;
}

/* Ensure swatches container is properly aligned */
.enhanced-color-picker-container .rbgcp-swatches {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 6px !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 8px 0 !important;
}

/* Enhanced gradient text rendering for live preview */
.text-style-preview-gradient-line {
    /* Ensure gradient text is always crisp and readable */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    
    /* Prevent text selection in preview */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    /* Ensure proper gradient clipping */
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    
    /* Fallback for browsers that don't support gradient text */
    color: transparent;
}

/* Enhanced gradient text for thumbnail preview */
.gradient-text-enhanced {
    /* Crisp text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    
    /* Proper gradient clipping */
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    
    /* Enhanced drop shadow for better contrast */
    filter: drop-shadow(0 3px 6px rgba(0,0,0,0.4)) !important;
    
    /* Ensure text doesn't break on small screens */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Fallback for browsers that don't support gradient text */
@supports not (-webkit-background-clip: text) {
    .text-style-preview-gradient-line,
    .gradient-text-enhanced {
        background: none !important;
        -webkit-text-fill-color: unset !important;
        color: var(--primary-text-color, #F0D000) !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5) !important;
    }
}

/* Color-specific stroke support for enhanced contrast */
.gradient-text-enhanced[data-stroke="white"] {
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.8);
    text-stroke: 1px rgba(255, 255, 255, 0.8);
}

.gradient-text-enhanced[data-stroke="black"] {
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.8);
    text-stroke: 1px rgba(0, 0, 0, 0.8);
}

/* Responsive text sizing for gradient text */
@media (max-width: 768px) {
    .text-style-preview-gradient-line {
        font-size: clamp(1rem, 4vw, 1.8rem) !important;
    }
    
    .gradient-text-enhanced {
        font-size: clamp(2rem, 8vw, 3.5rem) !important;
    }
}

/* High DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .text-style-preview-gradient-line,
    .gradient-text-enhanced {
        -webkit-font-smoothing: subpixel-antialiased;
    }
}

/* Responsive design for smaller screens */
@media (max-width: 640px) {
    .preset-color-swatches-row {
        gap: 0.25rem;
        padding: 0.5rem;
    }
    
    .preset-color-swatch {
        width: 28px;
        height: 28px;
    }
    
    .preset-color-tooltip {
        font-size: 11px;
        min-width: 120px !important;
    }
}

/* Enhanced focus states for accessibility */
.preset-color-swatch:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.5);
}

.preset-color-swatch:focus-visible {
    outline: 2px solid #A78BFA;
    outline-offset: 2px;
}

/* ================= URL SET BUTTON ATTENTION ================= */
@keyframes buttonAttention {
    0% {
        transform: scale(1);
        box-shadow: 0 0 12px rgba(147, 51, 234, 0.4);
    }
    50% {
        transform: scale(1.08);
        box-shadow: 0 0 20px rgba(147, 51, 234, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 12px rgba(147, 51, 234, 0.4);
    }
}

/* Enhanced focus states for URL input */
.url-input-wrapper:has(input:focus) {
    box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.3);
}

.url-input-wrapper input:focus + button[id="custom-face-image-url-set-btn"] {
    animation: buttonAttention 0.5s ease-out;
}

/* Pulse animation for when URL is present */
@keyframes pulseOnce {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.animate-pulse-once {
    animation: pulseOnce 1s ease-in-out;
}

/* ================= TOGGLE FOCUS RING FIX ================= */

/* Ensure toggle switches have proper spacing to prevent focus ring clipping */
.toggle-switch {
    margin: 4px 8px 4px 0 !important; /* Add margin to prevent right-edge clipping */
}

/* Add padding to toggle containers to ensure focus rings are visible */
.toggle-container {
    padding: 4px 8px 4px 0;
    margin: 0 -8px 0 0;
}

/* Specific fix for control sections with toggles */
.control-section-with-toggle {
    padding-right: 8px;
    margin-right: -8px;
}

/* Ensure Basic Controls section and other collapsible sections don't clip toggles */
.collapsible-section {
    overflow: visible !important;
    padding:.45rem 0.45rem;
}

/* Fix for text overlay and other toggle sections */
.toggle-section {
    padding: 4px 8px 4px 0;
    margin: 0 -8px 0 0;
    overflow: visible !important;
}

/* Ensure color swatches don't get clipped either */
.preset-color-swatches-row {
    padding: 0.75rem 8px 0.75rem 0.75rem; /* Add right padding */
    margin-right: -8px; /* Negative margin to maintain alignment */
    overflow: visible !important;
}

/* Fix for any focus rings that might extend beyond container */
*:focus,
*:focus-visible {
    position: relative;
    z-index: 100;
}

/* Ensure focus outlines are not clipped by any parent containers */
.left-sidebar *:focus {
    overflow: visible !important;
}

/* Additional safety for focus rings */
button:focus,
input:focus,
[role="switch"]:focus {
    transform: translateZ(0); /* Force hardware acceleration */
    position: relative;
    z-index: 50;
}

/* Enhanced dark scrollbar for design controls - Always visible */
.design-controls-scrollable {
  overflow-y: auto;
  scroll-behavior: smooth;
  max-height: calc(100vh - 12rem);
  /* Firefox scrollbar styling */
  scrollbar-width: auto;
  scrollbar-color: #4B5563 #374151;
}

/* Webkit scrollbar styling - Enhanced visibility */
.design-controls-scrollable::-webkit-scrollbar {
  width: 16px;
  background: #374151;
  border-radius: 6px;
}

.design-controls-scrollable::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 6px;
  margin: 4px 0;
}

.design-controls-scrollable::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 6px;
  border: 2px solid #374151;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  min-height: 40px;
}

.design-controls-scrollable::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.design-controls-scrollable::-webkit-scrollbar-thumb:active {
  background: #374151;
}

/* ================= OPENMOJI EMOJI STYLES ================= */

/* OpenMoji SVG image container styling - Optimized for crisp rendering */
.mood-expression-picker-grid img,
.gender-selector-section img {
    /* Force high-quality SVG rendering for ALL emoji */
    image-rendering: optimizeQuality !important;
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: -webkit-crisp-edges !important;
    image-rendering: crisp-edges !important;
    
    /* Prevent any unwanted smoothing or blurring */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* Ensure proper scaling behavior */
    object-fit: contain;
    object-position: center;
    
    /* Prevent user interactions that might cause quality issues */
    -webkit-user-select: none;
    user-select: none;
    pointer-events: none;
    
    /* Smooth transitions without affecting quality */
    transition: transform 0.2s ease, filter 0.2s ease;
    
    /* Force hardware acceleration for smooth animations */
    transform: translateZ(0);
    will-change: transform, filter;
    
    /* Ensure proper vector rendering */
    vector-effect: non-scaling-stroke;
}

/* Hover effect for emoji buttons - Maintain crisp quality during scaling */
.mood-expression-picker-grid button:hover img,
.gender-selector-section button:hover img {
    transform: translateZ(0) scale(1.1);
    image-rendering: optimizeQuality;
}

/* Active/pressed state for emoji images - Enhance without quality loss */
.mood-expression-picker-grid button[aria-pressed="true"] img,
.gender-selector-section button[aria-checked="true"] img {
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
    transform: translateZ(0) scale(1.02);
}

/* Loading state for OpenMoji images - Subtle background for SVG loading */
.mood-expression-picker-grid img[src*="openmoji"],
.gender-selector-section img[src*="openmoji"],
.mood-expression-picker-grid img[src*="jsdelivr"],
.gender-selector-section img[src*="jsdelivr"] {
    background: radial-gradient(circle, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    border-radius: 4px;
    /* Ensure SVG is treated as vector, not raster */
    image-rendering: optimizeQuality;
}

/* Error state - hide broken images and fallback gracefully */
.mood-expression-picker-grid img:not([src]),
.gender-selector-section img:not([src]),
.mood-expression-picker-grid img[src=""],
.gender-selector-section img[src=""] {
    display: none !important;
}

/* Ensure consistent sizing across different OpenMoji - Maintain aspect ratio */
.mood-expression-picker-section img {
    width: 40px !important;
    height: 40px !important;
    max-width: 40px !important;
    max-height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    /* Force vector rendering at exact dimensions */
    image-rendering: optimizeQuality;
}

.gender-selector-section img {
    width: 32px !important;
    height: 32px !important;
    max-width: 32px !important;
    max-height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    /* Force vector rendering at exact dimensions */
    image-rendering: optimizeQuality;
}

/* Accessibility - ensure focus is visible on buttons, not images */
.mood-expression-picker-grid button:focus-visible,
.gender-selector-section button:focus-visible {
    outline: 2px solid #A855F7;
    outline-offset: 2px;
}

/* Preload animation for OpenMoji images - Smooth appearance without quality loss */
@keyframes openmojiLoad {
    0% {
        opacity: 0;
        transform: translateZ(0) scale(0.9);
        filter: blur(0.5px);
    }
    100% {
        opacity: 1;
        transform: translateZ(0) scale(1);
        filter: blur(0);
    }
}

.mood-expression-picker-grid img,
.gender-selector-section img {
    animation: openmojiLoad 0.3s ease-out;
}

/* Ensure no system emoji fonts are used anywhere */
.mood-expression-picker-grid,
.gender-selector-section,
.mood-expression-picker-grid *,
.gender-selector-section * {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Hide any unicode emoji that might appear as fallback */
.mood-expression-picker-grid span[aria-hidden="true"],
.gender-selector-section span[aria-hidden="true"] {
    font-family: monospace !important; /* Force non-emoji font */
    color: transparent !important;
}

/* Mobile optimizations for OpenMoji - Maintain crisp rendering on smaller screens */
@media (max-width: 640px) {
    .mood-expression-picker-section img {
        width: 35px !important;
        height: 35px !important;
        max-width: 35px !important;
        max-height: 35px !important;
        min-width: 35px !important;
        min-height: 35px !important;
        /* Maintain quality on mobile */
        image-rendering: optimizeQuality;
    }
    
    .gender-selector-section img {
        width: 28px !important;
        height: 28px !important;
        max-width: 28px !important;
        max-height: 28px !important;
        min-width: 28px !important;
        min-height: 28px !important;
        /* Maintain quality on mobile */
        image-rendering: optimizeQuality;
    }
}

/* Dark mode optimization for OpenMoji SVGs - Enhance visibility without quality loss */
@media (prefers-color-scheme: dark) {
    .mood-expression-picker-grid img,
    .gender-selector-section img {
        filter: drop-shadow(0 0 1px rgba(255, 255, 255, 0.1)) contrast(1.02);
    }
}

/* High DPI / Retina display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mood-expression-picker-grid img,
    .gender-selector-section img {
        /* Force crisp rendering on high DPI displays */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Force SVG content type interpretation */
.mood-expression-picker-grid img[src$=".svg"],
.gender-selector-section img[src$=".svg"] {
    /* Explicitly treat as vector graphics */
    image-rendering: optimizeQuality !important;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* Prevent any browser-level image compression */
.mood-expression-picker-grid img,
.gender-selector-section img {
    /* Disable browser image optimization that might convert SVG to raster */
    image-orientation: from-image;
    image-resolution: from-image;
}

/* ================= TEXT OVERLAY TEXTAREA ANIMATIONS ================= */

/* Text overlay textarea smooth entry animation */
@keyframes textareaSlideIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Text overlay textarea smooth exit animation */
@keyframes textareaSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-4px);
  }
}

/* Text Overlay Refresh Button Styles */
.text-overlay-action-buttons {
    display: flex;
    align-items: center;
    gap: 0.25rem; /* 4px gap between buttons */
}

.text-overlay-refresh-button {
    /* Enhanced styling to match prompt action buttons */
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    min-height: 28px;
    font-size: 1.1em;
    opacity: 1;
    transform: translateY(0px);
}

.text-overlay-refresh-button:hover {
    /* Enhanced hover effects matching prompt buttons */
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.text-overlay-refresh-button:focus {
    outline: none;
    ring: 2px;
    ring-color: #8b5cf6; /* purple-400 */
    ring-offset: 2px;
    ring-offset-color: transparent;
}

.text-overlay-refresh-button:active {
    transform: scale(0.95);
}

.text-overlay-refresh-button .refresh-icon {
    font-size: 1em;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: rotate(0deg);
    display: inline-flex;
    align-items: center;
    width: auto;
    overflow: visible;
}

.text-overlay-refresh-button:hover .refresh-icon {
    transform: rotate(90deg); /* Slight rotation on hover for visual feedback */
    color: #ffffff; /* white */
}

/* Enhanced tooltip styles for text overlay action buttons */
#refreshTextOverlayTooltip,
#editTextOverlayTooltip {
    /* Match the exact styling of prompt action button tooltips */
    backdrop-filter: blur(8px);
    background-color: rgba(31, 41, 55, 0.95) !important; /* gray-900 with opacity */
    border: 1px solid rgba(75, 85, 99, 0.8) !important; /* gray-600 border */
    /* box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.25),
        0 4px 12px rgba(0, 0, 0, 0.15) !important; */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', system-ui, sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: -0.01em !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;

}

/* Compact tooltip sizing - 15% smaller than standard tooltips */
.text-overlay-tooltip-compact {
    font-size: 0.75rem !important; /* 15% smaller than 12px */
    padding: 8.5px 12.75px !important; /* 15% smaller than 10px 15px */
    min-width: 102px !important; /* 15% smaller than 120px */
    border-radius: 5.1px !important; /* 15% smaller than 6px */
    line-height: 1.3 !important; /* Slightly tighter line height for compact size */
}

/* Specific positioning for refresh button tooltip */
#refreshTextOverlayTooltip {
    /* Enhanced positioning to prevent edge cutting */
    /* The inline style overrides the transform for precise positioning */
}

/* Specific positioning for edit button tooltip */
#editTextOverlayTooltip {
    /* Right-aligned tooltip for edit button */
    transform: translateX(0);
}

/* Enhanced animation for text overlay tooltips */
.text-overlay-action-buttons .group:hover #refreshTextOverlayTooltip,
.text-overlay-action-buttons .group:hover #editTextOverlayTooltip {
    animation: tooltipFadeIn 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Ensure buttons are properly aligned in the header */
.text-overlay-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem; /* 8px */
}

/* This section is now replaced by the consolidated mobile responsive section below */

/* Dark mode compatibility (already using dark colors, but ensuring consistency) */
@media (prefers-color-scheme: dark) {
    .text-overlay-refresh-button {
        color: #9ca3af; /* gray-400 */
    }
    
    .text-overlay-refresh-button:hover {
        color: #ffffff; /* white */
        background: rgba(139, 92, 246, 0.15);
    }
}

/* High contrast mode accessibility */
@media (prefers-contrast: high) {
    .text-overlay-refresh-button {
        border: 1px solid currentColor;
    }
    
    .text-overlay-refresh-button:hover {
        color: #ffffff; /* white */
        background: rgba(139, 92, 246, 0.2);
    }
    
    #refreshTextOverlayTooltip,
    #editTextOverlayTooltip {
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        background-color: rgba(0, 0, 0, 0.9) !important;
    }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
    .text-overlay-refresh-button,
    .text-overlay-refresh-button .refresh-icon {
        transition: none;
    }
    
    .text-overlay-refresh-button:hover {
        color: #ffffff; /* white */
        transform: none;
    }
    
    .text-overlay-refresh-button:hover .refresh-icon {
        transform: none;
    }
    
    #refreshTextOverlayTooltip,
    #editTextOverlayTooltip {
        transition: opacity 0.1s ease;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .text-overlay-refresh-button {
        min-width: 24px;
        min-height: 24px;
        font-size: 1em;
    }
    
    .text-overlay-action-buttons {
        gap: 0.125rem; /* 2px gap on mobile */
    }
    
    /* Compact tooltips on mobile - even smaller for better fit */
    .text-overlay-tooltip-compact {
        font-size: 9.35px !important; /* 15% smaller than 11px mobile base */
        padding: 6.8px 10.2px !important; /* 15% smaller than 8px 12px mobile base */
        min-width: 85px !important; /* 15% smaller than 100px mobile base */
        border-radius: 4.25px !important; /* 15% smaller than 5px mobile base */
    }
    
    /* Refresh button tooltip positioning adjustment for mobile */
    #refreshTextOverlayTooltip {
        margin-left: -6px !important; /* Slightly more offset on mobile */
    }
}

/* ================= OVERLAY TEXT REFRESH LOADING FEEDBACK ================= */

/* ================= OVERLAY TEXT REFRESH LOADING EFFECT ================= */

/* Loading state for overlay text preview */
.overlay-text-loading {
    position: relative;
    overflow: hidden;
}

/* Smooth transition for loading overlay */
.overlay-text-loading .text-overlay-preview-line {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading overlay animations */
.overlay-text-loading .absolute.inset-0 {
    animation: fadeInBlur 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    background: rgba(17, 24, 39, 0.85); /* Darker overlay */
}

@keyframes fadeInBlur {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(6px);
        -webkit-backdrop-filter: blur(6px);
    }
}

/* Enhanced white spinner for loading overlay */
.overlay-text-loading .animate-spin {
    animation: spin 1s linear infinite;
    border-color: rgba(75, 85, 99, 0.3); /* Gray base */
    border-top-color: #ffffff; /* White top */
    border-width: 4px;
    width: 40px;
    height: 40px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Accessibility - respect reduced motion */
@media (prefers-reduced-motion: reduce) {
    .overlay-text-loading .absolute.inset-0 {
        animation: none;
        opacity: 1;
    }
    
    .overlay-text-loading .animate-spin {
        animation: none;
        border-top-color: #ffffff;
        border-right-color: #ffffff;
    }
}


