# ✨ Shiny & Glowing Text Overlay – Prompt Enhancement

## Objective
Ensure all generated YouTube thumbnails feature a text overlay that is visually striking, shiny, and glows with depth and realism.

---

## 📝 Prompt Rules

**Always include:**
1. “Add a bold, uppercase title in a modern sans-serif font.”
2. “Apply a **shiny, metallic or glossy finish** to the text, so it reflects light and appears premium.”
3. “Add a **strong, colorful outer glow** (e.g., neon, gold, or white) to make the text pop against any background.”
4. “Apply a **3D effect or subtle bevel/emboss** for depth and realism.”
5. “Use high-contrast color combinations for maximum readability and click appeal.”
6. “Ensure the glow and shine are visible on both light and dark backgrounds.”
7. “Keep the text overlay fully inside the safe zone, never clipped or touching the edge.”

---

## 💬 Example Prompt Fragment

> Add a bold, uppercase title in a modern sans-serif font. Make the text shiny and metallic, with a strong, colorful outer glow (such as neon, gold, or white). Apply a 3D effect or subtle bevel for depth. Use high-contrast colors and ensure the glow and shine are visible on any background. Place the text fully inside the safe zone, never clipped.

---

## 🧪 Testing Checklist

- [ ] Text overlay is shiny and appears to reflect light
- [ ] Strong, colorful outer glow is visible
- [ ] Text has a sense of depth (3D or bevel/emboss)
- [ ] Overlay is bold, modern, and highly readable
- [ ] No text is clipped or too close to the edge