# GPT-Image-1 Thumbnail Prompt – No-Cropping, Top-Right Text

## Purpose  
Guarantee that all generated thumbnails respect safe-zone rules, place overlay text in the **top-right**, and completely omit text if `textOverlay` is disabled.

## Usage  
Replace the bracketed section below with your own subject description and optional overlay text.

```plaintext
Create a cinematic YouTube thumbnail image at 1280×720.

Subject
• {YOUR_SCENE_DESCRIPTION}  
• Do not add any text unless explicitly instructed in “Text Overlay”.

Text Overlay  ➜ ONLY IF textOverlay = true
• Add the exact uppercase title: “{OVERLAY_TEXT}”.  
• Font: modern sans-serif, bold, highly legible.  
• Color: bright yellow (#FFD500) with black outline & soft glow.  
• Placement: top-right. Keep entire text block at least 64 px from every edge.  
• Auto-wrap to multiple lines if needed, but never exceed 35 % of image width.  
• No additional words, numbers, or punctuation.

When textOverlay = false  
• **Strictly remove all words, letters, numbers, captions, watermarks, or hidden text layers**.  
• Keep all icons, shapes, and background elements exactly as described.

Background & FX
• Deep emerald green gradient with soft vignette (luxury tone).  
• Add cinematic depth-of-field, rich contrast, and subtle particles of light.

Output
• 1280×720 PNG, high sharpness & saturation.  
• Ensure safe-zone compliance on both mobile & desktop.
```

---

Apply this prompt in your generator and you should see:

• Overlay text locked to the top-right.  
• No unintended text when the overlay toggle is off.  
• Icons or shapes remain unaffected.

If you still notice errant text, try reinforcing the **“When textOverlay = false”** clause or lowering the model’s creative temperature.