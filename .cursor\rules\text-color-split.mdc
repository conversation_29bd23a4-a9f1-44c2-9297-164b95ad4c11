---
description: 
globs: 
alwaysApply: false
---
# text-color-split
# Description
Ensures consistent, accessible application of primary and secondary text colors in overlay previews.

# Type: always

# Applies to:
- /src/components/ControlPanel.jsx
- /src/components/ThumbnailPreview.jsx
- /src/components/PromptBuilder.jsx

---

## 🎨 Text Overlay Color Split Rule

### Objective
Guarantee that overlay text in both the sidebar live preview and the main thumbnail preview uses the **primary color** for the first line and the **secondary color** for all subsequent lines, for a visually clear and consistent YouTube thumbnail experience.

---

## Implementation Details

- **Line Splitting:**  
  - Split the overlay text by line breaks (`\\n`).
  - The **first line** is rendered with the primary text color.
  - All **subsequent lines** are rendered with the secondary text color.

- **Preview Consistency:**  
  - This logic must be applied in:
    - The “Text Color & Typography” live preview box in the sidebar.
    - The main thumbnail preview panel.
    - Any other overlay text preview (e.g., PromptBuilder).

- **Fallback:**  
  - If only one line is present, use the primary color for that line.

- **Accessibility:**  
  - Ensure both colors meet WCAG contrast standards against the background.
  - Do not allow color combinations that fail accessibility checks.

- **UX/UI:**  
  - The color split should be visually clear and match the final thumbnail output.
  - If more than two lines, all after the first use the secondary color.

- **Big CAPS:**
  - All overlay text must be rendered in uppercase.
  - Fade-Smooth Gradient: All overlay text (in both previews and final output) must use a smooth gradient color 
  - fill, blending from the primary color (start) to the secondary color (end), with a fade effect.
  - All previous requirements (color split by line, accessibility, etc.) are preserved.

**Result:**
- “WORTH” appears in BIG CAPS with a gradient from primary to secondary color.
- “THE HYPE” appears in BIG CAPS with the same gradient (or, if fallback, in secondary color).

---


**Result:**
- “Worth” appears in the primary color.
- “The Hype” appears in the secondary color.

---

## Enforcement

- This rule is **always** active and must be reflected in all overlay text previews and the final thumbnail output.
- Any future changes to overlay text rendering must respect this color split logic.

---

# ✅ Ready to implement and enforce in all relevant components.