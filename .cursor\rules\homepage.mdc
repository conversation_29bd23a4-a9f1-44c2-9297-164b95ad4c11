---
description: 
globs: 
alwaysApply: false
---
# AI YouTube Thumbnail Generator

```prompt
Create a visually striking YouTube thumbnail that maximizes click-through rate (CTR) for the following video topic:

**Video Title**: [Insert title or choose from suggestions below]
**Category**: 🎮 Gaming | 📱 Tech | 🎓 Education | 🎥 Vlogging | 😂 Reactions
**Style**: Bold and attention-grabbing (e.g., shocked faces, neon text, dramatic lighting)

### Key Elements to Include:
1. **Focal Point**: 
   - A high-energy subject (e.g., person with exaggerated expression, trending gadget, game character)
   - Use the "rule of thirds" for balanced composition

2. **Text Overlay**:
   - 3-5 impactful words in bold, contrasting font
   - Examples: "EPIC FAIL!", "MIND BLOWN", "YOU WON'T BELIEVE"

3. **Color Scheme**:
   - Vibrant colors with high contrast (e.g., yellow text on dark background)
   - Gradient backgrounds recommended for depth

4. **Branding** (optional):
   - Consistent logo/channel name placement
   - Custom color palette (provide hex codes if needed)

### Popular Title Formulas (Editable):
🔥 Trending: 
- "I Tried [VIRAL TREND] For 24 Hours (SHOCKING RESULTS!)"
- "This [COMMON THING] Will BLOW YOUR MIND!"

🎮 Gaming:
- "[GAME] BROKE ME... (EMOTIONAL ENDING)"
- "SECRET HACK Every [GAME] Player Needs"

📱 Tech:
- "[NEW GADGET] Review: Worth the Hype?"
- "Why [PRODUCT] is a TOTAL SCAM"

### Output Specifications:
- Aspect Ratio: 16:9 (1280x720px minimum)
- File Type: PNG with transparent background option


style 
/* Visual hierarchy suggestions */
- Title text: 90-120px font size
- Facial expressions: Capture peak emotion
- Negative space: Keep 20% clean for YouTube UI elements

Pro Tip: Use power words like "SECRET", "SHOCKING", or "HACK" in your title/text overlay for higher CTR. Pair with matching visuals (e.g., shocked face for "SHOCKING").

This .mdc file provides:

A structured prompt for consistent AI output

Editable title templates for quick customization

Visual best practices without technical jargon

Platform-specific specs (YouTube dimensions/ratios)

CTR-boosting tips based on proven thumbnail psychology

Would you like me to add a section for A/B testing notes or viral trend references?