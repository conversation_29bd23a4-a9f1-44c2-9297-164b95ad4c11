---
description: 
globs: 
alwaysApply: false
---
---
title: user-driven-thumbnail-customization
id: user-driven-thumbnail-customization.mdc
ruleType: manual
---

## Feature: User-Driven Thumbnail Customization Suite

### Objective
Enhance the thumbnail generator MVP by empowering users with advanced customization options for personalization, branding, and expression — directly influencing fonts, icons, colors, gender representation, facial presence, and mood emojis.

This feature should flexibly adapt based on the selected **template type or mode**, such as "YouTube Reaction Thumbnail," "App Tutorial Promo," or "Podcast Episode Cover." Each mode can expose relevant controls only, simplifying the UX.

---

## Applies To
- /src/components/ThumbnailControls.jsx
- /src/components/PromptBuilder.js
- /src/state/userPreferences.ts
- /src/templates/
- /public/assets/icons/

---

## Feature Rules

### 🎨 Font Selection
- Display a shortlist dropdown of 4–6 web-safe or Tailwind-compatible fonts.
- Default: **Bold Sans-Serif**
- On selection change, font family is applied to `text_overlay.style.font`

### 🖼️ Icon/Logo Replacement
- Add an image upload field and/or text input:
  - **Input field**: If user types `Google`, `Apple`, `Amazon`, auto-assign icon from `/assets/icons/`
  - **Upload field**: If custom file uploaded, embed icon as `object_of_focus.icon_url`
- Replace default indicator icon in prompt output.

### 🖌️ Text Color Picker
- Add dual color pickers (Primary + Secondary)
- Values update `text_overlay.style.color` array (e.g., `[#FFD700, #FFFFFF]`)

### 🚻 Gender Selector
- Dropdown with: `Auto`, `Male`, `Female`,
- Maps directly to `subject.person.appearance.gender`

### 😄 Emoji Mood Reaction
- Use previously built Mood Picker UI (emojis mapped to prompt presets)
- Affects both facial expression and ambient color energy if person is included

### 🧑 Upload Your Own Face
- File upload option for headshot image
- Image URL embedded into prompt as `subject.person.custom_face_url`
- Optional cropping or face-align tool recommended (external lib OK)

---

## 🧠 Template-Adaptive Customization
- Users can browse or search a list of premade **template categories** (e.g., "App Promo," "Tech Review," "Cringe Reaction")
- Each template defines default layout, font, color palette, and icon slots
- When a user selects a template:
  - The customization UI **respects the design boundaries** of that template
  - User can override only the allowed properties (e.g., title text, emoji, or gender)
  - Locked elements (e.g., composition/framing) remain fixed unless developer enables them

---

## Cursor Chat Integration
To activate this customization rule in chat cascade:
```mdc
@user-driven-thumbnail-customization
```

> Note: Developers must respect fallback logic if user inputs are missing. Auto-suggestions or defaults should remain functional.

---

## Notes for Developers
- Ensure uploaded images/icons are safely processed, and ideally previewed.
- Store user selections in localStorage or state to persist between sessions.
- If user uploads a face, toggle off default AI face generation and show their own.
- Emojis must stay additive — never block other UX controls.

## Bonus UX Tip 💡
Group related customization panels inside collapsible sections or tabs (e.g. “Fonts & Text”, “Branding”, “Face & Mood”) to keep the interface tidy and scalable.
Show or hide control groups dynamically based on the selected template or thumbnail mode.

---

✅ Ready for rule activation.
To apply, drop this .mdc into your active Cursor workspace and toggle in Chat Cascade when needed.
