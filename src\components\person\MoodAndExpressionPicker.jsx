import React from 'react'
import { moodEmojiMapping, getOpenMojiUrl } from '../../utils/openmojiMapping.js';

// Helper to get PNG URL from OpenMoji CDN
const getOpenMojiPngUrl = (hexCode) =>
  `https://cdn.jsdelivr.net/gh/hfg-gmuend/openmoji@15/color/72x72/${hexCode}.png`;

export const MoodAndExpressionPicker = ({
  selectedExpression,
  setSelectedExpression
}) => {
  const expressionOptions = Object.entries(moodEmojiMapping).map(([key, data]) => ({
    value: key,
    ...data
  }));

  const handleExpressionClick = (newValue) => {
    setSelectedExpression(newValue);
  };

  const handleExpressionKeyDown = (e, newValue) => {
    if (e.key === 'Enter' || e.key === ' ') {
      setSelectedExpression(newValue);
      e.preventDefault();
    }
  };

  const buttons = expressionOptions.map(option => {
    const isSelected = selectedExpression === option.value;
    
    // ALL emoji use SVG with optimal quality settings
    const imgProps = {
      src: getOpenMojiUrl(option.openmojiHex),
      alt: option.ariaLabel,
      className: 'w-10 h-10 object-contain',
      loading: 'lazy',
      draggable: false,
      decoding: 'async',
      fetchpriority: 'low',
      style: {
        imageRendering: 'optimizeQuality',
        WebkitUserSelect: 'none',
        userSelect: 'none',
        // Force crisp SVG rendering for ALL emoji
        WebkitImageRendering: '-webkit-optimize-contrast',
        MozImageRendering: 'crisp-edges'
      },
      onError: (e) => {
        console.error(`Failed to load OpenMoji SVG for ${option.label}`);
        e.target.style.display = 'none';
        e.target.parentElement.insertAdjacentHTML('afterbegin', `<span class="text-3xl" aria-hidden="true">${option.unicode}</span>`);
      },
      onLoad: (e) => {
        console.log(`✅ OpenMoji SVG loaded: ${option.label} (${isSelected ? 'SELECTED' : 'unselected'})`);
      }
    };

    return React.createElement('button', {
      key: option.value,
      type: 'button',
      onClick: () => handleExpressionClick(option.value),
      onKeyDown: (e) => handleExpressionKeyDown(e, option.value),
      title: option.label,
      className: `flex flex-col items-center justify-center p-2 rounded-lg border-2 text-center ${isSelected ? 'border-purple-500 bg-purple-700' : 'border-gray-600 bg-gray-700 hover:border-purple-400'} transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 w-full`,
      id: `expression-btn-${option.value.toLowerCase().replace(/\s+/g, '-')}`,
      'aria-label': `${option.ariaLabel} - ${option.label}`,
      'aria-pressed': isSelected
    },
      React.createElement('img', imgProps),
      React.createElement('span', { 
        className: `block text-xs mt-1 text-gray-300 ${isSelected ? 'font-bold' : ''}` 
      }, option.label)
    );
  });

  return React.createElement('div', { className: 'mood-expression-picker-section py-2', id: 'mood-expression-picker-section' },
    React.createElement('label', { className: 'mood-expression-picker-label block text-sm font-medium text-gray-300 mb-2' }, 'Mood & Expression:'),
    React.createElement('div', { className: 'mood-expression-picker-grid grid grid-cols-3 sm:grid-cols-3 gap-2' }, buttons)
  );
}; 