---
title: Upload or Import Face Image
id: face-upload-or-import.md
---

## 🎯 Feature: Toggle Upload / Import Face Image (for Headshot Replacement)

### Objective
Allow users to either **upload a personal image** (from local device) or **import an image from a URL** to use as their face/headshot in the thumbnail. This gives flexibility to creators who prefer hosting their image externally or using existing profile images.

---

## 🧠 UX Design Best Practices

### Toggle Label:
**“Choose Image Source”**  
- Two radio buttons or segmented toggle options:
  - `🖼 Upload from Device`
  - `🌐 Import via URL`

---

### Upload Flow (`🖼 Upload`)
- Drag-and-drop area or standard file selector
- Accepts: `.jpg`, `.jpeg`, `.png`
- Max file size: 2MB
- Display:
  - File name + “Remove” option
  - Live circular preview of uploaded headshot

### Import Flow (`🌐 URL`)
- Input field with:
  - Label: “Paste Image URL”
  - Placeholder: `https://example.com/my-face.jpg`
  - Validation: Show error if URL doesn’t end in `.jpg` or `.png`
- On blur or paste:
  - Live preview of image from URL
  - "Remove" option to clear

---

### 📤 Shared Behavior
- Only one input (Upload or URL) can be active at a time
- Live preview (image circle or square) always visible
- If user switches toggle, clear existing input with warning prompt:
  > “Switching input type will remove your current image. Continue?”

---

## ✅ JSON Format

```json
"subject": {
  "person": {
    "use_custom_face": true,
    "custom_face_url": "https://example.com/my-image.jpg"
  }
}
If uploaded, store temporary CDN URL (e.g. https://cdn.app/face123.jpg).

🧠 Prompt Injection Logic

When a valid image (uploaded or imported) is present:

“Replace the subject’s face with the uploaded or imported image. Ensure pose, lighting, and realism are maintained.”
🛡️ Developer Notes

Sanitize and validate external URLs (CORS handling or proxy fallback)
Preview fallback image if URL breaks
Uploaded images should be optimized and cached if needed
🧾 Done When:

User can switch between Upload and URL modes
Preview is always shown
Only one image is active at a time
Custom face is used in the next AI generation call