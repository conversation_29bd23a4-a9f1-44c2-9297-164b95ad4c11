---
description: 
globs: 
alwaysApply: false
---
---
title: welcome-screen-ui-rule
id: welcome-screen-ui-rule.mdc
ruleType: manual
---

## 🎨 Rule: Welcome Screen UI for Sign Up & Login

### Objective
Apply a modern, Hero UI–styled **Welcome Screen** layout for the MVP of the web app. This layout includes both **Sign Up** and **Login** states with clean tabs and consistent styling. Focus is on frontend UI only — no backend auth logic yet.

---

## Applies To
- /src/pages/Welcome.jsx
- /src/components/auth/
- /src/styles/

---

## Features

### 🔐 Sign Up View
- Fields: Full Name, Email, Password
- Buttons:
  - Primary: "Sign Up"
  - Google Auth UI Button (icon + label)
  - Skip button (bottom left, secondary)

### 🔑 Login View
- Fields: Email, Password
- Buttons:
  - Primary: "Log In"
  - Google Auth UI Button (icon + label)
  - Skip (bottom left), Forgot Password? (top right)

### 🧭 Layout Guidelines
- Hero UI card container (centered, max-w-sm)
- Dark mode compatible
- Tabs for toggling Sign Up / Login
- Accent color: #FFD700 or brand highlight

---

## UX Notes
- Always include a “Skip” button so users can enter without logging in
- Google button is **UI only** for now (no OAuth logic yet)
- Include divider (— or —) between buttons
- Make sure layout looks polished across mobile & desktop

---

## Cursor Behavior
- When working on `/Welcome.jsx` or auth UI components, apply this rule to enforce layout structure and styling logic
- This rule can be scaffolded into Hero UI + Tailwind React JSX automatically

---

✅ To activate in Cursor, type:
```mdc
@welcome-screen-ui-rule
```

UI-only logic — backend coming later.
