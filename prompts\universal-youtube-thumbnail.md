# Universal High-Quality YouTube Thumbnail Prompt

## Objective
Provide a single prompt template that reliably produces vibrant, cinematic, and highly detailed thumbnails for **any** video topic.

---

## Prompt Template

```
Create a cinematic, ultra-high-quality YouTube thumbnail at 1280x720 resolution.

Subject:
- Feature a bold, expressive main subject that clearly represents the video's topic.
- Use dynamic poses, exaggerated or engaging facial expressions, and clear visual storytelling.
- If relevant, include supporting objects, props, or secondary characters that enhance the theme.
- Ensure the subject is the clear focal point, with strong visual separation from the background.

Background:
- Use a visually interesting, context-appropriate background (e.g., futuristic, natural, abstract, or thematic).
- Incorporate lighting effects, color gradients, or textures to add depth and vibrancy.
- Include environmental details or visual cues that reinforce the topic where appropriate.

Text Overlay:
- Add a bold, uppercase title or caption. Keep text concise (ideally 3-5 words).
- Use a modern, sans-serif font. Apply a strong outline, prominent drop shadow, and subtle glow for maximum pop and readability.
- **Critical Safe Zone & Placement:**
    - Position the text block in the chosen area (e.g., lower-right, top-left).
    - **Strictly enforce a 64px safe margin from ALL four canvas edges (top, bottom, left, right).**
    - **Crucially, NO part of the text, including outlines, shadows, or glows, must EVER touch or cross these 64px boundaries.**
    - If the text is too long or the font size is too large to fit within this safe zone at the chosen position, the AI **MUST automatically reduce the font size, wrap the text to a new line, or slightly adjust word spacing** to ensure full compliance with the 64px safe margin.
    - The text must never obscure the primary subject.
- Ensure the text is highly visible and legible on both small mobile screens and large desktop displays.

Visual Style:
- Render in a highly detailed digital-painting or comic style with smooth gradients, sharp highlights, and crisp edges.
- Use vibrant, saturated colors and strong contrast for maximum impact.
- Deliver a polished, professional finish—**avoid blurriness or low-resolution artifacts**.
- Match the overall mood and tone (humorous, serious, dramatic, etc.) to the video content.

Output:
- 1280×720 px (16:9 aspect ratio).
- Ready for YouTube thumbnail use with all critical elements clearly visible on mobile and desktop.
```

---

## Recommended Usage
1. Replace **video's topic** with a concise description of your video subject.
2. Optionally specify the desired **mood** (e.g., energetic, suspenseful) or **style** (e.g., photorealistic, anime) after the first sentence.
3. Provide any must-have props, icons, or brand colors in the **Subject** or **Background** sections.
4. Keep the template structure; tweak bullet items to emphasize or omit elements as needed.

---

## Example Adaptations

> *Tech Review*: Replace the main subject with "a sleek new smartphone spinning in mid-air, lightning bolts of data streaming around it."
>
> *Cooking Channel*: "A chef enthusiastically flipping a sizzling skillet with colorful veggies flying."

---

**File location:** `prompts/universal-youtube-thumbnail.md` – include in your repo so teammates have a ready-made, high-quality starting point for any thumbnail. 