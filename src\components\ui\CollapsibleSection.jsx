/**
 * CollapsibleSection.jsx
 * Reusable component for creating collapsible sections in the sidebar
 */

import React, { useState, useEffect } from 'react'

export const CollapsibleSection = ({
  title,
  icon,
  defaultExpanded = false,
  children,
  id,
  // New props for dynamic max-height fix
  isDynamicHeight = false,
  isContentExpanded = false
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // Check localStorage for saved state on mount
  useEffect(() => {
    const savedState = localStorage.getItem(`section_expanded_${id}`);
    if (savedState !== null) {
      setIsExpanded(savedState === 'true');
    }
  }, [id]);

  // Save state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(`section_expanded_${id}`, isExpanded);
  }, [isExpanded, id]);

  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
  };

  return React.createElement('div', {
    className: 'glass-collapsible-section collapsible-section',
    id: `section-${id}`
  },
    // Glass-styled header with toggle
    React.createElement('div', {
      className: 'glass-collapsible-header flex items-center justify-between cursor-pointer',
      onClick: toggleExpanded,
      role: 'button',
      'aria-expanded': isExpanded,
      tabIndex: 0,
      onKeyDown: (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          toggleExpanded();
          e.preventDefault();
        }
      }
    },
      // Title with icon using glass typography
      React.createElement('h3', {
        className: 'glass-section-title text-md font-medium flex items-center gap-1.5 mb-0'
      },
        icon && React.createElement('span', {
          className: 'iconify text-purple-400',
          'data-icon': icon
        }),
        title
      ),
      // Chevron icon with rotation animation
      React.createElement('span', {
        className: 'iconify flex items-center justify-center',
        'data-icon': 'solar:alt-arrow-down-linear',
        style: { 
          color: 'white',
          fontSize: '1.25rem',
          minWidth: '1.25rem',
          minHeight: '1.25rem',
          transform: `rotate(${isExpanded ? '180deg' : '0deg'})`,
          transition: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
          transformOrigin: 'center'
        }
      })
    ),
    
    // Glass-styled collapsible content with dynamic height support
    React.createElement('div', {
      className: `glass-collapsible-content ${isExpanded ? 'expanded' : 'collapsed'}${
        isDynamicHeight && isContentExpanded && isExpanded ? ' face-upload-active' : ''
      }`,
      'aria-hidden': !isExpanded
    },
      children
    )
  );
};

export default CollapsibleSection; 