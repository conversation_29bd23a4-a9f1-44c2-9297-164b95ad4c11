---
title: Text Color Palette Presets
id: text-color-palette-presets.md
---

## 🎯 Feature: Text Color Palette Presets (With Custom Picker)

### Objective
Improve the thumbnail text design experience by providing a curated set of pre-made color palette templates for users to quickly apply high-impact, visually harmonized color combinations to their thumbnail titles or text overlays.

This supports creators who are unsure of color theory, accessibility, or style direction — while preserving full customization for advanced users.

---

## 🧩 UI Structure

### 1. Preset Palette Grid
- Display as **horizontal scrollable swatches** or **popup grid**
- Each palette contains **2–4 matched color chips**
- Label underneath each (e.g. `minty`, `glacier`, `blueberry`)
- Clicking a palette auto-updates:
  ```json
  "text_overlay.style.color": ["#HEX1", "#HEX2"]

2. Manual Color Picker (Optional)
Keep existing color picker input below presets
When adjusted manually, override preset values
Tooltip: “🎨 Custom adjustments will override selected palette”
💡 UX Behavior

Action	Result
User selects a palette	Colors applied to heading/subtitle instantly
User tweaks manually	Palette state becomes Custom
User resets	Returns to default preset or system theme
Tip: Apply first color to text-shadow or border styling if only one is selected.
📚 Suggested Palettes (Cold Tone Set)

Name	HEX Values
glacier	#7BDFF2, #B2F7EF, #EFFFE9
blueberry	#1F8A70, #004358, #BEDB39
minty	#A9FBD7, #CBF1F5, #D6FFF6
coldspring	#A3F7BF, #5EDFFF, #B967FF
deepsea	#1D1E2C, #3D426B, #8797AF, #C3CBDC
Add 🔥 Warm, 🎮 Gaming, 📚 Professional, and 💡 Creative categories in later phases.

🧾 Output Prompt Example

"text_overlay": {
  "title": "SHOCKING RESULTS!",
  "style": {
    "color": ["#7BDFF2", "#EFFFE9"],
    "shadow": true,
    "font": "bold sans-serif",
    "placement": "top right"
  }
}

✅ Done When:

Users can preview and apply color palettes visually
Manual overrides remain possible
Presets are expandable (future dynamic import support)
Palette name optionally included in exported prompt metadata
🧠 Dev Notes

Colors may be stored in textThemes.json or /src/constants/palettes.ts
Add fallback to light/dark mode contrast checks for accessibility
Use Tailwind-compatible classes when possible

## Bonus UX Tip 💡

Allow “Recently Used Colors” to appear beneath presets for quick reuse — store in localStorage per session.