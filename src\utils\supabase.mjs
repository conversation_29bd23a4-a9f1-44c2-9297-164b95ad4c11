import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config/supabase.mjs';

// Create a safe localStorage wrapper for Supabase
const safeLocalStorage = {
  getItem: (key) => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('localStorage getItem error:', error);
      return null;
    }
  },
  setItem: (key, value) => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      if (error.name === 'QuotaExceededError' || error.message.includes('quota has been exceeded')) {
        console.warn('🚨 localStorage quota exceeded for auth tokens, attempting cleanup...');
        
        try {
          // Emergency cleanup: Remove non-essential items
          const keysToRemove = [];
          for (let i = 0; i < localStorage.length; i++) {
            const storageKey = localStorage.key(i);
            if (storageKey && storageKey !== key) {
              // Remove old auth tokens, avatars, and cache data
              if (storageKey.includes('avatar_') || 
                  storageKey.includes('cache_') || 
                  storageKey.includes('temp_') ||
                  storageKey.includes('openmojiCache') ||
                  (storageKey.includes('auth-token') && storageKey !== key)) {
                keysToRemove.push(storageKey);
              }
            }
          }
          
          // Remove up to 5 items to free space
          keysToRemove.slice(0, 5).forEach(k => {
            try { localStorage.removeItem(k); } catch {}
          });
          
          // Retry setting the auth token
          localStorage.setItem(key, value);
          console.log('✅ Auth token stored successfully after emergency cleanup');
          
        } catch (retryError) {
          console.error('❌ Critical: Failed to store auth token even after cleanup:', retryError);
          // Show user notification about storage issues
          if (window.showNotification) {
            window.showNotification('Storage limit reached. Please clear browser data.', 'error');
          }
        }
      } else {
        console.error('localStorage setItem error:', error);
      }
    }
  },
  removeItem: (key) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('localStorage removeItem error:', error);
    }
  }
};

// Create Supabase client with safe localStorage
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true, // Enable session persistence for password recovery
    autoRefreshToken: true, // Enable auto token refresh
    storage: safeLocalStorage, // Use safe localStorage wrapper
    detectSessionInUrl: true, // Detect session from URL for OAuth flows
    flowType: 'pkce' // Use PKCE flow for better security
  },
  // Reduce connection pooling to prevent multiple simultaneous connections
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Authentication functions
export const authAPI = {
  // Sign up with email and password
  signUp: async (email, password, fullName) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          }
        }
      });
      
      if (error) {
        // Handle specific Supabase errors
        if (error.message.includes('User already registered')) {
          return { success: false, error: 'An account with this email already exists. Please try signing in instead.' };
        }
        if (error.message.includes('Password should be at least')) {
          return { success: false, error: 'Password must be at least 6 characters long.' };
        }
        if (error.message.includes('Invalid email')) {
          return { success: false, error: 'Please enter a valid email address.' };
        }
        throw error;
      }
      
      return { success: true, data, needsConfirmation: !data.user.email_confirmed_at };
    } catch (error) {
      console.error('Sign up error:', error);
      return { success: false, error: error.message || 'Failed to create account. Please try again.' };
    }
  },

  // Sign in with email and password
  signIn: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        // Handle specific Supabase errors
        if (error.message.includes('Invalid login credentials')) {
          return { success: false, error: 'Invalid email or password. Please check your credentials and try again.' };
        }
        if (error.message.includes('Email not confirmed')) {
          return { success: false, error: 'Please check your email and click the confirmation link before signing in.' };
        }
        throw error;
      }
      
      return { success: true, data, user: data.user };
    } catch (error) {
      console.error('Sign in error:', error);
      return { success: false, error: error.message || 'Failed to sign in. Please try again.' };
    }
  },

  // Sign in with Google
  signInWithGoogle: async () => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      });
      
      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Google sign in error:', error);
      return { success: false, error: error.message || 'Failed to sign in with Google. Please try again.' };
    }
  },

  // Sign out
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear any local storage items
      localStorage.removeItem('thumbnail-generator-auth');
      // Clear all Supabase session data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('sb-csibhnfqpwqkhpnvdakz-auth-token') || 
            key.startsWith('supabase.auth.token')) {
          localStorage.removeItem(key);
        }
      });
      
      return { success: true };
    } catch (error) {
      console.error('Sign out error:', error);
      return { success: false, error: error.message || 'Failed to sign out. Please try again.' };
    }
  },

  // Clear invalid tokens and reset auth state
  clearInvalidTokens: () => {
    try {
      console.log('🧹 Clearing invalid tokens...');
      
      // Clear all Supabase-related localStorage items
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('sb-') || 
            key.startsWith('supabase.auth.token') ||
            key.includes('auth-token')) {
          console.log('Removing:', key);
          localStorage.removeItem(key);
        }
      });
      
      // Clear session storage as well
      if (sessionStorage) {
        const sessionKeys = Object.keys(sessionStorage);
        sessionKeys.forEach(key => {
          if (key.startsWith('sb-') || 
              key.startsWith('supabase.auth.token') ||
              key.includes('auth-token')) {
            console.log('Removing from session:', key);
            sessionStorage.removeItem(key);
          }
        });
      }
      
      console.log('✅ Invalid tokens cleared');
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  },
  
  // Sends a password reset link to the user's email
  sendPasswordResetEmail: async (email) => {
    try {
      const redirectUrl = `${window.location.origin}/reset-password`;
      console.log('Sending password reset email with redirect URL:', redirectUrl);
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });
      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Send password reset email error:', error);
      // Provide more specific error messages if known
      if (error.message.includes('User not found')) {
        return { success: false, error: 'No account found with this email address.' };
      }
      if (error.message.includes('rate limit')) {
        return { success: false, error: 'Too many requests. Please try again later.' };
      }
      return { success: false, error: error.message || 'Failed to send password reset email.' };
    }
  },

  // Update password (after OTP recovery verification)
  updatePassword: async (newPassword) => {
    try {
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;
      return { success: true, session: data?.session };
    } catch (error) {
      console.error('Password update error:', error);
      return { success: false, error: error.message };
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return { success: true, user };
    } catch (error) {
      console.error('Get current user error:', error);
      return { success: false, error: error.message };
    }
  },

  // Get current session
  getCurrentSession: async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;

      // Trust the session data from getSession() - it already validates the token
      // Remove the additional getUser() call that was causing loops
      return { success: true, session };
    } catch (error) {
      console.error('Get current session error:', error);
      return { success: false, error: error.message };
    }
  },

  // Listen to auth state changes
  onAuthStateChange: (callback) => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email || 'no user');

      // Handle password recovery event - this is key for the /reset-password page
      if (event === 'PASSWORD_RECOVERY') {
        callback(event, session); // Pass event and session to handler in ResetPassword.jsx
        return;
      }

      // For SIGNED_IN events, only verify user if we don't already have session data
      // This prevents unnecessary API calls that could cause loops
      if (event === 'SIGNED_IN' && session && session.user) {
        // Trust the session data from the auth state change
        // Only do additional verification if there's a specific need
        callback(event, session);
        return;
      }

      callback(event, session);
    });

    // Return the unsubscribe function
    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  },

  // Check if user is authenticated
  isAuthenticated: async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return !!session;
    } catch (error) {
      console.error('Check authentication error:', error);
      return false;
    }
  }
}; 