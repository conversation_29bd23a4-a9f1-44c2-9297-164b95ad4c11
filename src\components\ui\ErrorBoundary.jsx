import React from 'react'
import { authAPI } from '../../utils/supabase.mjs'

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { 
            hasError: false, 
            error: null, 
            errorInfo: null 
        };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Log the error
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        // Check if it's an authentication-related error
        if (error.message && (
            error.message.includes('Invalid Refresh Token') ||
            error.message.includes('unsubscribe is not a function') ||
            error.message.includes('auth')
        )) {
            console.log('🧹 Authentication error detected, clearing tokens...');
            authAPI.clearInvalidTokens();
        }
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });
    }

    handleReload = () => {
        // Clear any invalid tokens before reloading
        authAPI.clearInvalidTokens();
        window.location.reload();
    }

    handleClearTokens = () => {
        // Clear tokens and reset error state
        authAPI.clearInvalidTokens();
        this.setState({ 
            hasError: false, 
            error: null, 
            errorInfo: null 
        });
    }

    render() {
        if (this.state.hasError) {
            // Fallback UI
            return (
                <div className="flex min-h-screen bg-gray-900 text-white items-center justify-center p-6">
                    <div className="max-w-md w-full text-center space-y-6">
                        {/* Error Icon */}
                        <div className="flex justify-center">
                            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center">
                                <span className="iconify text-red-400" data-icon="solar:danger-triangle-bold" style={{ fontSize: '32px' }}></span>
                            </div>
                        </div>

                        {/* Error Message */}
                        <div>
                            <h2 className="text-2xl font-bold mb-2 text-red-400">Something went wrong</h2>
                            <p className="text-gray-400 mb-4">
                                An unexpected error occurred. This might be due to invalid authentication tokens.
                            </p>
                            
                            {/* Error Details (in development) */}
                            {process.env.NODE_ENV === 'development' && this.state.error && (
                                <details className="text-left bg-gray-800 p-4 rounded-lg mb-4">
                                    <summary className="cursor-pointer text-sm text-gray-300 mb-2">
                                        Error Details (Development)
                                    </summary>
                                    <div className="text-xs text-red-300 font-mono">
                                        <p className="mb-2"><strong>Error:</strong> {this.state.error.toString()}</p>
                                        {this.state.errorInfo && (
                                            <pre className="whitespace-pre-wrap overflow-auto max-h-40">
                                                {this.state.errorInfo.componentStack}
                                            </pre>
                                        )}
                                    </div>
                                </details>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="space-y-3">
                            <button
                                onClick={this.handleClearTokens}
                                className="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-xl font-medium transition-colors flex items-center justify-center gap-2"
                            >
                                <span className="iconify" data-icon="solar:refresh-bold"></span>
                                Clear Tokens & Retry
                            </button>
                            
                            <button
                                onClick={this.handleReload}
                                className="w-full py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-medium transition-colors flex items-center justify-center gap-2"
                            >
                                <span className="iconify" data-icon="solar:restart-bold"></span>
                                Reload Page
                            </button>
                        </div>

                        {/* Help Text */}
                        <p className="text-xs text-gray-500">
                            If this problem persists, try clearing your browser cache or contact support.
                        </p>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

export { ErrorBoundary }; 