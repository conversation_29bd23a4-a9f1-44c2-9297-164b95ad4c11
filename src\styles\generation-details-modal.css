/* Generation Details Modal Styles - Hero UI Dark Mode */

/* Modal Overlay */
.generation-details-modal-overlay {
    animation: fadeIn 0.2s ease-out;
}

.generation-details-modal-overlay.closing {
    animation: fadeOut 0.2s ease-in;
}

.generation-details-modal-overlay .generation-details-modal-container {
    animation: slideInScale 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.generation-details-modal-overlay.closing .generation-details-modal-container {
    animation: slideOutScale 0.2s cubic-bezier(0.4, 0, 1, 1);
}

/* Modal Composition */
.modal-composition {
    background: rgba(31, 41, 55, 0.95); /* Darker background for better contrast */
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.3),
        0 4px 6px -2px rgba(0, 0, 0, 0.2),
        0 20px 25px -5px rgba(0, 0, 0, 0.25);
}

/* Prompt Content Area */
.prompt-content {
    background: rgba(17, 24, 39, 0.8); /* Darker background matching modal theme */
    border-color: rgba(75, 85, 99, 0.3);
}

/* Prompt Content Scrollbar */
.prompt-content::-webkit-scrollbar {
    width: 6px;
}

.prompt-content::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 3px;
}

.prompt-content::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 3px;
}

.prompt-content::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

/* Copy Button Animation */
.copy-btn {
    transition: all 0.2s ease;
}

.copy-btn:active {
    transform: scale(0.95);
}

/* Checkbox Styling */
.checkbox:checked {
    background-color: #8B5CF6;
    border-color: #8B5CF6;
}

.checkbox:checked:hover {
    background-color: #7C3AED;
    border-color: #7C3AED;
}

.checkbox:focus {
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Detail Rows */
.detail-row {
    border-bottom: 1px solid rgba(75, 85, 99, 0.2);
}

.detail-row:last-child {
    border-bottom: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
}

@keyframes slideOutScale {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0) scale(1);
    }
    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0) scale(0.95);
    }
}

/* Responsive Design */
@media (max-width: 640px) {
    .generation-details-modal-container {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .modal-composition {
        border-radius: 1rem;
        width: 100%;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    .prompt-content {
        max-height: 6rem;
    }
}

/* Mobile Portrait Specific */
@media (max-width: 480px) and (orientation: portrait) {
    .generation-details-modal-overlay {
        padding: 1rem;
    }
    
    .generation-details-modal-container {
        margin: 0;
        max-width: 100%;
        width: 100%;
    }
    
    .modal-composition {
        max-width: 100%;
        margin: 0 auto;
    }
    
    .modal-title {
        font-size: 1rem;
    }
    
    .prompt-content {
        font-size: 0.75rem;
        max-height: 5rem;
    }
    
    .detail-row {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .copy-btn {
        padding: 0.375rem 0.75rem;
    }
} 