const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;
import { AdminTable } from './AdminTable.jsx';
import { TemplateModal } from './TemplateModal.jsx';

export const TemplateManagementPage = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');

    // Mock template data
    const [templates, setTemplates] = useState([
        {
            id: 'template-1',
            name: 'Gaming Reaction',
            category: 'Gaming',
            description: 'Perfect for gaming reaction videos with shocked expressions',
            dateAdded: '2024-01-15',
            preview: '/placeholder-gaming.jpg',
            basePrompt: 'Create a gaming reaction thumbnail with shocked expression',
            settings: {
                includePerson: true,
                selectedExpression: 'Shocked',
                textOverlay: true,
                overlayText: 'EPIC REACTION!'
            }
        },
        {
            id: 'template-2',
            name: 'Tech Review',
            category: 'Technology',
            description: 'Clean tech review style with product focus',
            dateAdded: '2024-01-14',
            preview: '/placeholder-tech.jpg',
            basePrompt: 'Create a tech review thumbnail with clean modern design',
            settings: {
                includePerson: false,
                includeIcons: true,
                textOverlay: true,
                overlayText: 'TECH REVIEW'
            }
        },
        {
            id: 'template-3',
            name: 'Tutorial Guide',
            category: 'Education',
            description: 'Educational tutorial style with step-by-step focus',
            dateAdded: '2024-01-13',
            preview: '/placeholder-tutorial.jpg',
            basePrompt: 'Create an educational tutorial thumbnail',
            settings: {
                includePerson: true,
                selectedExpression: 'Happy',
                textOverlay: true,
                overlayText: 'LEARN NOW!'
            }
        }
    ]);

    const categories = ['all', 'Gaming', 'Technology', 'Education', 'Entertainment', 'Business'];

    const handleAddTemplate = () => {
        setEditingTemplate(null);
        setIsModalOpen(true);
    };

    const handleEditTemplate = (template) => {
        setEditingTemplate(template);
        setIsModalOpen(true);
    };

    const handleDeleteTemplate = (templateId) => {
        if (window.confirm('Are you sure you want to delete this template?')) {
            setTemplates(prev => prev.filter(t => t.id !== templateId));
        }
    };

    const handleSaveTemplate = (templateData) => {
        if (editingTemplate) {
            // Update existing template
            setTemplates(prev => prev.map(t => 
                t.id === editingTemplate.id ? { ...templateData, id: editingTemplate.id } : t
            ));
        } else {
            // Add new template
            const newTemplate = {
                ...templateData,
                id: `template-${Date.now()}`,
                dateAdded: new Date().toISOString().split('T')[0]
            };
            setTemplates(prev => [...prev, newTemplate]);
        }
        setIsModalOpen(false);
        setEditingTemplate(null);
    };

    const filteredTemplates = templates.filter(template => {
        const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            template.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });

    const tableColumns = [
        {
            key: 'preview',
            label: 'Preview',
            render: (template) => React.createElement('div', {
                className: 'template-preview-cell',
                id: `template-preview-${template.id}`
            },
                React.createElement('div', {
                    className: 'template-preview-placeholder w-16 h-10 bg-gray-700 rounded flex items-center justify-center'
                },
                    React.createElement('span', {
                        className: 'template-preview-text text-xs text-gray-400'
                    }, 'IMG')
                )
            )
        },
        {
            key: 'name',
            label: 'Template Name',
            render: (template) => React.createElement('div', {
                className: 'template-name-cell'
            },
                React.createElement('span', {
                    className: 'template-name-text font-medium text-white'
                }, template.name)
            )
        },
        {
            key: 'category',
            label: 'Category',
            render: (template) => React.createElement('span', {
                className: 'template-category-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800'
            }, template.category)
        },
        {
            key: 'description',
            label: 'Description',
            render: (template) => React.createElement('span', {
                className: 'template-description-text text-gray-400 text-sm'
            }, template.description.length > 50 ? template.description.substring(0, 50) + '...' : template.description)
        },
        {
            key: 'dateAdded',
            label: 'Date Added',
            render: (template) => React.createElement('span', {
                className: 'template-date-text text-gray-400 text-sm'
            }, new Date(template.dateAdded).toLocaleDateString())
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (template) => React.createElement('div', {
                className: 'template-actions-cell flex items-center gap-2'
            },
                React.createElement('button', {
                    className: 'template-edit-btn p-2 text-blue-400 hover:text-blue-300 transition-colors',
                    onClick: () => handleEditTemplate(template),
                    title: 'Edit template'
                },
                    React.createElement('svg', {
                        className: 'template-edit-icon w-4 h-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'm16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10'
                        })
                    )
                ),
                React.createElement('button', {
                    className: 'template-delete-btn p-2 text-red-400 hover:text-red-300 transition-colors',
                    onClick: () => handleDeleteTemplate(template.id),
                    title: 'Delete template'
                },
                    React.createElement('svg', {
                        className: 'template-delete-icon w-4 h-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'm14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0'
                        })
                    )
                )
            )
        }
    ];

    return React.createElement('div', {
        className: 'admin-template-management-page',
        id: 'admin-template-management-content'
    },
        // Page Header
        React.createElement('div', {
            className: 'admin-template-management-header flex justify-between items-center mb-8',
            id: 'admin-template-management-header'
        },
            React.createElement('div', {
                className: 'admin-template-header-left'
            },
                React.createElement('h1', {
                    className: 'admin-template-management-title text-3xl font-bold text-white mb-2'
                }, 'Thumbnail Template Management'),
                React.createElement('p', {
                    className: 'admin-template-management-subtitle text-gray-400'
                }, 'Create and manage premade thumbnail templates for users')
            ),
            React.createElement('button', {
                className: 'admin-add-template-btn bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2',
                id: 'admin-add-template-btn',
                onClick: handleAddTemplate
            },
                React.createElement('svg', {
                    className: 'admin-add-template-icon w-5 h-5',
                    fill: 'none',
                    stroke: 'currentColor',
                    viewBox: '0 0 24 24'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: 'M12 4.5v15m7.5-7.5h-15'
                    })
                ),
                'Add New Template'
            )
        ),

        // Filters and Search
        React.createElement('div', {
            className: 'admin-template-filters-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6',
            id: 'admin-template-filters-section'
        },
            React.createElement('div', {
                className: 'admin-template-filters-grid grid grid-cols-1 md:grid-cols-2 gap-4'
            },
                // Search Input
                React.createElement('div', {
                    className: 'admin-template-search-wrapper'
                },
                    React.createElement('label', {
                        className: 'admin-template-search-label block text-sm font-medium text-gray-300 mb-2'
                    }, 'Search Templates'),
                    React.createElement('input', {
                        type: 'text',
                        className: 'admin-template-search-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                        id: 'admin-template-search-input',
                        placeholder: 'Search by name or description...',
                        value: searchTerm,
                        onChange: (e) => setSearchTerm(e.target.value)
                    })
                ),
                
                // Category Filter
                React.createElement('div', {
                    className: 'admin-template-category-wrapper'
                },
                    React.createElement('label', {
                        className: 'admin-template-category-label block text-sm font-medium text-gray-300 mb-2'
                    }, 'Filter by Category'),
                    React.createElement('select', {
                        className: 'admin-template-category-select w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                        id: 'admin-template-category-select',
                        value: selectedCategory,
                        onChange: (e) => setSelectedCategory(e.target.value)
                    }, categories.map(category => 
                        React.createElement('option', {
                            key: category,
                            value: category
                        }, category === 'all' ? 'All Categories' : category)
                    ))
                )
            )
        ),

        // Templates Table
        React.createElement('div', {
            className: 'admin-templates-table-wrapper',
            id: 'admin-templates-table-section'
        },
            React.createElement(AdminTable, {
                columns: tableColumns,
                data: filteredTemplates,
                emptyMessage: 'No templates found. Create your first template to get started.',
                tableId: 'admin-templates-table'
            })
        ),

        // Template Modal
        isModalOpen && React.createElement(TemplateModal, {
            isOpen: isModalOpen,
            onClose: () => {
                setIsModalOpen(false);
                setEditingTemplate(null);
            },
            onSave: handleSaveTemplate,
            template: editingTemplate,
            categories: categories.filter(cat => cat !== 'all')
        })
    );
}; 