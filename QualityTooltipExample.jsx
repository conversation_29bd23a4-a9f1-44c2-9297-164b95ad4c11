// QualityTooltipExample.jsx
// This is a simplified example of how to implement the Image Quality tooltip

// Import React and necessary hooks
const React = window.React;
const { useState } = React;

// Helper function to create an info icon using Solar Icon Set via Iconify
const createInfoIcon = (className = 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help') => {
    return React.createElement('span', {
        className: className,
        role: 'img',
        'aria-label': 'Information',
        dangerouslySetInnerHTML: {
            __html: `<span class="iconify" data-icon="solar:info-circle-linear" style="color: currentColor;"></span>`
        }
    });
};

// Quality Selector Component with Tooltip
const QualitySelector = () => {
    const [selectedQuality, setSelectedQuality] = useState('normal'); // 'low', 'normal', 'hd'
    
    const handleQualityChange = (newQuality) => {
        setSelectedQuality(newQuality);
    };

    return React.createElement('div', { 
        className: 'w-full mt-4',
        style: {
            backgroundColor: '#1E1E20',
            borderRadius: '14px',
            width: '100%'
        }
    },
        // Quality selector container
        React.createElement('div', {
            className: 'py-4 px-4 flex items-center justify-between',
        },
            // Quality Label with Info Tooltip
            React.createElement('div', {
                className: 'flex items-center gap-2'
            },
                React.createElement('div', {
                    className: 'text-sm font-normal',
                    style: { color: '#FFFFFF' }
                }, 'Image Quality'),
                
                // Info tooltip
                React.createElement('div', {
                    className: 'relative group'
                },
                    createInfoIcon(),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 z-20 mb-2 -translate-x-1/2 transform rounded-md bg-purple-900 px-3 py-2 text-xs text-white shadow-lg opacity-0 invisible transition-all duration-300 ease-in-out group-hover:opacity-100 group-hover:visible',
                        style: { minWidth: '280px' },
                        role: 'tooltip',
                        'aria-hidden': 'true' // Will be shown on hover via CSS
                    },
                        React.createElement('div', { 
                            className: 'absolute -bottom-2 left-1/2 h-4 w-4 -translate-x-1/2 transform rotate-45 bg-purple-900'
                        }),
                        React.createElement('div', { 
                            className: 'relative z-10'
                        }, 
                            React.createElement('h3', {
                                className: 'font-medium mb-1'
                            }, 'Image Quality Options'),
                            React.createElement('ul', {
                                className: 'space-y-2'
                            },
                                React.createElement('li', {},
                                    React.createElement('strong', {}, 'Low (Draft): '),
                                    'Fastest, lowest credit usage. Great for quick drafts and previews.'
                                ),
                                React.createElement('li', {},
                                    React.createElement('strong', {}, 'Medium (Balanced): '),
                                    'Best for most use cases. Good balance of quality and credit cost.'
                                ),
                                React.createElement('li', {},
                                    React.createElement('strong', {}, 'High (HD): '),
                                    'Highest quality rendering. Ideal for final exports, but uses more credits.'
                                )
                            )
                        )
                    )
                )
            ),
            
            // Button Group
            React.createElement('div', {
                className: 'flex rounded-xl overflow-hidden',
                style: {
                    background: 'rgba(63, 63, 70, 0.4)',
                    borderRadius: '12px'
                }
            },
                // Low quality option
                React.createElement('button', {
                    className: `relative px-4 py-2 text-sm font-normal flex items-center justify-center ${selectedQuality === 'low' ? 'bg-[#9353D3] rounded-xl' : 'bg-transparent text-white'}`,
                    onClick: () => handleQualityChange('low'),
                    style: { 
                        minWidth: '120px',
                        color: '#FFFFFF'
                    }
                }, 'Low ( Draft )'),
                
                // Medium quality option (selected by default)
                React.createElement('button', {
                    className: `relative px-4 py-2 text-sm font-medium flex items-center justify-center ${selectedQuality === 'normal' ? 'bg-[#9353D3] rounded-xl' : 'bg-transparent text-white'}`,
                    onClick: () => handleQualityChange('normal'),
                    style: { 
                        minWidth: '170px',
                        color: '#FFFFFF'
                    }
                }, 'Medium (Balanced)'),
                
                // High quality option
                React.createElement('button', {
                    className: `relative px-4 py-2 text-sm font-normal flex items-center justify-center ${selectedQuality === 'hd' ? 'bg-[#9353D3] rounded-xl' : 'bg-transparent text-white'}`,
                    onClick: () => handleQualityChange('hd'),
                    style: { 
                        minWidth: '120px',
                        color: '#FFFFFF'
                    }
                }, 'High (HD)')
            )
        )
    );
};

// Usage in App.jsx:
// 1. Find the Image Quality section in the render function
// 2. Modify the Quality Label to include the tooltip
// 3. Add the tooltip with the specified content

// Example modification to App.jsx (pseudo-code):
/*
// In App.jsx, find the Quality Label section:

React.createElement('div', {
    className: 'text-sm font-normal',
    style: { color: '#FFFFFF' }
}, 'Image Quality'),

// Replace with:

React.createElement('div', {
    className: 'flex items-center gap-2'
},
    React.createElement('div', {
        className: 'text-sm font-normal',
        style: { color: '#FFFFFF' }
    }, 'Image Quality'),
    
    // Info tooltip
    React.createElement('div', {
        className: 'relative group'
    },
        createInfoIcon(),
        React.createElement('div', {
            className: 'absolute bottom-full left-1/2 z-20 mb-2 -translate-x-1/2 transform rounded-md bg-purple-900 px-3 py-2 text-xs text-white shadow-lg opacity-0 invisible transition-all duration-300 ease-in-out group-hover:opacity-100 group-hover:visible',
            style: { minWidth: '280px' }
        },
            React.createElement('div', { 
                className: 'absolute -bottom-2 left-1/2 h-4 w-4 -translate-x-1/2 transform rotate-45 bg-purple-900'
            }),
            React.createElement('div', { 
                className: 'relative z-10'
            }, 
                React.createElement('h3', {
                    className: 'font-medium mb-1'
                }, 'Image Quality Options'),
                React.createElement('ul', {
                    className: 'space-y-2'
                },
                    React.createElement('li', {},
                        React.createElement('strong', {}, 'Low (Draft): '),
                        'Fastest, lowest credit usage. Great for quick drafts and previews.'
                    ),
                    React.createElement('li', {},
                        React.createElement('strong', {}, 'Medium (Balanced): '),
                        'Best for most use cases. Good balance of quality and credit cost.'
                    ),
                    React.createElement('li', {},
                        React.createElement('strong', {}, 'High (HD): '),
                        'Highest quality rendering. Ideal for final exports, but uses more credits.'
                    )
                )
            )
        )
    )
)
*/

export default QualitySelector; 