/**
 * @file Manages mobile zoom behavior, specifically for disabling pinch-to-zoom
 * in portrait mode to ensure a consistent, app-like experience.
 * This utility provides developers with simple controls to enable or disable the feature
 * and includes accessibility considerations.
 */

// State to track if the zoom lock is active
let isZoomLockActive = false;
let isNotificationShown = false;

/**
 * Checks if the device is a mobile device in portrait orientation.
 * @returns {boolean} - True if the device is mobile and in portrait mode.
 */
const isMobilePortrait = () => {
    const isMobile = /Mobi|Android/i.test(navigator.userAgent);
    const isPortrait = window.matchMedia("(orientation: portrait)").matches;
    return isMobile && isPortrait;
};

/**
 * Handles the touchstart event to prevent zooming.
 * It checks for multi-touch gestures, which are indicative of pinch-to-zoom.
 * @param {TouchEvent} event - The touch event.
 */
const handleTouchStart = (event) => {
    if (isMobilePortrait() && event.touches.length > 1) {
        event.preventDefault();

        // Show a notification to the user, but only once per session
        if (!isNotificationShown) {
            const toastEvent = new CustomEvent('show-toast', {
                detail: {
                    message: 'Zoom has been disabled for a better viewing experience.',
                    type: 'info',
                },
            });
            window.dispatchEvent(toastEvent);
            isNotificationShown = true;
        }
    }
};

const handleGesture = (e) => {
    if (isMobilePortrait()) {
        e.preventDefault();
    }
};

/**
 * Enables the mobile zoom lock.
 * This adds the necessary event listeners to prevent pinch-to-zoom.
 * It also checks the orientation and adds/removes the listener accordingly.
 */
export const enableZoomLock = () => {
    if (isZoomLockActive) return;

    const orientationHandler = () => {
        if (isMobilePortrait()) {
            document.addEventListener('touchstart', handleTouchStart, { passive: false });
        } else {
            document.removeEventListener('touchstart', handleTouchStart, { passive: false });
        }
    };

    // Initial check
    orientationHandler();

    // Listen for orientation changes
    window.matchMedia("(orientation: portrait)").addEventListener('change', orientationHandler);
    
    // iOS Safari gesture events
    document.addEventListener('gesturestart', handleGesture, { passive: false });
    document.addEventListener('gesturechange', handleGesture, { passive: false });
    document.addEventListener('gestureend', handleGesture, { passive: false });

    isZoomLockActive = true;
    console.log('Mobile zoom lock ENABLED.');

    // Store the handler to remove it later
    window.mobileZoomOrientationHandler = orientationHandler;
};

/**
 * Disables the mobile zoom lock.
 * This removes all event listeners associated with the feature.
 */
export const disableZoomLock = () => {
    if (!isZoomLockActive) return;

    document.removeEventListener('touchstart', handleTouchStart, { passive: false });
    
    if (window.mobileZoomOrientationHandler) {
        window.matchMedia("(orientation: portrait)").removeEventListener('change', window.mobileZoomOrientationHandler);
        delete window.mobileZoomOrientationHandler;
    }

    document.removeEventListener('gesturestart', handleGesture);
    document.removeEventListener('gesturechange', handleGesture);
    document.removeEventListener('gestureend', handleGesture);

    isZoomLockActive = false;
    isNotificationShown = false; // Reset notification status
    console.log('Mobile zoom lock DISABLED.');
};

/**
 * Returns the current status of the zoom lock.
 * @returns {boolean} - True if the zoom lock is currently active.
 */
export const isZoomLockEnabled = () => {
    return isZoomLockActive;
}; 