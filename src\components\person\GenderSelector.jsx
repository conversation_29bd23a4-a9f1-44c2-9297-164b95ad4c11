import React from 'react'
import { genderEmojiMapping, getOpenMojiUrl } from '../../utils/openmojiMapping.js';

export const GenderSelector = ({
  selectedGender,
  setSelectedGender,
  createTooltipIconProp // Prop for the tooltip utility
}) => {
  const genderOptions = Object.entries(genderEmojiMapping).map(([key, data]) => ({
    value: key,
    ...data
  }));
  const tooltipText = "Specify the gender of the person in the thumbnail. 'Auto' lets the AI decide.";

  return React.createElement('div', { className: 'gender-selector-section py-2', id: 'gender-selector-section' },
    React.createElement('div', { className: 'flex items-center justify-between mb-1'},
      React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, 'Preferred Gender:'),
      createTooltipIconProp(tooltipText, 'tooltip-gender')
    ),
    React.createElement('div', { className: 'grid grid-cols-4 gap-2 mt-2', role: 'radiogroup', 'aria-label': 'Gender selection' },
      genderOptions.map(gender => {
        const isSelected = selectedGender === gender.value;
        
        // ALL gender emoji use SVG with optimal quality settings
        const imgProps = {
          src: getOpenMojiUrl(gender.openmojiHex),
          alt: gender.ariaLabel,
          className: 'w-8 h-8 object-contain mb-1',
          loading: 'lazy',
          draggable: false,
          decoding: 'async',
          fetchpriority: 'low',
          style: {
            imageRendering: 'optimizeQuality',
            WebkitUserSelect: 'none',
            userSelect: 'none',
            // Force crisp SVG rendering for ALL gender emoji
            WebkitImageRendering: '-webkit-optimize-contrast',
            MozImageRendering: 'crisp-edges'
          },
          onError: (e) => {
            console.error(`Failed to load OpenMoji SVG for ${gender.label}`);
            e.target.style.display = 'none';
            e.target.parentElement.insertAdjacentHTML('afterbegin', `<span class="text-2xl mb-1">${gender.unicode}</span>`);
          },
          onLoad: (e) => {
            console.log(`✅ OpenMoji SVG loaded: ${gender.label} (${isSelected ? 'SELECTED' : 'unselected'})`);
          }
        };
        
        return React.createElement('button', {
          key: gender.value,
          onClick: () => setSelectedGender(gender.value),
          className: `gender-card flex flex-col items-center justify-center p-2 ${isSelected ? 'bg-purple-700 border-purple-500' : 'bg-gray-700 border-gray-600'} border rounded-md shadow-sm hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500`,
          'aria-pressed': isSelected,
          'aria-label': `${gender.ariaLabel} - ${gender.label}`,
          role: 'radio',
          'aria-checked': isSelected
        },
          React.createElement('img', imgProps),
          React.createElement('span', { 
            className: `text-xs font-medium ${isSelected ? 'font-bold' : ''}` 
          }, gender.label)
        );
      })
    )
  );
}; 