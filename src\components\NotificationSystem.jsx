// Use the same React pattern as the main app
import React, { useState, useEffect } from 'react'

// Real notification data based on user activity and app state
const getNotifications = (user) => {
    const now = new Date();
    const userGenerations = JSON.parse(localStorage.getItem('user_generation_history') || '[]');
    const totalCreditsUsed = userGenerations.reduce((sum, gen) => sum + (gen.credits || 3), 0);
    const remainingCredits = 1000 - totalCreditsUsed;
    const hdGenerationsUsed = userGenerations.filter(gen => (gen.quality || '').toLowerCase() === 'hd').length;
    const freeLimitReached = user?.app_metadata?.plan === 'free' && hdGenerationsUsed >= 3;
    
    return [
        {
            id: 'welcome-new-user',
            type: 'tutorial',
            title: 'New Tutorial Available',
            message: 'Watch our latest video on mastering AI image generation techniques',
            icon: 'solar:play-circle-linear',
            iconColor: 'text-gray-400',
            timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(),
            isRead: false,
            actionText: 'Watch Now',
            actionUrl: '#tutorial'
        },
        {
            id: 'platform-updates',
            type: 'update',
            title: 'Platform Updates',
            message: "We've added new features to enhance your thumbnail creation experience",
            icon: 'solar:rocket-linear',
            iconColor: 'text-gray-400',
            timestamp: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(),
            isRead: false,
            actionText: 'Learn More',
            actionUrl: '#updates'
        },
        {
            id: 'free-limit-reached',
            type: 'warning',
            title: 'Free Plan Limit Reached',
            message: 'You have used all 3 of your free HD generations. Please upgrade to continue creating.',
            icon: 'solar:danger-triangle-linear',
            iconColor: 'text-gray-400',
            timestamp: new Date().toISOString(),
            isRead: false,
            actionText: 'Upgrade Now',
            actionUrl: '#upgrade',
            show: freeLimitReached
        },
        {
            id: 'low-credits-warning',
            type: 'warning',
            title: 'Low Credit Balance',
            message: `Your credit balance is running low. Consider upgrading to continue creating amazing content`,
            icon: 'solar:danger-triangle-linear',
            iconColor: 'text-gray-400',
            timestamp: new Date(now.getTime() - 30 * 60 * 1000).toISOString(),
            isRead: false,
            actionText: 'Upgrade Plan',
            actionUrl: '#upgrade',
            show: remainingCredits < 100 && user?.app_metadata?.plan !== 'free'
        },
        {
            id: 'quick-start-guide',
            type: 'tip',
            title: 'Quick Start Guide',
            message: 'Learn the basics of PixelMuse in our comprehensive video tutorial',
            icon: 'solar:lightbulb-bolt-linear',
            iconColor: 'text-gray-400',
            timestamp: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(),
            isRead: true,
            actionText: 'Start Guide',
            actionUrl: '#guide'
        }
    ].filter(notification => notification.show !== false);
};

export const NotificationSystem = ({ user, isOpen, onClose, onOpenDashboard, onNotificationUpdate }) => {
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [shouldRender, setShouldRender] = useState(false);

    // Handle mount/unmount with proper animation timing
    useEffect(() => {
        if (isOpen) {
            setShouldRender(true);
        } else {
            // Delay unmounting to allow exit animation to complete
            const timeoutId = setTimeout(() => {
                setShouldRender(false);
            }, 200); // Match animation duration
            return () => clearTimeout(timeoutId);
        }
    }, [isOpen]);

    useEffect(() => {
        const notificationData = getNotifications(user);
        setNotifications(notificationData);
        
        const unread = notificationData.filter(n => !n.isRead).length;
        setUnreadCount(unread);
        
        // Notify parent component of the unread count
        if (onNotificationUpdate) {
            onNotificationUpdate(unread);
        }
    }, [user, onNotificationUpdate]);

    const markAsRead = (notificationId) => {
        setNotifications(prev => 
            prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
        );
        const newUnreadCount = Math.max(0, unreadCount - 1);
        setUnreadCount(newUnreadCount);
        
        // Notify parent component of the updated count
        if (onNotificationUpdate) {
            onNotificationUpdate(newUnreadCount);
        }
    };

    const markAllAsRead = () => {
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setUnreadCount(0);
        
        // Notify parent component that all are read
        if (onNotificationUpdate) {
            onNotificationUpdate(0);
        }
    };

    const dismissNotification = (notificationId) => {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        const notification = notifications.find(n => n.id === notificationId);
        if (notification && !notification.isRead) {
            const newUnreadCount = Math.max(0, unreadCount - 1);
            setUnreadCount(newUnreadCount);
            
            // Notify parent component of the updated count
            if (onNotificationUpdate) {
                onNotificationUpdate(newUnreadCount);
            }
        }
    };

    const handleNotificationAction = (notification) => {
        markAsRead(notification.id);
        
        switch (notification.actionUrl) {
            case '#history':
                onOpenDashboard?.();
                break;
            case '#upgrade':
                onOpenDashboard?.('billing');
                onClose();
                break;
            case '#tutorial':
                alert('Tutorial feature coming soon!');
                break;
            case '#guide':
                alert('Quick start guide coming soon!');
                break;
            default:
                console.log('Action:', notification.actionUrl);
        }
    };

    const formatTimeAgo = (timestamp) => {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInMinutes = Math.floor((now - time) / (1000 * 60));
        
        if (diffInMinutes < 60) {
            return `${diffInMinutes}m ago`;
        } else if (diffInMinutes < 1440) {
            return `${Math.floor(diffInMinutes / 60)}h ago`;
        } else {
            return `${Math.floor(diffInMinutes / 1440)}d ago`;
        }
    };

    if (!shouldRender) return null;

    return React.createElement('div', { 
            className: 'notification-dropdown absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-xl overflow-hidden',
            style: { 
                width: '380px',
                maxHeight: '80vh',
                overflowY: 'auto',
                zIndex: 1000,
                // Use the correct animation based on state - no conflict because we control timing
                animation: isOpen ? 'slideDown 200ms ease-out' : 'slideUp 200ms ease-out forwards'
            },
            onClick: (e) => e.stopPropagation() // Prevent closing when clicking inside the panel
        },
        React.createElement('div', { className: 'notification-header border-b border-gray-700 p-4' },
            React.createElement('div', { className: 'mb-3' },
                React.createElement('h2', { className: 'text-lg font-bold text-white' }, 'Updates & News')
            ),
            React.createElement('div', { className: 'flex items-center justify-between' },
                React.createElement('span', { className: 'text-sm text-gray-400' }, 
                    `${notifications.length} notification${notifications.length !== 1 ? 's' : ''}`
                ),
                unreadCount > 0 && React.createElement('button', {
                    onClick: markAllAsRead,
                    className: 'text-purple-400 hover:text-purple-300 font-medium',
                    style: { fontSize: '0.8rem' }
                }, 'Mark all read')
            )
        ),

        React.createElement('div', { className: 'notification-list overflow-y-auto max-h-96' },
            notifications.map(notification => 
                React.createElement('div', {
                    key: notification.id,
                    className: `notification-item border-b border-gray-700 p-4 hover:bg-gray-750 transition-colors ${
                        !notification.isRead ? 'bg-gray-750/50' : ''
                    }`
                },
                    React.createElement('div', { className: 'flex gap-3' },
                        React.createElement('div', { 
                            className: 'notification-icon flex-shrink-0 w-8 h-8 flex items-center justify-center'
                        },
                            React.createElement('span', {
                                className: `iconify ${notification.iconColor} notification-icon-size`,
                                'data-icon': notification.icon,
                                style: { fontSize: '20px' }
                            })
                        ),
                        
                        React.createElement('div', { className: 'flex-1 min-w-0' },
                            React.createElement('div', { className: 'flex items-start justify-between gap-2 mb-1' },
                                React.createElement('h3', { 
                                    className: `font-medium ${!notification.isRead ? 'text-white' : 'text-gray-300'}` 
                                }, notification.title),
                                React.createElement('div', { className: 'flex items-center gap-2 flex-shrink-0' },
                                    !notification.isRead && React.createElement('div', { 
                                        className: 'w-1.5 h-1.5 bg-red-500 rounded-full' 
                                    }),
                                    React.createElement('span', { 
                                        className: 'text-xs text-gray-500' 
                                    }, formatTimeAgo(notification.timestamp))
                                )
                            ),
                            React.createElement('p', { 
                                className: 'text-sm text-gray-400 mb-2' 
                            }, notification.message),
                            notification.actionText && React.createElement('button', {
                                onClick: () => handleNotificationAction(notification),
                                className: 'text-purple-400 hover:text-purple-300 font-medium',
                                style: { fontSize: '0.7rem' }
                            }, notification.actionText)
                        )
                    )
                )
            )
        )
    );
};

export const NotificationBadge = ({ user, onClick, unreadCount }) => {

    return React.createElement('button', {
        onClick: onClick,
        className: 'relative notification-btn p-2 rounded-lg hover:bg-gray-700 transition-colors',
        title: 'Notifications'
    },
        React.createElement('span', {
            className: 'iconify text-gray-400 notification-bell-icon',
            'data-icon': 'solar:bell-linear',
            style: { fontSize: '20px' }
        }),
        unreadCount > 0 && React.createElement('span', {
            className: 'absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1',
            style: { fontSize: '10px', lineHeight: '1' }
        }, unreadCount > 9 ? '9+' : unreadCount)
    );
};
