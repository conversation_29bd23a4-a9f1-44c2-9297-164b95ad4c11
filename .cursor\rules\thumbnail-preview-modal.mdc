---
description: 
globs: 
alwaysApply: false
---
@thumbnail-large-preview-modal
ruleId: thumbnail-large-preview-modal
description: >
  When a user clicks the “Preview” dropdown-item for a thumbnail, open a modal overlay that displays the selected thumbnail at its full 1280x720 resolution (or scaled down responsively if the viewport is smaller). The modal must feature a dark, semi-transparent, blurred backdrop using `backdrop-filter: blur(8px)` and a dark overlay for maximum focus and contrast.

  Inside the modal:
    - Render the thumbnail image centered, at its original size (1280x720) or scaled down to fit the viewport without upscaling.
    - Place a prominent download button (using the Solar Iconfy “download linear” icon) inside the modal, top-right or floating over the image, with a tooltip “Download”.
    - Include a close button (top-right, “Close” text or icon).
    - Ensure the modal is fully keyboard accessible (ESC closes, focus trap).
    - The download button must trigger download of the original image file.
    - Modal and controls must be styled for dark mode and mobile responsiveness.

appliesTo:
  - /src/components/ThumbnailPreview.jsx # Modal implementation and styling
  - /src/components/UserDashboard.jsx # Trigger modal from dropdown-item preview
  - /src/styles/preview.css # Modal, backdrop, and button styles

acceptanceCriteria:
  - Clicking “Preview” opens a modal overlay, not a new tab.
  - Modal displays the thumbnail at 1280x720 or scaled down responsively.
  - Dark, blurred backdrop with `backdrop-filter: blur(8px)` and dark overlay.
  - Download button (Solar Iconfy “download linear” icon) is visible, accessible, and triggers image download.
  - Modal is keyboard accessible and closes on ESC or close button.
  - Modal and controls are styled for dark mode and mobile-friendly.
  - Image is never upscaled; scales down if needed.
  - Tooltip appears on download button hover/focus.

sampleUI: |
  ┌───────────────────────────────────────────────┐
  | (dark blurred backdrop)                       |
  | ┌───────────────────────────────────────────┐ |
  | | [Close]                        [↓]        | |
  | |                                         | |
  | |      [1280x720 thumbnail image]         | |
  | |                                         | |
  | └─────────────────────────────────────────┘ |
  |   [Download button: Solar Iconfy linear]     |
  └───────────────────────────────────────────────┘

notes: |
  - Use a modal portal to ensure overlay is above all content.
  - Use Tailwind/utility classes for dark mode and responsive scaling.
  - Download button should use the Solar Iconfy “download linear” icon.
  - Modal must be accessible and mobile-friendly.