---
description: 
globs: 
alwaysApply: false
---
---
ruleId: thumbnail-modal-export-matching
description: >
  Ensures that the exported thumbnail image and all modal previews (including the default home page modal and the generation history modal) use exactly the same aspect ratio, cropping, and rendering logic as the generation history modal (modal-image-container). The exported image must be a pixel-perfect match to what is shown in the generation history modal—no stretching, squashing, or letterboxing differences. The home page modal preview and the export must both visually match the generation history modal.

appliesTo:
  - /src/components/ui/ThumbnailPreviewModal.jsx
  - /src/components/ThumbnailPreview.jsx
  - /src/components/UserDashboard.jsx
  - /src/utils/imageUtils.js
  - /src/styles/preview.css

acceptanceCriteria:
  - The “generation history” modal (modal-image-container) is the single source of truth for correct aspect ratio and cropping.
  - The exported image, the home page modal preview, and the generation history modal preview are visually identical in composition, framing, and content.
  - No vertical or horizontal stretching, no extra padding, no letterboxing, and no content loss compared to the generation history modal.
  - If the backend returns a non-16:9 image, always crop/resize to match the generation history modal’s logic (center-crop, no letterboxing).
  - All three (export, home modal, generation history modal) use a shared image processing utility for aspect ratio and cropping.
  - Remove or override any conflicting CSS or JS that causes the home page modal or export to differ from the generation history modal.
  - Exported image is at highest available quality (PNG or JPEG 95+), with no extra compression or smoothing.
  - Test by comparing screenshots of all three: home modal, generation history modal, and export—they must be pixel-identical.
notes: |
  - The “generation history” modal’s aspect ratio and cropping logic is the reference standard.
  - All other previews and exports must match this exactly, regardless of device or screen size.
  - Use a single function in imageUtils.js for all cropping/resizing.
  - Remove legacy or conflicting logic in other components.
  - This rule supersedes any previous aspect ratio or export logic for thumbnails.
---
