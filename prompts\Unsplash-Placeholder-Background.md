# 🖼️ Feature Request: Add Unsplash Placeholders for Background Styles in Modal

## Context

In the background style selection modal (`BackgroundTemplateModal.jsx`), each style card currently displays a `.webp` preview from the `/assets/background-templates/` directory. For new or missing backgrounds, it would be helpful to show a **temporary Unsplash placeholder image** instead of a blank or broken image. This will make the UI more visually consistent and help designers easily spot which backgrounds still need custom previews.

## Requirements

- **Component:** `.bgModal-styleInfo` (inside `BackgroundTemplateModal.jsx`)
- **Behavior:**  
  - If a style’s `preview` path is missing, invalid, or the image fails to load, display a relevant Unsplash placeholder image.
  - Use a different Unsplash image for each background category (e.g., Cinematic, Gaming, Tech, etc.) for better context.
  - The Unsplash image should be easy to swap out later by updating the file path or URL in the config.
- **Implementation:**
  - Add a mapping in `backgroundConfig.js` or directly in the modal component that links each category to a default Unsplash image URL.
  - In the style card rendering logic, check if the preview image exists. If not, use the Unsplash placeholder.
  - Optionally, add a subtle overlay or watermark (e.g., “Placeholder”) to indicate it’s not the final asset.

## Example Unsplash URLs

```js
const UNSPLASH_PLACEHOLDERS = {
  cinematic: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80',
  gaming: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?auto=format&fit=crop&w=600&q=80',
  tech: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=600&q=80',
  business: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80',
  abstract: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=600&q=80',
  solid: 'https://images.unsplash.com/photo-1502082553048-f009c37129b9?auto=format&fit=crop&w=600&q=80',
  // ...add more as needed
};
```

## Acceptance Criteria

- [ ] Every style card in the modal always displays an image (either the real preview or a category-appropriate Unsplash placeholder).
- [ ] Placeholders are visually distinct and easy to update.
- [ ] No broken or empty image slots in the modal grid.
- [ ] Code is documented so future devs know how to swap out placeholders.

---

**Why?**  
This will improve the user experience, make the UI more robust, and help with visual QA as new backgrounds are added.

---

> _Feel free to copy-paste this into your project’s issue tracker or documentation!_