---
description: 
globs: 
alwaysApply: true
---
@reverse-thumbnail-ai
ruleId: reverse-thumbnail-ai
description: >
  Adds a new "Reverse Thumbnail AI" feature to the app. This tool allows users to upload a YouTube thumbnail image or paste a YouTube video link. The system analyzes the image and generates a detailed, editable text prompt that captures the style, layout, and visual elements of the target thumbnail. Users can then customize the overlay text and toggle design controls (Include Person, Include Icons, Text Overlay) before generating a new, high-quality thumbnail inspired by their target. All UI and interactions must match the app’s cinematic, dark mode, Hero UI + Tailwind style, and accessibility standards.

appliesTo:
  - /src/components/ReverseThumbnailAI.jsx
  - /src/pages/ReverseThumbnailAI.jsx
  - /src/styles/reverse-thumbnail-ai.css
  - /src/App.jsx # If feature is routed or surfaced in main nav

ruleType: always

implementationNotes: |
  - All UI must use Hero UI + Tailwind classes, dark mode, and premium transitions.
  - Use glassy, blurred cards for input and preview containers.
  - All controls (toggles, buttons, textareas) must match the app’s style guide.
  - Ensure full keyboard and screen reader accessibility.
  - Use clear focus states, proper ARIA roles, and responsive layout.
  - Preview images must use object-cover, rounded-lg, and shadow-lg.
  - Overlay text editor uses the same pyramid/stacked style as the main generator.
  - All modals and popovers use Hero UI modal conventions.

sampleUI: |
  +-------------------------------------------------------------+
  |  [Reverse Thumbnail AI]                                     |
  +-------------------------------------------------------------+
  |  1. Upload Thumbnail Image  [📁]  or  Paste YouTube Link [🔗]|
  |  ---------------------------------------------------------  |
  |  [ Drag & Drop or Browse ]   [ https://youtu.be/xxxxxxx ]   |
  |                                                             |
  |  2. [Analyze] Button                                        |
  |                                                             |
  |  ---------------------------------------------------------  |
  |  [Preview: Target Thumbnail]                                |
  |  +-----------------------------+   +--------------------+   |
  |  |   [Thumbnail Image Here]    |   |  Extracted Prompt  |   |
  |  +-----------------------------+   |  "A smiling person |   |
  |                                    |  holding a phone..."|   |
  |                                    +--------------------+   |
  |                                                             |
  |  3. Edit Prompt:                                            |
  |  [ Textarea: "A smiling person holding a phone..."      ]   |
  |                                                             |
  |  4. Design Controls:                                        |
  |   [x] Include Person   [x] Include Icons   [x] Text Overlay |
  |                                                             |
  |  5. Overlay Text:                                           |
  |  [ "MY NEW VIDEO" ] [Edit]                                  |
  |                                                             |
  |  6. [Generate Thumbnail] Button                             |
  |                                                             |
  |  ---------------------------------------------------------  |
  |  [Generated Thumbnail Preview]                              |
  |  +-----------------------------+                            |
  |  |   [New Thumbnail Image]     |                            |
  |  +-----------------------------+                            |
  |                                                             |
  +-------------------------------------------------------------+

userFlow: |
  1. User navigates to "Reverse Thumbnail AI" from the dashboard or sidebar.
  2. User uploads a thumbnail image or pastes a YouTube video link.
  3. User clicks [Analyze]. The system extracts a prompt and shows a preview.
  4. User edits the generated prompt in a textarea.
  5. User toggles design controls: Include Person, Include Icons, Text Overlay.
  6. User edits overlay text if desired.
  7. User clicks [Generate Thumbnail] to create a new image.
  8. User previews the generated thumbnail and can download or use it.

acceptanceCriteria:
  - Feature is accessible from main navigation.
  - All UI matches app’s dark, cinematic, glassy style.
  - Upload and link input are side-by-side or stacked on mobile.
  - Prompt extraction and editing are seamless and accessible.
  - All design controls are present and match app toggles.
  - Generated thumbnail preview uses the same preview container as main app.
  - All interactions are keyboard and screen reader accessible.
  - No layout shifts or jank; transitions are smooth and premium.