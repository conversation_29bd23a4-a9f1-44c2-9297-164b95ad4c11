---
description: 
globs: 
alwaysApply: false
---
# Subscription Checkout Modal – HeroUI Style

## Description
Implement a new modal page for subscription checkout, triggered when the user clicks “Upgrade to Pro” from the pricing table or “Upgrade Plan” from the Billing & Subscription tab in the user dashboard. The modal must follow the HeroUI design system and be fully responsive.

## Requirements

- **Trigger:**  
  - Open modal when user clicks “Upgrade to Pro” or “Upgrade Plan”.

- **Modal Layout:**  
  - Two-column layout on desktop, stacked on mobile.
  - Left: Billing information and payment method.
  - Right: Plan info, features, user count, billing frequency, promo code, and summary.

- **Billing Information Section:**  
  - Fields: First name, Country (dropdown), State/Region (dropdown), Zip code (optional).
  - Toggle: “Buying as a company?”
  - Payment method: Card, PayPal, Google Pay (radio buttons with icons).

- **Plan Info Section:**  
  - Title: “With [Plan Name] you get”
  - Feature list (with check icons).
  - Number of users (stepper).
  - Billing frequency toggle (Monthly/Annual).
  - Promo code input (expandable).
  - Price summary: subtotal, taxes, total.
  - “Confirm and pay” button (primary, full width).
  - Legal text: Terms, renewal, cancelation info.

- **Design:**  
  - Use HeroUI Modal, Card, Input, Select, Button, and Stepper components.
  - Responsive: 2 columns on desktop, 1 column on mobile.
  - Accessible: Proper labels, focus states, keyboard navigation.
  - Visuals: Clean, modern, high-contrast, with HeroUI spacing and typography.

- **Behavior:**  
  - Modal closes on outside click or close button.
  - “Confirm and pay” triggers payment flow (mock or real).
  - All fields validated (required, format, etc).
  - Show errors inline.

---

## Design Reference (JSON)

```json
{
  "type": "modal",
  "props": {
    "size": "xl",
    "radius": "lg",
    "backdrop": "blur",
    "isOpen": true,
    "onClose": "closeModal"
  },
  "children": [
    {
      "type": "div",
      "props": {
        "className": "flex flex-col md:flex-row gap-8 p-8"
      },
      "children": [
        {
          "type": "card",
          "props": {
            "className": "flex-1 min-w-[320px] bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg"
          },
          "children": [
            { "type": "h2", "props": { "className": "text-xl font-bold mb-4" }, "children": "Billing information" },
            { "type": "input", "props": { "label": "First name", "required": true, "fullWidth": true, "className": "mb-4" } },
            {
              "type": "div",
              "props": { "className": "flex gap-4 mb-4" },
              "children": [
                { "type": "select", "props": { "label": "Country", "required": true, "fullWidth": true } },
                { "type": "select", "props": { "label": "State/Region", "required": true, "fullWidth": true } }
              ]
            },
            { "type": "input", "props": { "label": "Zip code (optional)", "fullWidth": true, "className": "mb-4" } },
            {
              "type": "switch",
              "props": { "label": "Buying as a company?", "className": "mb-6" }
            },
            { "type": "h3", "props": { "className": "text-base font-semibold mb-2" }, "children": "Payment method" },
            {
              "type": "radio-group",
              "props": { "name": "payment", "className": "flex gap-4 mb-2" },
              "children": [
                { "type": "radio", "props": { "value": "card", "icon": "credit-card", "label": "Card" } },
                { "type": "radio", "props": { "value": "paypal", "icon": "paypal", "label": "PayPal" } },
                { "type": "radio", "props": { "value": "gpay", "icon": "google-pay", "label": "G Pay" } }
              ]
            }
          ]
        },
        {
          "type": "card",
          "props": {
            "className": "flex-1 min-w-[320px] bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg"
          },
          "children": [
            { "type": "h2", "props": { "className": "text-xl font-bold mb-4" }, "children": "Plan info" },
            {
              "type": "ul",
              "props": { "className": "mb-6 space-y-2" },
              "children": [
                { "type": "li", "props": { "icon": "check" }, "children": "540,000 AI credits/year..." },
                { "type": "li", "props": { "icon": "check" }, "children": "Priority speed when generating images..." },
                { "type": "li", "props": { "icon": "check" }, "children": "Train custom AI models..." },
                { "type": "li", "props": { "icon": "check" }, "children": "Upscale images up to 10K resolution..." },
                { "type": "li", "props": { "icon": "check" }, "children": "Early access to selected AI features" },
                { "type": "li", "props": { "icon": "check" }, "children": "Buy extra credits with discounts" }
              ]
            },
            {
              "type": "stepper",
              "props": { "label": "Number of users", "min": 1, "max": 100, "value": 1, "className": "mb-4" }
            },
            {
              "type": "toggle-group",
              "props": { "label": "Billing", "className": "mb-4" },
              "children": [
                { "type": "toggle", "props": { "label": "Monthly", "value": "monthly" } },
                { "type": "toggle", "props": { "label": "Annual", "value": "annual", "checked": true } }
              ]
            },
            {
              "type": "input",
              "props": { "label": "Add a promo code", "fullWidth": true, "className": "mb-4", "expandable": true }
            },
            {
              "type": "div",
              "props": { "className": "mb-4" },
              "children": [
                { "type": "div", "props": { "className": "flex justify-between" }, "children": [
                  { "type": "span", "props": { "className": "text-gray-500" }, "children": "Charged today" },
                  { "type": "span", "props": { "className": "font-bold" }, "children": "510.30 CAD" }
                ]},
                { "type": "div", "props": { "className": "flex justify-between" }, "children": [
                  { "type": "span", "props": { "className": "text-gray-400" }, "children": "Taxes included (24.30 CAD)" }
                ]}
              ]
            },
            {
              "type": "button",
              "props": {
                "variant": "primary",
                "fullWidth": true,
                "size": "lg",
                "children": "Confirm and pay"
              }
            },
            {
              "type": "p",
              "props": { "className": "text-xs text-gray-500 mt-4" },
              "children": "By clicking 'Confirm and pay' you agree to our Terms of use. Automatic annual renewal: Your subscription will automatically renew every year. You’ll be charged 510.30 CAD on the renewal date. Cancel: You can cancel your renewal anytime from Subscription > Plan."
            }
          ]
        }
      ]
    }
  ]
}
```
```

---

**You can copy-paste the above into a `.mdc` file for Cursor.**  
Let me know if you want a React code scaffold for this modal as well!